#!/usr/bin/env python
"""
Test script to verify LocalSEO integration with seoanalyser.com.au
Run this script to test the integration: python test_localseo_integration.py
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'seo_analyzer.settings')

def test_django_setup():
    """Test Django setup and configuration"""
    print("🔧 Testing Django setup...")
    try:
        django.setup()
        print("✅ Django setup successful")
        return True
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False

def test_models_import():
    """Test LocalSEO models import"""
    print("📊 Testing models import...")
    try:
        from data_acquisition.models import (
            Business, DataSource, GoogleAccount, 
            GoogleBusinessLocation, AIRecommendation
        )
        print("✅ All LocalSEO models imported successfully")
        return True
    except Exception as e:
        print(f"❌ Models import failed: {e}")
        return False

def test_views_import():
    """Test LocalSEO views import"""
    print("🌐 Testing views import...")
    try:
        from data_acquisition.views.business import business_search, business_detail
        from data_acquisition.views.google_business_profile import gbp_connect
        print("✅ All LocalSEO views imported successfully")
        return True
    except Exception as e:
        print(f"❌ Views import failed: {e}")
        return False

def test_api_clients():
    """Test API clients import"""
    print("🔌 Testing API clients...")
    try:
        from data_acquisition.api_clients.google_places import GooglePlacesClient
        from data_acquisition.api_clients.google_business_profile import GoogleBusinessProfileClient
        from data_acquisition.api_clients.gpt_api import GPTClient
        print("✅ All API clients imported successfully")
        return True
    except Exception as e:
        print(f"❌ API clients import failed: {e}")
        return False

def test_utils():
    """Test utility modules"""
    print("🛠️  Testing utility modules...")
    try:
        from data_acquisition.utils.data_collector import DataCollector
        from data_acquisition.utils.competitive_analysis import CompetitiveAnalyzer
        from data_acquisition.utils.recommendation_engine import RecommendationEngine
        print("✅ All utility modules imported successfully")
        return True
    except Exception as e:
        print(f"❌ Utility modules import failed: {e}")
        return False

def test_urls():
    """Test URL configuration"""
    print("🔗 Testing URL configuration...")
    try:
        from django.urls import reverse
        from django.test import Client
        
        # Test URL reversing
        business_search_url = reverse('data_acquisition:business_search')
        gbp_connect_url = reverse('data_acquisition:gbp_connect')
        
        print(f"✅ Business search URL: {business_search_url}")
        print(f"✅ GBP connect URL: {gbp_connect_url}")
        return True
    except Exception as e:
        print(f"❌ URL configuration failed: {e}")
        return False

def test_database_connection():
    """Test database connection"""
    print("🗄️  Testing database connection...")
    try:
        from data_acquisition.models import Business
        count = Business.objects.count()
        print(f"✅ Database connection successful - {count} businesses in database")
        return True
    except Exception as e:
        print(f"⚠️  Database connection issue: {e}")
        print("   This is expected if migrations haven't been run yet")
        return False

def test_settings():
    """Test LocalSEO settings"""
    print("⚙️  Testing LocalSEO settings...")
    try:
        from django.conf import settings
        
        # Check if data_acquisition is in INSTALLED_APPS
        if 'data_acquisition' in settings.INSTALLED_APPS:
            print("✅ data_acquisition app is in INSTALLED_APPS")
        else:
            print("❌ data_acquisition app not found in INSTALLED_APPS")
            return False
            
        # Check LocalSEO specific settings
        localseo_settings = [
            'GOOGLE_PLACES_API_KEY',
            'GOOGLE_OAUTH_CLIENT_ID', 
            'OPENAI_API_KEY'
        ]
        
        for setting in localseo_settings:
            if hasattr(settings, setting):
                print(f"✅ {setting} configured")
            else:
                print(f"⚠️  {setting} not configured (add to .env file)")
        
        return True
    except Exception as e:
        print(f"❌ Settings test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 LocalSEO Integration Test Suite")
    print("=" * 50)
    
    tests = [
        test_django_setup,
        test_settings,
        test_models_import,
        test_views_import,
        test_api_clients,
        test_utils,
        test_urls,
        test_database_connection,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! LocalSEO integration is working correctly.")
        print("\n📋 Next steps:")
        print("1. Run migrations: python manage.py migrate")
        print("2. Create superuser: python manage.py createsuperuser")
        print("3. Start server: python manage.py runserver")
        print("4. Visit: http://localhost:8000/localseo/")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
