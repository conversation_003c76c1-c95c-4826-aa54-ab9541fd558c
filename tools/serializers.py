from rest_framework import serializers
from .utils import normalize_url

class URLAnalyzeSerializer(serializers.Serializer):
    url = serializers.CharField(required=True)
    
    def validate_url(self, value):
        normalized_url = normalize_url(value)
        if not normalized_url:
            raise serializers.ValidationError("Invalid URL format. Please provide a valid URL.")
        return normalized_url

class TaskStatusSerializer(serializers.Serializer):
    task_id = serializers.CharField(read_only=True)
    status = serializers.CharField(read_only=True)
    result = serializers.J<PERSON><PERSON>ield(read_only=True, required=False)
    message = serializers.Char<PERSON>ield(read_only=True)
    
class SEOScoreSerializer(serializers.Serializer):
    numeric = serializers.IntegerField(read_only=True)
    letter = serializers.Char<PERSON>ield(read_only=True)
    
class SERPPreviewSerializer(serializers.Serializer):
    title = serializers.Char<PERSON>ield(read_only=True)
    description = serializers.Char<PERSON><PERSON>(read_only=True)
