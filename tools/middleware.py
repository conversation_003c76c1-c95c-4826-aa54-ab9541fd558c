import logging
import time
from django.http import HttpResponse, JsonResponse
import json
import redis
from django.conf import settings
from datetime import timedelta
from django.utils import timezone
from accounts.models import Subscription, CustomUser
from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework_simplejwt.exceptions import AuthenticationFailed, InvalidToken

logger = logging.getLogger(__name__)

class RequestLoggingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        start_time = time.time()
        
        logger.info(f"[REQUEST START] {request.method} {request.path}")
        logger.debug(f"Request from {request.META.get('REMOTE_ADDR')}")
        logger.debug(f"Full URL: {request.build_absolute_uri()}")
        logger.debug(f"Query Params: {request.GET}")
        
        response = self.get_response(request)
        
        duration = time.time() - start_time
        logger.info(f"[REQUEST END] {request.method} {request.path} - Status {response.status_code} - Time {duration:.3f}s")
        
        if hasattr(response, 'content') and response.status_code == 404:
            logger.warning(f"404 Not Found for {request.path} - This might indicate a URL routing issue")
        
        return response
    
    def process_exception(self, request, exception):
        logger.exception(f"Exception processing request {request.path}: {str(exception)}")
        return HttpResponse(
            content=json.dumps({"error": str(exception)}),
            status=500,
            content_type="application/json"
        )

class RateLimitMiddleware:
    # Limit: 4 requests per day
    REQUEST_LIMIT = 4
    # Window: 24 hours in seconds
    WINDOW_SECONDS = int(timedelta(days=1).total_seconds())
    ANALYZE_PATH = '/api/analyze/' # Adjust if your path is different, e.g., /tools/analyze/

    def __init__(self, get_response):
        self.get_response = get_response
        try:
            self.redis_client = redis.StrictRedis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                socket_timeout=settings.REDIS_SOCKET_TIMEOUT,
                decode_responses=True  # Important for reading string counts
            )
            self.redis_client.ping() # Check connection
            logger.info("RateLimitMiddleware: Successfully connected to Redis.")
        except AttributeError:
            logger.error("RateLimitMiddleware: Redis settings (REDIS_HOST, REDIS_PORT, REDIS_DB, REDIS_SOCKET_TIMEOUT) not found in Django settings. Rate limiting will be disabled.")
            self.redis_client = None
        except redis.exceptions.ConnectionError as e:
            logger.error(f"RateLimitMiddleware: Could not connect to Redis: {e}. Rate limiting will be disabled.")
            self.redis_client = None

    def get_client_ip(self, request):
        # Cloudflare typically sets 'CF-Connecting-IP'
        cf_ip = request.META.get('HTTP_CF_CONNECTING_IP')
        if cf_ip:
            return cf_ip.split(',')[0].strip() 

        # Standard 'X-Forwarded-For' header, used by many proxies
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            # The first IP in the list is the original client IP
            ip = x_forwarded_for.split(',')[0].strip()
            # logger.debug(f"RateLimitMiddleware: Using X-Forwarded-For: {ip}")
            return ip
        
        # Fallback to REMOTE_ADDR if no proxy headers are present
        remote_addr_ip = request.META.get('REMOTE_ADDR')
        # logger.debug(f"RateLimitMiddleware: Using REMOTE_ADDR: {remote_addr_ip}")
        return remote_addr_ip

    def __call__(self, request):
        # Only apply rate limiting to the specific analyze path
        if request.path_info.rstrip('/') != self.ANALYZE_PATH.rstrip('/'):
            # logger.debug(f"RateLimitMiddleware: Path {request.path_info} does not match {self.ANALYZE_PATH}. Skipping rate limit.")
            return self.get_response(request)

        logger.info(f"RateLimitMiddleware: Processing request for {request.path_info} (ANALYZE_PATH matched).")

        # Attempt JWT authentication if user is anonymous and Authorization header is present
        if hasattr(request, 'user') and not request.user.is_authenticated:
            auth_header = request.META.get('HTTP_AUTHORIZATION')
            if auth_header:
                parts = auth_header.split()
                if len(parts) == 2 and parts[0].lower() == 'bearer':
                    logger.info("RateLimitMiddleware: Found Bearer token, attempting JWT authentication.")
                    jwt_authenticator = JWTAuthentication()
                    try:
                        # authenticate() returns a (user, token) tuple or raises AuthenticationFailed
                        user_token_tuple = jwt_authenticator.authenticate(request)
                        if user_token_tuple:
                            request.user = user_token_tuple[0] # Assign authenticated user to request
                            logger.info(f"RateLimitMiddleware: JWT authentication successful for user {request.user.email}.")
                    except (AuthenticationFailed, InvalidToken) as e:
                        logger.warning(f"RateLimitMiddleware: JWT authentication failed: {str(e)}")
                    except Exception as e: # Catch any other unexpected errors during JWT auth
                        logger.error(f"RateLimitMiddleware: Unexpected error during JWT authentication: {e}", exc_info=True)
                else:
                    logger.debug("RateLimitMiddleware: Authorization header found but not a Bearer token.")
            else:
                logger.debug("RateLimitMiddleware: No Authorization header found for anonymous user.")


        # Check for white label subscription
        if hasattr(request, 'user') and request.user and request.user.is_authenticated:
            logger.info(f"RateLimitMiddleware: Attempting white label check for authenticated user {request.user.email} (ID: {request.user.id}).")
            try:
                # Fetch active subscriptions first
                active_user_subscriptions = Subscription.objects.filter(user=request.user, status='active')
                
                if not active_user_subscriptions.exists():
                    logger.info(f"RateLimitMiddleware: User {request.user.email} has NO active subscriptions (status='active').")
                else:
                    logger.info(f"RateLimitMiddleware: User {request.user.email} has {active_user_subscriptions.count()} active subscription record(s). Checking details:")
                    has_qualifying_white_label_sub = False
                    for sub_record in active_user_subscriptions:
                        plan_name_debug = sub_record.plan.name if sub_record.plan else "No Plan Linked"
                        logger.info(f"  - Evaluating Sub ID: {sub_record.id}, Plan: '{plan_name_debug}', Status: '{sub_record.status}', Period End: {sub_record.current_period_end}")
                        
                        if sub_record.plan and 'white label' in plan_name_debug.lower():
                            # It's a white label plan. Now check if it's considered ongoing.
                            if sub_record.current_period_end is None:
                                logger.info(f"    - White label plan '{plan_name_debug}' has Period End = None. Considered active and ongoing. Bypassing rate limit.")
                                has_qualifying_white_label_sub = True
                                break # Found a qualifying sub
                            elif sub_record.current_period_end >= timezone.now():
                                logger.info(f"    - White label plan '{plan_name_debug}' has Period End >= now. Bypassing rate limit.")
                                has_qualifying_white_label_sub = True
                                break # Found a qualifying sub
                            else:
                                logger.info(f"    - White label plan '{plan_name_debug}' has Period End in the past ({sub_record.current_period_end}). Not bypassing.")
                        else:
                            logger.info(f"    - Sub ID: {sub_record.id} is not a 'white label' plan (Plan: '{plan_name_debug}').")

                    if has_qualifying_white_label_sub:
                        return self.get_response(request) # Bypass for white label
                    else:
                        logger.info(f"RateLimitMiddleware: No qualifying (active and current/ongoing) 'white label' plan found for user {request.user.email}.")

                # If we reach here, no qualifying white label subscription was found for the authenticated user.
                if not 'has_qualifying_white_label_sub' in locals() or not has_qualifying_white_label_sub: # Ensure var exists if no active subs
                     logger.info(f"RateLimitMiddleware: Proceeding with standard rate limiting for authenticated user {request.user.email} as no qualifying white label subscription was confirmed.")

            except Exception as e:
                # Log error but proceed with standard rate limiting if check fails
                logger.error(f"RateLimitMiddleware: Error during white label subscription check for user {request.user.email}: {e}", exc_info=True)
        elif hasattr(request, 'user'):
            logger.info(f"RateLimitMiddleware: White label check skipped. User authenticated: {request.user.is_authenticated} (User: {request.user}). Potential JWT auth failed or no token.")
        else:
            logger.info("RateLimitMiddleware: White label check skipped. request.user not available.")


        if not self.redis_client:
            logger.warning("RateLimitMiddleware: Redis client not available. Skipping rate limit check for {request.path_info}.")
            return self.get_response(request)

        ip_address = self.get_client_ip(request)
        device_id = request.headers.get('X-Device-ID')

        logger.debug(f"RateLimitMiddleware: IP Address: {ip_address}, Device ID: {device_id} for path {request.path_info}")

        if not device_id:
            logger.warning(f"RateLimitMiddleware: X-Device-ID header missing for IP {ip_address}. Request will be rejected for {self.ANALYZE_PATH}.")
            return JsonResponse({
                "error": "Device ID required.",
                "message": "Please include the X-Device-ID header with your request."
            }, status=400) # Bad Request

        # Construct a unique key for Redis. If device_id is present, it's the primary identifier.
        rate_limit_key = f"rate_limit:{device_id}:{self.ANALYZE_PATH}"
        logger.debug(f"RateLimitMiddleware: Generated Redis Key (device-centric): {rate_limit_key}")

        try:
            current_count_raw = self.redis_client.get(rate_limit_key)
            logger.debug(f"RateLimitMiddleware: Raw current_count from Redis for key {rate_limit_key}: {current_count_raw}")
            
            if current_count_raw is None:
                logger.info(f"RateLimitMiddleware: No existing count for key {rate_limit_key}. Setting count to 1.")
                self.redis_client.setex(rate_limit_key, self.WINDOW_SECONDS, 1)
                request_count = 1
            else:
                try:
                    current_count_int = int(current_count_raw)
                except ValueError:
                    logger.error(f"RateLimitMiddleware: Could not convert Redis value '{current_count_raw}' to int for key {rate_limit_key}. Resetting count to 1.")
                    self.redis_client.setex(rate_limit_key, self.WINDOW_SECONDS, 1)
                    request_count = 1
                else:
                    request_count = current_count_int + 1
                    logger.info(f"RateLimitMiddleware: Existing count for key {rate_limit_key} is {current_count_int}. New potential count: {request_count}.")
                    if request_count > self.REQUEST_LIMIT:
                        ttl = self.redis_client.ttl(rate_limit_key)
                        retry_after_seconds = ttl if ttl > 0 else self.WINDOW_SECONDS
                        logger.warning(
                            f"RateLimitMiddleware: Rate limit EXCEEDED for IP {ip_address}, Device ID {device_id}. "
                            f"Count: {request_count}/{self.REQUEST_LIMIT}. Path: {request.path_info}. TTL: {ttl}s"
                        )
                        return JsonResponse({
                            "error": "Too Many Requests",
                            "message": f"You have exceeded the request limit of {self.REQUEST_LIMIT} requests per day for this service.",
                            "retry_after_seconds": retry_after_seconds
                        }, status=429) # Too Many Requests
                    else:
                        # Increment count, retain existing TTL
                        self.redis_client.incr(rate_limit_key)
                        # Ensure expiration is set in case of race conditions or if key expired between get and incr
                        current_ttl = self.redis_client.ttl(rate_limit_key)
                        if current_ttl == -1: # -1 means no expire is set
                             logger.warning(f"RateLimitMiddleware: Key {rate_limit_key} had no TTL after INCR. Setting TTL to {self.WINDOW_SECONDS}s.")
                             self.redis_client.expire(rate_limit_key, self.WINDOW_SECONDS)
                        elif current_ttl == -2: # -2 means key does not exist (should not happen if incr worked)
                             logger.error(f"RateLimitMiddleware: Key {rate_limit_key} does not exist after INCR. This should not happen. Resetting count to 1.")
                             self.redis_client.setex(rate_limit_key, self.WINDOW_SECONDS, 1)
                        # request_count is already set correctly for this branch if we consider it as a new request hitting this state
                        logger.info(f"RateLimitMiddleware: Incremented count for key {rate_limit_key} to {request_count}. Current TTL: {current_ttl if current_ttl not in [-1,-2] else self.WINDOW_SECONDS}s.")


            logger.info(
                f"RateLimitMiddleware: Request ALLOWED for IP {ip_address}, Device ID {device_id}. "
                f"Count: {request_count}/{self.REQUEST_LIMIT}. Path: {request.path_info}"
            )
            response = self.get_response(request)
            return response

        except redis.exceptions.RedisError as e:
            logger.error(f"RateLimitMiddleware: Redis error: {e}. Allowing request to proceed.")
            # Fallback: If Redis fails, allow the request to avoid blocking users due to infrastructure issues.
            # You might want to handle this differently based on your application's requirements.
            return self.get_response(request)
        except Exception as e:
            logger.error(f"RateLimitMiddleware: Unexpected error: {e}. Allowing request to proceed.")
            return self.get_response(request)
