from django.urls import path
from .views import AnalyzeURLView, TaskStatusView, ScreenshotView, ShareAnalysisView

app_name = 'tools'

urlpatterns = [
    path('analyze/', AnalyzeURLView.as_view(), name='analyze'),
    path('status/<str:task_id>/', TaskStatusView.as_view(), name='task-status'),
    path('screenshots/<str:screenshot_id>/', ScreenshotView.as_view(), name='screenshot'),
    path('share/<str:task_id>/', ShareAnalysisView.as_view(), name='share-analysis'),
]
