import re
import logging
import redis
import json
import os
import asyncio
import httpx
from bs4 import BeautifulSoup
from urllib.parse import urlparse, urljoin
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
import time
from typing import Optional
import cssutils

logger = logging.getLogger(__name__)

COMPILED_FONT_SIZE_PATTERN = re.compile(
    r'([^{}]+)\s*\{[^{}]*?font-size:\s*([0-9.]+)\s*(px|pt|em|rem|%|vh|vw)[^{}]*?\}',
    re.DOTALL
)

MAX_USABILITY_SCORES = {
    "device_rendering": 18,
    "viewport_usage": 16,
    "iframes_usage": 6,
    "iframe_protection": 5,
    "flash_usage": 6,
    "favicon_presence": 3,
    "email_privacy": 8,
    "font_legibility": 19,
    "tap_target_sizing": 19,
}

class UsabilityAnalyzer:
    MAX_SCORES = MAX_USABILITY_SCORES

    def __init__(self, url, task_id=None, html=None, device_screenshots=None, technical_data=None, proxy_string: Optional[str] = None):
        self.url = url
        self.task_id = task_id
        self.html = html
        self.soup = None
        self.device_screenshots = device_screenshots
        self.technical_data = technical_data or {}
        self.parsed_url = urlparse(url)
        self.domain = self.parsed_url.netloc
        self.proxy_string = proxy_string
        
        cssutils.ser.prefs.lineNumbers = False
        cssutils.ser.prefs.resolveVariables = False
        cssutils.ser.prefs.keepComments = False
        cssutils.ser.prefs.omitLastSemicolon = True
        cssutils.ser.prefs.normalizedNewline = '\n'
        cssutils_logger = logging.getLogger('cssutils')
        cssutils_logger.setLevel(logging.CRITICAL) 

        try:
            # Attempt to access Django settings for Redis
            redis_host = settings.REDIS_HOST
            redis_port = settings.REDIS_PORT
            redis_db = settings.REDIS_DB
            redis_password = os.getenv('REDIS_PASSWORD', None)

            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                password=redis_password,
                decode_responses=False
            )
            self.redis_client.ping()
            logger.info("Redis connection successful in UsabilityAnalyzer")
            self.redis_available = True
        except (redis.exceptions.ConnectionError, AttributeError, NameError, ImproperlyConfigured) as e:
            # NameError for when 'settings' itself is not defined (less likely with direct import but good for robustness)
            # AttributeError for when settings.REDIS_HOST (etc.) is not defined
            # ImproperlyConfigured for Django's specific configuration error
            if isinstance(e, ImproperlyConfigured) or isinstance(e, AttributeError) or isinstance(e, NameError):
                 logger.warning(f"Django settings for Redis not available or misconfigured in UsabilityAnalyzer: {e}. Redis will be disabled.")
            elif isinstance(e, redis.exceptions.ConnectionError):
                logger.warning(f"Redis connection failed (ConnectionError) in UsabilityAnalyzer: {e}. Redis will be disabled.")
            else:
                # Catch any other unexpected exceptions during Redis setup
                logger.warning(f"An unexpected error occurred during Redis setup in UsabilityAnalyzer: {e}. Redis will be disabled.")
            self.redis_available = False
            self.redis_client = None

        logger.info(f"Initialized UsabilityAnalyzer for {url}")

    def _save_to_redis(self, key, data):
        if self.redis_available and self.task_id:
            try:
                start_time = time.time()
                data_size = len(json.dumps(data))
                self.redis_client.hset(self.task_id, key, json.dumps(data))
                save_time = time.time() - start_time
                logger.debug(f"Redis save for {key} took {save_time:.4f} seconds, data size: {data_size/1024:.2f} KB")
                return True
            except Exception as e:
                logger.error(f"Failed to save to Redis: {e}")
        return False

    async def fetch_content(self):
        if self.html:
            self.soup = BeautifulSoup(self.html, 'lxml')
            return True

        else:
            logger.error(f"No HTML content available for {self.url}")
            return False

    async def analyze_all(self):
        if not self.soup and not self.html:
            return {
                "error": "No HTML content available for analysis",
                "url": self.url
            }

        if not self.soup and self.html:
            try:
                self.soup = BeautifulSoup(self.html, 'lxml')
            except Exception as e:
                 logger.error(f"Failed to parse HTML for {self.url} in analyze_all: {e}")
                 return {"error": f"Failed to parse HTML: {e}", "url": self.url}


        if self.soup:
            device_rendering_res = self.analyze_device_rendering()
            viewport_usage_res = self.analyze_viewport_usage()
            flash_usage_res = self.analyze_flash_usage()
            iframes_usage_res = self.analyze_iframes_usage()
            iframe_protection_res = self.analyze_iframe_protection()
            favicon_presence_res = self.analyze_favicon_presence()
            email_privacy_res = self.analyze_email_privacy()
            tap_target_sizing_res = self.analyze_tap_target_sizing()
        else:
            logger.warning(f"HTML soup not available for sync analyses for {self.url}")
            default_sync_res = {"pass": False, "message": "HTML content not available/parsed", "recommendation": None}
            device_rendering_res = self.analyze_device_rendering()
            viewport_usage_res = default_sync_res.copy()
            flash_usage_res = default_sync_res.copy()
            iframes_usage_res = default_sync_res.copy()
            iframe_protection_res = self.analyze_iframe_protection()
            favicon_presence_res = default_sync_res.copy()
            email_privacy_res = default_sync_res.copy()
            tap_target_sizing_res = default_sync_res.copy()

        logger.info(f"Starting async font legibility analysis for {self.url}")
        try:
            font_legibility_res = await self.analyze_font_legibility()
            logger.info(f"Finished font legibility analysis for {self.url}")
        except Exception as e:
            logger.error(f"Error during font legibility analysis for {self.url}: {e}", exc_info=True)
            font_legibility_res = {
                "pass": False,
                "message": f"Font legibility analysis failed: {e}",
                "score": 0
            }

        results = {
            "device_rendering": device_rendering_res,
            "viewport_usage": viewport_usage_res,
            "flash_usage": flash_usage_res,
            "iframes_usage": iframes_usage_res,
            "iframe_protection": iframe_protection_res,
            "favicon_presence": favicon_presence_res,
            "email_privacy": email_privacy_res,
            "font_legibility": font_legibility_res,
            "tap_target_sizing": tap_target_sizing_res
        }

        return results

    async def run_all(self):
        overall_start_time = time.time()
        if not self.html:
            logger.info(f"Fetching content before running usability analysis for {self.url}")
            fetch_start_time = time.time()
            success = await self.fetch_content()
            fetch_end_time = time.time()
            logger.info(f"Content fetching for {self.url} took {fetch_end_time - fetch_start_time:.2f}s. Success: {success}")
            if not success:
                logger.warning(f"Failed to fetch content for {self.url}, usability analysis may be incomplete.")
                if not self.html:
                    return {
                        "error": "Failed to fetch HTML content for analysis",
                        "url": self.url,
                        "timing": {"total_run_all_time": time.time() - overall_start_time}
                    }

        content_fetch_parse_end_time = time.time() # Mark end of fetch/initial availability

        if self.html and not self.soup:
             parse_start_time = time.time()
             try:
                 self.soup = BeautifulSoup(self.html, 'lxml')
                 logger.info(f"Parsed HTML for {self.url} in run_all. Took {time.time() - parse_start_time:.2f}s")
             except Exception as e:
                 logger.error(f"Failed to parse HTML for {self.url} in run_all: {e}")
                 self.soup = None
             content_fetch_parse_end_time = time.time() # Update if parsing happened


        logger.info(f"Running all usability analyses for {self.url}")

        if not self.soup:
             logger.error(f"Cannot perform usability analysis for {self.url} because HTML could not be parsed.")
             return {
                 "error": "HTML Parsing Failed",
                 "url": self.url,
                 "timing": {"total_run_all_time": time.time() - overall_start_time}
            }

        sync_analyses_start_time = time.time()
        logger.debug(f"Running synchronous analyses for {self.url}")
        device_rendering_res = self.analyze_device_rendering()
        viewport_usage_res = self.analyze_viewport_usage()
        flash_usage_res = self.analyze_flash_usage()
        iframes_usage_res = self.analyze_iframes_usage()
        iframe_protection_res = self.analyze_iframe_protection()
        favicon_presence_res = self.analyze_favicon_presence()
        email_privacy_res = self.analyze_email_privacy()
        tap_target_sizing_res = self.analyze_tap_target_sizing()
        sync_analyses_end_time = time.time()
        logger.debug(f"Finished synchronous analyses for {self.url}. Took {sync_analyses_end_time - sync_analyses_start_time:.2f}s")

        logger.info(f"Starting async font legibility analysis for {self.url}")
        font_legibility_start_time = time.time()
        try:
            font_legibility_res = await self.analyze_font_legibility()
            font_legibility_analysis_duration = time.time() - font_legibility_start_time
            logger.info(f"Finished async font legibility analysis for {self.url}. Took {font_legibility_analysis_duration:.2f}s")
        except Exception as e:
            font_legibility_analysis_duration = time.time() - font_legibility_start_time
            logger.error(f"Error during font legibility analysis for {self.url}: {e}", exc_info=True)
            font_legibility_res = {
                "pass": False,
                "message": f"Font legibility analysis failed: {e}",
                "score": 0
            }

        results = {
            "device_rendering": device_rendering_res,
            "viewport_usage": viewport_usage_res,
            "flash_usage": flash_usage_res,
            "iframes_usage": iframes_usage_res,
            "iframe_protection": iframe_protection_res,
            "favicon_presence": favicon_presence_res,
            "email_privacy": email_privacy_res,
            "font_legibility": font_legibility_res,
            "tap_target_sizing": tap_target_sizing_res
        }

        total_score = 0
        total_possible = sum(self.MAX_SCORES.values())

        for key, metric_data in results.items():
            if key in self.MAX_SCORES and isinstance(metric_data, dict):
                total_score += metric_data.get('score', 0)
            elif key == 'error':
                 logger.warning(f"Analysis for {self.url} returned an error: {results.get('error')}")
                 pass

        percentage = round((total_score / total_possible) * 100) if total_possible > 0 else 0

        if percentage >= 98:
            grade = 'A+'
        elif percentage >= 95:
            grade = 'A'
        elif percentage >= 90:
            grade = 'A-'
        elif percentage >= 85:
            grade = 'B+'
        elif percentage >= 80:
            grade = 'B'
        elif percentage >= 75:
            grade = 'B-'
        elif percentage >= 70:
            grade = 'C+'
        elif percentage >= 60:
            grade = 'C'
        elif percentage >= 50:
            grade = 'C-'
        elif percentage >= 40:
            grade = 'D'
        else: 
            grade = 'F'

        results['total_score'] = {
            'score': total_score,
            'grade': grade
        }

        if grade in ['A+', 'A', 'A-']:
            overall_title = "Your Usability is Excellent"
            overall_description = "Your website demonstrates excellent usability across devices and accessibility standards. Only minor improvements may be needed."
        elif grade in ['B+', 'B', 'B-']:
            overall_title = "Your Usability is Good"
            overall_description = "Your website has good usability, but there are some areas that could be improved for an even better user experience."
        elif grade in ['C+', 'C', 'C-']:
            overall_title = "Your Usability Could Be Better"
            overall_description = "Your website has some usability optimisations, but there are several areas that need attention to improve user experience."
        elif grade == 'D':
            overall_title = "Your Usability Needs Improvement"
            overall_description = "Your website has significant usability issues. Address the key recommendations to improve accessibility and user satisfaction."
        else:
            overall_title = "Your Usability is Poor"
            overall_description = "Your website is missing critical usability elements. Immediate action is required to improve user experience."

        results['overall_title'] = overall_title
        results['overall_description'] = overall_description

        # Add timing information
        total_run_all_time = time.time() - overall_start_time
        results['timing'] = {
            'total_run_all_time': total_run_all_time,
            'content_fetch_and_parse_time': content_fetch_parse_end_time - overall_start_time,
            'synchronous_analyses_time': sync_analyses_end_time - sync_analyses_start_time,
            'font_legibility_analysis_time': font_legibility_analysis_duration
            # 'font_legibility_internal_timing': font_legibility_res.get('timing') if isinstance(font_legibility_res, dict) else None
        }

        logger.info(f"Usability analysis score for {self.url}: {total_score}/{total_possible} ({percentage}%, Grade: {grade})")
        logger.info(f"Total time for run_all for {self.url}: {total_run_all_time:.2f}s")
        logger.info(f"Content fetch and parse took: {results['timing']['content_fetch_and_parse_time']:.2f}s")
        logger.info(f"Synchronous analyses took: {results['timing']['synchronous_analyses_time']:.2f}s")
        logger.info(f"Font legibility analysis took: {results['timing']['font_legibility_analysis_time']:.2f}s")


        return results

    def analyze_device_rendering(self, base_url="http://seoanalyser.com.au"):
        expected_devices = ['desktop', 'tablet', 'mobile']
        available_devices = [device for device in expected_devices if device in self.device_screenshots]

        screenshot_urls = {}
        for device, key in self.device_screenshots.items():
            if key:
                screenshot_urls[device] = f"http://seoanalyser.com.au/api/screenshots/{key}/"

        all_devices_available = len(available_devices) == len(expected_devices)

        description = (
            "Device rendering checks if your website displays correctly across different devices: "
            "desktop, tablet, and mobile. This test captures screenshots of your site on each device type "
            "to verify proper rendering."
        )

        importance = (
            "Properly rendered content across all device types is essential for user experience and SEO. "
            "Search engines like Google use mobile-first indexing, making mobile rendering particularly important."
        )

        recommendation_text = ""
        priority = "Low"
        status = True

        if all_devices_available:
            recommendation_text = "Your website appears to render correctly across all tested devices."
        elif len(available_devices) == 0:
            recommendation_text = "No device screenshots were available for analysis. This could indicate rendering problems or processing issues."
            status = False
            priority = "High"
        else:
            missing_devices = [d for d in expected_devices if d not in available_devices]
            recommendation_text = f"Screenshots were not available for: {', '.join(missing_devices)}. Ensure your website renders properly on these devices."
            status = False
            priority = "Medium"

        result = {
            "pass": status,
            "screenshot_urls": screenshot_urls,
            "available_devices": available_devices,
            "expected_devices": expected_devices,
            "description": description,
            "importance": importance,
            "recommendation": None if status else {"text": recommendation_text, "priority": priority},
            "blog": ""
        }

        max_score = self.MAX_SCORES.get('device_rendering', 18)

        if len(available_devices) == 0:
            score = 0
        else:
            score_per_device = max_score / len(expected_devices)
            score = int(len(available_devices) * score_per_device)

            if all_devices_available:
                score = max_score

        result['score'] = score

        self._save_to_redis('device_rendering', result)
        return result

    def analyze_viewport_usage(self):
        viewport_meta = self.soup.find('meta', attrs={'name': 'viewport'})
        has_viewport = viewport_meta is not None

        content = viewport_meta.get('content', '') if has_viewport else ''
        content_lower = content.lower()

        has_width = 'width=device-width' in content_lower
        has_initial_scale = 'initial-scale=1' in content_lower or 'initial-scale=1.0' in content_lower
        is_responsive = has_width and has_initial_scale
        priority = "Low"
        recommendation_text = ""
        status_pass = has_viewport and is_responsive

        if not status_pass:
            if not has_viewport:
                recommendation_text = (
                    "Add a viewport meta tag in the <head> section: "
                    "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">. "
                    "This ensures your website scales correctly on all devices."
                )
                priority = "High"
            elif not has_width and not has_initial_scale:
                recommendation_text = (
                    "Update your viewport meta tag to include both 'width=device-width' and 'initial-scale=1' "
                    "for optimal responsive design."
                )
                priority = "High"
            elif not has_width:
                recommendation_text = (
                    "Include 'width=device-width' in your viewport meta tag to match the screen's width "
                    "and improve responsiveness."
                )
                priority = "High"
            elif not has_initial_scale:
                recommendation_text = (
                    "Add 'initial-scale=1' to your viewport meta tag to set the initial zoom level "
                    "and enhance user experience."
                )
                priority = "Medium"
        else:
            recommendation_text = None

        result = {
            "pass": status_pass,
            "has_viewport_meta": has_viewport,
            "viewport_content": content,
            "is_responsive": is_responsive,
            "description": (
                "The viewport meta tag instructs browsers on how to control the page's dimensions and scaling. "
                "It's essential for responsive web design, ensuring that web pages render well on a variety of devices."
            ),
            "importance": (
                "Proper configuration of the viewport meta tag is crucial for mobile usability and accessibility. "
                "It allows content to adapt to different screen sizes, improving readability and user experience. "
                "Additionally, search engines like Google consider mobile-friendliness as a ranking factor."
            ),
            "recommendation": None if status_pass else {"text": recommendation_text, "priority": priority},
            "blog": ""
        }

        max_score = self.MAX_SCORES.get('viewport_usage', 16)
        score = 0
        if has_viewport:
            if is_responsive:
                score = max_score
            elif has_width or has_initial_scale:
                score = max_score // 2

        result['score'] = score

        self._save_to_redis('viewport_usage', result)
        return result

    def analyze_flash_usage(self):
        flash_objects = self.soup.find_all(['object', 'embed'], attrs={
            'type': re.compile(r'application/x-shockwave-flash|flash', re.IGNORECASE)
        })

        flash_embeds = self.soup.find_all(['embed', 'object'], attrs={
            'src': re.compile(r'\.swf', re.IGNORECASE)
        })

        all_flash_elements = list(set(flash_objects) | set(flash_embeds))
        has_flash = len(all_flash_elements) > 0
        priority = "Low"
        recommendation_text = "No Flash content detected."
        status_pass = not has_flash

        if not status_pass:
            recommendation_text = (
                "Remove all Flash content from your website. "
                "Replace it with modern, secure technologies such as HTML5, CSS3, and JavaScript."
            )
            priority = "High"
        else:
            recommendation_text = None


        result = {
            "pass": status_pass,
            "count": len(all_flash_elements),
            "elements": [str(el)[:100] + '...' if len(str(el)) > 100 else str(el)
                         for el in all_flash_elements[:5]],
            "description": (
                "Adobe Flash Player reached its end-of-life on December 31, 2020. "
                "As of January 12, 2021, Adobe has blocked Flash content from running in Flash Player, "
                "and major browsers have disabled Flash Player from running after the EOL date."
            ),
            "importance": (
                "Continuing to use Flash content poses significant security risks, "
                "as it is no longer supported or updated. "
                "Modern web standards like HTML5 provide secure and efficient alternatives."
            ),
            "recommendation": None if status_pass else {"text": recommendation_text, "priority": priority},
            "blog": ""
        }

        max_score = self.MAX_SCORES.get('flash_usage', 6)
        
        score = max_score if not has_flash else 0
        result['score'] = score

        self._save_to_redis('flash_usage', result)
        return result


    def analyze_iframes_usage(self):
        iframes = self.soup.find_all('iframe')
        iframe_srcs = [iframe.get('src', '') for iframe in iframes if iframe.get('src')]
        has_iframes = len(iframes) > 0
        priority = "Low"
        recommendation_text = "No iframes detected."
        status_pass = not has_iframes

        if not status_pass:
            recommendation_text = (
                "Review all iframe usages to ensure they are necessary and sourced from trusted domains. "
                "Implement security measures such as the 'sandbox' attribute, 'allow' attribute, and appropriate 'referrerpolicy'. "
                "Additionally, configure HTTP headers like 'Content-Security-Policy: frame-ancestors' to control iframe behavior."
            )
            priority = "Medium"
        else:
            recommendation_text = None


        result = {
            "pass": status_pass,
            "count": len(iframes),
            "iframe_sources": iframe_srcs[:10],
            "description": (
                "The <iframe> element allows embedding of external content into a webpage. "
                "However, improper use can lead to security vulnerabilities such as clickjacking and cross-site scripting (XSS)."
            ),
            "importance": (
                "Ensuring that iframes are used securely is crucial to protect users from potential attacks. "
                "Proper configuration helps maintain the integrity and security of your website."
            ),
            "recommendation": None if status_pass else {"text": recommendation_text, "priority": priority},
            "blog": ""
        }

        max_score = self.MAX_SCORES.get('iframes_usage', 6)
        count = result['count']
        score = max_score if count == 0 else 0
        result['score'] = score

        self._save_to_redis('iframes_usage', result)
        return result


    def analyze_iframe_protection(self):
        x_frame_options = self.technical_data.get('x_frame_options', '')
        csp = self.technical_data.get('content_security_policy', '')

        has_x_frame = bool(x_frame_options and x_frame_options.lower() in ['deny', 'sameorigin'])
        has_frame_ancestors = 'frame-ancestors' in csp.lower() if csp else False

        is_protected = has_frame_ancestors
        priority = "Low"
        recommendation_text = "Your 'Content-Security-Policy' header effectively controls framing, preventing clickjacking."

        if not is_protected:
            if has_x_frame:
                 recommendation_text = (
                     "You are using the older 'X-Frame-Options' header. "
                     "While better than nothing, consider upgrading to 'Content-Security-Policy: frame-ancestors' for better browser support and flexibility."
                 )
                 priority = "Medium"
            else:
                 recommendation_text = (
                     "Implement the 'Content-Security-Policy' header with the 'frame-ancestors' directive "
                     "to control which origins can embed your content and prevent clickjacking. For example:\n"
                     "Content-Security-Policy: frame-ancestors 'none';"
                 )
                 priority = "High"
        else:
            recommendation_text = None


        result = {
            "pass": is_protected,
            "has_x_frame_options": has_x_frame,
            "x_frame_options_value": x_frame_options,
            "has_csp_frame_protection": has_frame_ancestors,
            "description": (
                "The 'Content-Security-Policy' header with the 'frame-ancestors' directive specifies "
                "which origins are permitted to embed the page using frames. This is essential for "
                "preventing clickjacking attacks."
            ),
            "importance": (
                "Using 'frame-ancestors' in CSP provides fine-grained control over framing policies and "
                "is supported by modern browsers. It supersedes the older 'X-Frame-Options' header."
            ),
            "recommendation": {"text": recommendation_text, "priority": priority} if recommendation_text else None,
            "blog": ""
        }

        max_score = self.MAX_SCORES.get('iframe_protection', 5)
        score = 0
        if has_frame_ancestors:
            score = max_score
        elif has_x_frame:
            score = max_score * 3 // 5

        result['score'] = score

        self._save_to_redis('iframe_protection', result)
        return result


    def analyze_favicon_presence(self):
        favicon_links = self.soup.find_all('link', attrs={
            'rel': re.compile(r'icon|shortcut icon', re.IGNORECASE)
        })

        apple_touch_icons = self.soup.find_all('link', attrs={
            'rel': re.compile(r'apple-touch-icon', re.IGNORECASE)
        })

        favicon_paths = [link.get('href', '') for link in favicon_links if link.get('href')]
        apple_icon_paths = [link.get('href', '') for link in apple_touch_icons if link.get('href')]

        has_favicon = len(favicon_links) > 0
        has_apple_touch_icon = len(apple_touch_icons) > 0
        priority = "Low"
        recommendation_text = None
        status_pass = has_favicon

        if not status_pass:
            recommendation_text = (
                "Add a standard favicon (`rel=\"icon\"`) to your website for browser tabs and bookmarks. "
                "Ensure it's at least 48x48 pixels."
            )
            priority = "Low"

        result = {
            "pass": status_pass,
            "has_favicon": has_favicon,
            "has_apple_touch_icon": has_apple_touch_icon,
            "favicon_paths": favicon_paths,
            "apple_touch_icon_paths": apple_icon_paths,
            "description": (
                "Favicons are small icons that represent your website in browser tabs, bookmarks, and "
                "search results. They contribute to brand identity and user trust."
            ),
            "importance": (
                "A well-designed favicon enhances your website's professionalism and can improve visibility "
                "in search engine results."
            ),
            "recommendation": {"text": recommendation_text, "priority": priority} if recommendation_text else None,
            "blog": ""
        }

        max_score = self.MAX_SCORES.get('favicon_presence', 3)
        score = 0
        if has_favicon and has_apple_touch_icon:
            score = max_score
        elif has_favicon:
            score = max_score * 2 // 3
        elif has_apple_touch_icon: 
            score = max_score * 1 // 3

        result['score'] = score

        self._save_to_redis('favicon_presence', result)
        return result


    def analyze_email_privacy(self):
        email_regex = r'(?<!\w)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?!\w)'
        exposed_emails = set()
        try:
            for text_node in self.soup.find_all(string=True):
                if text_node.parent.name in ['script', 'style']:
                    continue
                found = re.findall(email_regex, str(text_node))
                if found:
                    exposed_emails.update(found)
        except Exception as e:
            logger.warning(f"Error searching text nodes for emails in {self.url}: {e}")

        try:
            mailto_links = self.soup.find_all('a', href=re.compile(r'^mailto:', re.IGNORECASE))
            for link in mailto_links:
                href = link.get('href', '')
                email_part = href.split(':', 1)[-1].split('?')[0]
                if re.fullmatch(email_regex, email_part):
                    exposed_emails.add(email_part)
                else:
                    match = re.search(email_regex, email_part)
                    if match:
                        exposed_emails.add(match.group(0))
        except Exception as e:
            logger.warning(f"Error searching mailto links for emails in {self.url}: {e}")

        try:
            script_tags = self.soup.find_all('script')
            for script in script_tags:
                if script.string:
                    found = re.findall(email_regex, script.string)
                    if found:
                        exposed_emails.update(found)
        except Exception as e:
            logger.warning(f"Error searching script tags for emails in {self.url}: {e}")

        reported_emails = list(exposed_emails)[:10]
        is_vulnerable = len(exposed_emails) > 0
        priority = "Low"
        recommendation_text = "No plain text email addresses were detected. If you use emails, ensure they are protected."
        status_pass = not is_vulnerable

        if not status_pass:
            recommendation_text = (
                "Plain text email addresses were found on the page. These can be harvested by spam bots. "
                "Consider replacing them with a contact form or using obfuscation techniques "
                "(e.g., JavaScript encoding, HTML entity encoding) to protect them."
            )
            priority = "Medium"
        else:
            recommendation_text = None


        result = {
            "pass": status_pass,
            "exposed_email_count": len(exposed_emails),
            "exposed_emails_sample": reported_emails,
            "description": (
                "Detects plain text email addresses within the page's HTML content (text, mailto links, scripts). "
                "Exposed emails are easily collected by automated bots for spamming purposes."
            ),
            "importance": (
                "Protecting email addresses from harvesting helps reduce spam received by those addresses "
                "and enhances user privacy and security."
            ),
            "recommendation": {"text": recommendation_text, "priority": priority} if not status_pass else None,
            "blog": ""
        }

        max_score = self.MAX_SCORES.get('email_privacy', 8)
        score = max_score if result['pass'] else 0
        result['score'] = score

        self._save_to_redis('email_privacy', result)
        return result


    async def _fetch_external_css(self, css_url):
        proxy_url_formatted = None
        try:
            if not css_url.startswith(('http://', 'https://')):
                original_url = css_url
                css_url = urljoin(self.url, css_url)
                logger.debug(f"Resolved relative CSS URL '{original_url}' to '{css_url}'")

            logger.info(f"Fetching external CSS from: {css_url}")

            if self.proxy_string:
                match = re.match(r'^([^:]+):(\d+):([^:]+):(.+)$', self.proxy_string)
                if match:
                    host, port, user, password = match.groups()
                    proxy_url_formatted = f"http://{user}:{password}@{host}:{port}"
                    logger.info(f"_fetch_external_css using proxy: {host}:{port}")
                else:
                    logger.warning(f"Invalid proxy string format in UsabilityAnalyzer: {self.proxy_string}. Proceeding without proxy for CSS fetch.")
            else:
                 logger.debug("_fetch_external_css running without proxy")

            async with httpx.AsyncClient(timeout=30.0, follow_redirects=True, proxy=proxy_url_formatted) as client:
                response = await client.get(css_url)
                if response.status_code == 200:
                    logger.info(f"Successfully fetched CSS from {css_url} (Size: {len(response.content)/1024:.2f} KB)")
                    try:
                        return response.content.decode('utf-8', errors='replace')
                    except UnicodeDecodeError:
                        logger.warning(f"Unicode decode error for {css_url}, trying latin-1")
                        return response.content.decode('latin-1', errors='replace')
                else:
                    logger.warning(f"Failed to fetch CSS from {css_url}: Status {response.status_code}")
                    return ""
        except httpx.TimeoutException:
             logger.warning(f"Timeout fetching CSS from {css_url}")
             return ""
        except httpx.RequestError as e:
            logger.error(f"Request error fetching CSS from {css_url}: {e}")
            return ""
        except Exception as e:
            logger.error(f"Generic error fetching CSS from {css_url}: {e}", exc_info=True)
            return ""

    def _analyze_css_for_small_fonts(self, css_content, source="internal"):
        issues = []
        declarations_processed_count = 0
        # Reduced verbosity for this log, DEBUG is fine for normal use.
        logger.debug(f"Analyzing CSS from {source} using cssutils. Content length: {len(css_content)}")

        try:
            parser = cssutils.CSSParser(loglevel=logging.CRITICAL) # Ensure parser itself is quiet
            sheet = parser.parseString(css_content)
            
            if not sheet and css_content.strip():
                logger.warning(f"cssutils parsing failed for {source}, content was not empty. Length: {len(css_content)}")
                return issues, 0
            elif not sheet:
                logger.debug(f"cssutils parsed empty or invalid sheet for {source}. Length: {len(css_content)}")
                return issues, 0

            for rule in sheet:
                if rule.type == rule.STYLE_RULE:
                    for prop in rule.style:
                        declarations_processed_count += 1
                        if prop.name.lower() == 'font-size':
                            value_text = prop.value
                            match = re.match(r'^([0-9.]+)\s*?(px|pt|em|rem|%|vh|vw)$', value_text.strip(), re.IGNORECASE)
                            if match:
                                size_str = match.group(1)
                                unit = match.group(2).lower()
                                selector_text = rule.selectorText
                                try:
                                    size = float(size_str)
                                    is_small = False
                                    if unit == 'px' and size < 12:
                                        is_small = True
                                    elif unit == 'pt' and size < 9:  # 9pt ~ 12px
                                        is_small = True
                                    elif unit == 'em' and size < 0.75: # 0.75em * 16px base = 12px
                                        is_small = True
                                    elif unit == 'rem' and size < 0.75: # 0.75rem * 16px base = 12px
                                        is_small = True
                                    elif unit == '%' and size < 75: # 75% * 16px base = 12px
                                        is_small = True
                                    elif unit in ['vh', 'vw'] and size < 3.75: # 3.75vw on 320px viewport = 12px
                                        is_small = True

                                    if is_small:
                                        is_heading = any(h in selector_text.lower() for h in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
                                        context = "heading" if is_heading else "text"
                                        # Critically small: < 10px
                                        if (unit == 'px' and size < 10) or \
                                           (unit == 'pt' and size < 7.5) or \
                                           (unit in ['em', 'rem'] and size < 0.625) or \
                                           (unit == '%' and size < 62.5) or \
                                           (unit in ['vh', 'vw'] and size < 3.125): # 3.125vw on 320px viewport = 10px
                                           severity = "High"
                                        else:
                                           severity = "Medium"
                                        cleaned_selector = ' '.join(selector_text.strip().split())
                                        # This log is useful for debugging specific small font issues
                                        logger.debug(f"Small font found in {source}: Selector='{cleaned_selector}', Size='{size}{unit}', Severity={severity}")
                                        issues.append({
                                            'selector': cleaned_selector,
                                            'size': f"{size}{unit}",
                                            'context': context,
                                            'severity': severity,
                                            'source': source
                                        })
                                except ValueError:
                                    logger.warning(f"Could not parse font size from '{size_str}' in {source} for selector '{selector_text}'")
                                    continue
                                except Exception as e:
                                    logger.error(f"Error processing declaration in {source} (Selector: {selector_text}, Value: {value_text}): {e}", exc_info=True)
                                    continue
            logger.debug(f"Processed {declarations_processed_count} CSS declarations in {source} using cssutils.")
        except UnicodeDecodeError as ude:
            logger.error(f"UnicodeDecodeError parsing CSS from {source} with cssutils: {ude}")
            return issues, 0
        except Exception as e:
            logger.error(f"Error analyzing CSS from {source} with cssutils: {e}", exc_info=True)
            return issues, 0
        return issues, declarations_processed_count

    async def analyze_font_legibility(self):
        logger.info(f"Starting font legibility analysis for {self.url}")
        problematic_elements = []
        css_issues = []
        total_analyzed_in_css = 0
        external_css_analyzed_count = 0
        external_css_fetch_errors = 0

        if not self.soup:
            logger.error("HTML soup not available for font legibility analysis.")
            return {"pass": False, "message": "HTML not parsed", "score": 0}

        start_time = time.time()

        # Inline styles analysis (logging refined)
        inline_start = time.time()
        font_size_pattern_inline = r'font-size:\s*([0-9.]+)\s*(px|pt|em|rem|%|vh|vw)' # Specific for inline
        inline_style_elements = []
        try:
            inline_style_elements = self.soup.find_all(style=re.compile(font_size_pattern_inline, re.IGNORECASE))
            logger.debug(f"Found {len(inline_style_elements)} elements with inline font-size style.")
        except Exception as e:
             logger.error(f"Error finding elements with inline style: {e}", exc_info=True)

        for element in inline_style_elements:
            style = element.get('style', '')
            match = re.search(font_size_pattern_inline, style, re.IGNORECASE)
            if match:
                try:
                    size = float(match.group(1))
                    unit = match.group(2).lower()
                    is_small = False
                    severity = "Low"
                    if unit == 'px' and size < 12:
                        is_small = True
                        severity = "High" if size < 10 else "Medium"
                    elif unit == 'pt' and size < 9:
                        is_small = True
                        severity = "High" if size < 7.5 else "Medium"
                    elif unit == 'em' and size < 0.75:
                        is_small = True
                        severity = "High" if size < 0.625 else "Medium"
                    elif unit == 'rem' and size < 0.75:
                        is_small = True
                        severity = "High" if size < 0.625 else "Medium"
                    elif unit == '%' and size < 75:
                        is_small = True
                        severity = "High" if size < 62.5 else "Medium"
                    elif unit in ['vh', 'vw'] and size < 3.75:
                        is_small = True
                        severity = "High" if size < 3.125 else "Medium"

                    if is_small:
                        element_type = element.name
                        text_content = element.get_text().strip()
                        if not text_content or len(text_content) < 3:
                            logger.debug(f"Skipping small inline font element ('{element_type}') due to short/empty text.")
                            continue
                        context = "heading" if element_type in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] else "text"
                        logger.debug(f"Small inline font found: Element='{element_type}', Size='{size}{unit}', Severity={severity}")
                        problematic_elements.append({
                            'element': element_type,
                            'style': style,
                            'text': text_content[:50] + ('...' if len(text_content) > 50 else ''),
                            'size': f"{size}{unit}",
                            'context': context,
                            'severity': severity 
                        })
                except ValueError:
                    logger.warning(f"Could not parse inline font size from style: '{style}'")
                    continue
                except Exception as e:
                    logger.error(f"Error processing inline style '{style}': {e}", exc_info=True)
                    continue
        inline_time = time.time() - inline_start
        logger.info(f"Inline styles analysis completed in {inline_time:.2f}s. Found {len(problematic_elements)} issues.")

        # Internal <style> tags analysis (logging refined)
        internal_start = time.time()
        style_tags = self.soup.find_all('style')
        logger.debug(f"Found {len(style_tags)} internal <style> tags.")
        for i, style_tag in enumerate(style_tags):
            css_content = style_tag.string
            if css_content:
                tag_id_for_log = f"internal <style> #{i+1}"
                # Reduced verbosity for this INFO log
                logger.debug(f"Analyzing {tag_id_for_log} - Size: {len(css_content)} characters") 
                analysis_start_time_tag = time.time()
                issues_from_tag, declarations_in_tag = self._analyze_css_for_small_fonts(css_content, tag_id_for_log)
                analysis_duration_tag = time.time() - analysis_start_time_tag
                # This log is more for detailed debugging now
                logger.debug(f"Finished analyzing {tag_id_for_log}. Duration: {analysis_duration_tag:.2f}s. Issues: {len(issues_from_tag)}, Decls: {declarations_in_tag}")
                css_issues.extend(issues_from_tag)
                total_analyzed_in_css += declarations_in_tag
            else:
                 logger.debug(f"Skipping empty internal style tag #{i+1}")
        internal_time = time.time() - internal_start
        logger.info(f"Internal styles analysis completed in {internal_time:.2f}s.") # Removed issue count for brevity

        # External CSS files analysis (logging refined)
        external_start = time.time()
        external_css_links = self.soup.find_all('link', rel='stylesheet')
        has_external_css = len(external_css_links) > 0
        logger.info(f"Found {len(external_css_links)} external CSS links.")

        MAX_EXTERNAL_CSS_TO_FETCH = self.technical_data.get('max_css_files', 5)
        CSS_FETCH_TIMEOUT = self.technical_data.get('css_fetch_timeout', 60)
        logger.info(f"Fetching and analyzing up to {MAX_EXTERNAL_CSS_TO_FETCH} external CSS files with {CSS_FETCH_TIMEOUT}s timeout.")

        fetch_tasks = []
        urls_to_fetch = []
        for link in external_css_links[:MAX_EXTERNAL_CSS_TO_FETCH]:
            css_url = link.get('href')
            if css_url:
                urls_to_fetch.append(css_url)
                fetch_tasks.append(self._fetch_external_css(css_url))
            else:
                logger.warning("Found <link rel='stylesheet'> without href attribute.")

        if fetch_tasks:
            try:
                fetched_css_contents = await asyncio.wait_for(
                    asyncio.gather(*fetch_tasks, return_exceptions=True),
                    timeout=CSS_FETCH_TIMEOUT
                )
                processed_contents = []
                for i_fetch, fetch_result in enumerate(fetched_css_contents):
                    if isinstance(fetch_result, Exception):
                        logger.error(f"Error fetching CSS {urls_to_fetch[i_fetch]}: {fetch_result}")
                        external_css_fetch_errors += 1
                        processed_contents.append("")
                    else:
                        processed_contents.append(fetch_result)
                fetched_css_contents = processed_contents
            except asyncio.TimeoutError:
                logger.error(f"Timeout during asyncio.gather for CSS fetching (timeout={CSS_FETCH_TIMEOUT}s)")
                fetched_css_contents = [""] * len(fetch_tasks)
                external_css_fetch_errors = len(fetch_tasks)
            except Exception as e:
                logger.error(f"Error during asyncio.gather for CSS fetching: {e}", exc_info=True)
                fetched_css_contents = [""] * len(fetch_tasks)
                external_css_fetch_errors = len(fetch_tasks)

            for i_css, css_content_ext in enumerate(fetched_css_contents):
                css_url_ext = urls_to_fetch[i_css]
                css_analyze_start_ext = time.time()
                if css_content_ext:
                    external_css_analyzed_count += 1
                    # Reduced verbosity for this INFO log
                    logger.debug(f"Analyzing fetched external CSS: {css_url_ext} - Size: {len(css_content_ext)} chars")
                    issues_from_ext, declarations_in_ext = self._analyze_css_for_small_fonts(css_content_ext, css_url_ext)
                    # This log is more for detailed debugging now
                    logger.debug(f"Finished analyzing external CSS {css_url_ext}. Duration: {time.time() - css_analyze_start_ext:.2f}s. Issues: {len(issues_from_ext)}, Decls: {declarations_in_ext}")
                    css_issues.extend(issues_from_ext)
                    total_analyzed_in_css += declarations_in_ext
                else:
                    if i_css < len(fetched_css_contents) and not isinstance(fetched_css_contents[i_css], Exception) and not css_content_ext:
                         external_css_fetch_errors += 1 # Count as error if fetch didn't except but content is empty
                    logger.warning(f"Skipping analysis for {css_url_ext} due to fetch error or empty content.")
        else:
            logger.info("No valid external CSS URLs found to fetch.")
        external_time = time.time() - external_start
        logger.info(f"External CSS analysis completed in {external_time:.2f}s.")

        all_issues = problematic_elements + css_issues
        has_issues = len(all_issues) > 0
        has_high_severity_issues = any(item.get('severity') == 'High' for item in all_issues)

        analysis_complete = external_css_analyzed_count == len(urls_to_fetch)
        some_external_fetched = external_css_analyzed_count > 0
        analysis_partially_complete = has_external_css and not analysis_complete and some_external_fetched
        analysis_failed_completely = has_external_css and not some_external_fetched and len(urls_to_fetch) > 0

        recommendation_text = ""
        priority = "Low"
        status_pass = not has_issues

        if not status_pass:
            recommendation_text += f"Found {len(all_issues)} instance(s) where text might be too small. For good readability, all text should generally be 12px or larger (or its equivalent in units like em, rem, pt)."
            priority = "Medium" # Default to Medium for any issue
            if has_high_severity_issues:
                recommendation_text += " Critical: Some text is significantly undersized (e.g., less than 10px). This can be very hard to read, especially on mobile devices or for users with visual impairments. Increase these font sizes promptly."
        else:
            priority = "Low"
            recommendation_text = None # Explicitly set to None if pass is true


        total_time = time.time() - start_time
        # Timing info removed from the final result as per user request
        # timing_info = {
        #     "total_analysis_time": total_time,
        #     "inline_styles_time": inline_time,
        #     "internal_css_time": internal_time,
        #     "external_css_time": external_time,
        # }

        result = {
            "pass": status_pass,
            "small_font_issue_count": len(all_issues),
            "elements_analyzed_inline": len(inline_style_elements),
            "declarations_analyzed_css": total_analyzed_in_css,
            "problematic_elements": problematic_elements[:5], # Keep sample concise
            "css_issues": css_issues[:5], # Keep sample concise
            "has_external_css": has_external_css,
            "external_css_total_links": len(external_css_links),
            "external_css_attempted_fetch": len(urls_to_fetch),
            "external_css_analyzed_count": external_css_analyzed_count,
            "external_css_fetch_errors": external_css_fetch_errors,
            # "timing": timing_info, # Ensure this remains commented or removed
            "description": (
                "Checks if text on your page is large enough to be easily readable. Small text can be hard to read, "
                "especially on mobile devices or for people with visual difficulties."
            ),
            "importance": (
                "Readable font sizes are key for a good user experience and web accessibility. Ensure no important text is smaller than 12px. This helps users engage with your content effectively."
            ),
            "recommendation": {"text": recommendation_text.strip(), "priority": priority} if recommendation_text else None,
            "blog": ""
        }

        max_score = self.MAX_SCORES.get('font_legibility', 19)
        score = max_score
        max_deduction = max_score 
        high_severity_count = sum(1 for issue in all_issues if issue.get('severity') == 'High')
        medium_severity_count = len(all_issues) - high_severity_count
        score -= min(high_severity_count * 3, max_deduction * 0.7)
        score -= min(medium_severity_count * 1, max_deduction * 0.3)
        if analysis_partially_complete or analysis_failed_completely:
            incomplete_deduction = min(5, score * 0.25)
            score -= incomplete_deduction
        score = max(round(score), 0)
        result['score'] = score

        logger.info(f"Finished font legibility analysis for {self.url}. Score: {score}, Issues: {len(all_issues)}, Time: {total_time:.2f}s")
        self._save_to_redis('font_legibility', result)
        return result

    def analyze_tap_target_sizing(self):
        if not self.soup:
            return {
                "pass": False,
                "message": "HTML content not available for analysis",
                "score": 0,
                "recommendation": {
                    "text": "Ensure all interactive elements (buttons, links) are at least 48x48px and have adequate spacing. Cannot analyze without HTML.",
                    "priority": "High"
                }
            }

        interactive_elements = {
            'a': self.soup.find_all('a', href=True),
            'button': self.soup.find_all('button'),
            'input': self.soup.find_all('input', type=lambda t: t in ['submit', 'button', 'reset', 'image', 'checkbox', 'radio']),
            'select': self.soup.find_all('select'),
            'textarea': self.soup.find_all('textarea'),
            'label': self.soup.find_all('label'), 
            'role_button': self.soup.find_all(attrs={"role": "button"}),
            'clickable_div': self.soup.find_all(lambda tag: tag.name == 'div' and tag.has_attr('onclick')), 
        }

        total_interactive = sum(len(elements) for elements in interactive_elements.values())

        problematic_elements = []
        checked_elements = set() 

        
        for element_type, elements in interactive_elements.items():
            for element in elements:
                if element in checked_elements: continue
                style = element.get('style', '')

                width_match = re.search(r'width:\s*([0-9.]+)(px|em|rem|%)', style, re.IGNORECASE)
                height_match = re.search(r'height:\s*([0-9.]+)(px|em|rem|%)', style, re.IGNORECASE)

                if width_match or height_match:
                    width_too_small = False
                    height_too_small = False
                    width_value = ''
                    height_value = ''

                    if width_match:
                        try:
                            width_size = float(width_match.group(1))
                            width_unit = width_match.group(2).lower()
                            width_value = f"{width_size}{width_unit}"
                            
                            if (width_unit == 'px' and width_size < 48) or \
                               (width_unit == 'em' and width_size < 3) or \
                               (width_unit == 'rem' and width_size < 3): 
                                width_too_small = True
                        except ValueError: pass

                    if height_match:
                        try:
                            height_size = float(height_match.group(1))
                            height_unit = height_match.group(2).lower()
                            height_value = f"{height_size}{height_unit}"
                            
                            if (height_unit == 'px' and height_size < 48) or \
                               (height_unit == 'em' and height_size < 3) or \
                               (height_unit == 'rem' and height_size < 3): 
                                height_too_small = True
                        except ValueError: pass

                    if width_too_small or height_too_small:
                        element_text = element.get_text(strip=True)[:50] or \
                                       element.get('aria-label', '')[:50] or \
                                       element.get('id', '') or \
                                       f"{element.name} element"

                        problematic_elements.append({
                            'element_type': element_type,
                            'text': element_text + ('...' if len(element_text) > 50 else ''),
                            'width': width_value,
                            'height': height_value,
                            'issue': 'Small tap target size detected in inline style'
                        })
                        checked_elements.add(element)


        
        small_indicator_classes = ['small', 'sm', 'xs', 'tiny', 'mini', 'icon-only', 'btn-sm', 'btn-xs']
        for element_type, elements in interactive_elements.items():
            for element in elements:
                if element in checked_elements: continue
                element_classes = element.get('class', [])
                if not isinstance(element_classes, list):
                    element_classes = element_classes.split() if element_classes else []

                found_indicator = False
                indicator_class = ''
                for cls in element_classes:
                    if any(indicator in cls.lower() for indicator in small_indicator_classes):
                        found_indicator = True
                        indicator_class = cls
                        break

                if found_indicator:
                    element_text = element.get_text(strip=True)[:50] or \
                                   element.get('aria-label', '')[:50] or \
                                   element.get('id', '') or \
                                   f"{element.name} element"

                    problematic_elements.append({
                        'element_type': element_type,
                        'text': element_text + ('...' if len(element_text) > 50 else ''),
                        'class': indicator_class,
                        'issue': 'Potentially small element based on class name (less reliable)'
                    })
                    checked_elements.add(element)


        has_touch_specific_css = False
        touch_media_queries = []

        
        for style_tag in self.soup.find_all('style'):
            if style_tag.string:
                
                touch_queries = re.findall(r'@media[^{]*\(\s*(?:pointer|any-pointer)\s*:\s*coarse\s*\)',
                                           style_tag.string, re.IGNORECASE | re.DOTALL)
                if touch_queries:
                    has_touch_specific_css = True
                    touch_media_queries.extend(touch_queries)
                    break 


        recommendation_text = ""
        priority = "Low"
        status = len(problematic_elements) == 0

        if not status:
            recommendation_text += f"Found {len(problematic_elements)} potentially small tap targets. Ensure all interactive elements (buttons, links, etc.) are at least 48x48 pixels for optimal usability. "
            priority = "High"
            recommendation_text += "Maintain adequate spacing (at least 8px) between interactive elements. "
            if not has_touch_specific_css:
                recommendation_text += "Consider adding touch-specific media queries (`@media (pointer: coarse)`) to fine-tune styles like padding or margins for touch devices."
        else:
            recommendation_text = None # Explicitly set to None if pass is true

        result = {
            "pass": status,
            "total_interactive_elements": total_interactive,
            "problematic_elements_count": len(problematic_elements),
            "problematic_elements": problematic_elements[:10], 
            "has_touch_specific_css": has_touch_specific_css,
            "touch_media_queries": touch_media_queries[:5], 
            "description": (
                "Tap targets are interactive elements users interact with on touchscreens. "
                "Best practices recommend a minimum size of 48x48 CSS pixels with adequate spacing (at least 8px) "
                "to prevent accidental taps and improve usability, especially for users with motor impairments."
            ),
            "importance": (
                "Properly sized tap targets significantly improve mobile usability, reducing user frustration and errors. "
                "This contributes to a better user experience and aligns with accessibility guidelines (WCAG 2.5.5 Target Size - Level AAA)."
            ),
            "recommendation": {"text": recommendation_text.strip() if recommendation_text else None, "priority": priority} if recommendation_text else None,
            "blog": ""
        }

        max_score = self.MAX_SCORES.get('tap_target_sizing', 19)
        score = max_score
        max_deduction_from_elements = 15

        
        inline_style_issues = sum(1 for p in problematic_elements if 'inline style' in p['issue'])
        class_name_issues = len(problematic_elements) - inline_style_issues

        score -= min(inline_style_issues * 2, max_deduction_from_elements * 0.8)
        score -= min(class_name_issues * 1, max_deduction_from_elements * 0.2) 


        if not has_touch_specific_css:
            score -= 2 

        score = max(round(score), 0) 

        result['score'] = score

        self._save_to_redis('tap_target_sizing', result)
        return result


async def analyze_website_usability(url, task_id=None, html=None, device_screenshots=None, technical_data=None):
    analyzer = UsabilityAnalyzer(
        url=url,
        task_id=task_id,
        html=html,
        device_screenshots=device_screenshots,
        technical_data=technical_data
    )

    results = await analyzer.run_all()

    if analyzer.redis_available:
        try:
            cache_key = f"usability-result:{url}"
            analyzer.redis_client.set(cache_key, json.dumps(results, default=str), ex=3600)
            logger.info(f"Cached usability results in Redis with key {cache_key}")
        except Exception as e:
            logger.error(f"Failed to cache usability results: {e}")

    return results