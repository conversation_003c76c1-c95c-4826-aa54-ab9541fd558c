import re
from bs4 import BeautifulSoup
import json
import logging
import os
from django.conf import settings
import httpx
from typing import Optional

logger = logging.getLogger(__name__)

MAX_PERFORMANCE_SCORES = {
    "analyze_console_errors":         1,
    "analyze_deprecated_html":        3,
    "check_compression":             12,
    "count_resources":               12,
    "check_amp":                      8,
    "analyze_page_size":             12,
    "analyze_inline_styles":          4,
    "analyze_performance_timing":    15,
    "check_http_protocol":            8,
    "analyze_image_optimisation":    15,
    "check_minification":            10
}


class PerformanceAnalyzer:
    MAX_SCORES = MAX_PERFORMANCE_SCORES
    
    def __init__(self, html_content, headers, url=None, task_id=None, console_logs=None, proxy_string: Optional[str] = None):
        
        self.html_content = html_content
        self.headers = headers
        self.url = url
        self.task_id = task_id
        self.console_logs = console_logs or []
        self.results = {}
        self.pagespeed_api_key = os.getenv('PAGESPEED_API_KEY', getattr(settings, 'PAGESPEED_API_KEY', None))
        self.proxy_string = proxy_string
        logger.info(f"[PERFORMANCE ANALYSIS] Initialized analyzer for URL: {url}")
    
    def run_all(self):
        
        try:
            logger.info(f"[PERFORMANCE ANALYSIS] Starting performance analysis for URL: {self.url}")
            
            self.results["javascript_errors"] = self.analyze_console_errors()
            self.results["deprecated_html"] = self.analyze_deprecated_html()
            self.results["compression"] = self.check_compression()
            self.results["resource_count"] = self.count_resources()
            self.results["amp"] = self.check_amp()
            self.results["page_size"] = self.analyze_page_size()
            self.results["inline_styles"] = self.analyze_inline_styles()
            self.results["performance_timing"] = self.analyze_performance_timing()
            self.results["http_protocol"] = self.check_http_protocol()
            self.results["image_optimisation"] = self.analyze_image_optimisation()
            self.results["minification"] = self.check_minification()
            
            total_score = 0
            total_possible = sum(self.MAX_SCORES.values())

            for key, result_data in self.results.items():
                 if isinstance(result_data, dict):
                    total_score += result_data.get('score', 0)

            total_score = min(total_score, total_possible)

            percentage = round((total_score / total_possible) * 100) if total_possible > 0 else 0

            if percentage >= 98:
                grade = 'A+'
            elif percentage >= 95:
                grade = 'A'
            elif percentage >= 90:
                grade = 'A-'
            elif percentage >= 85:
                grade = 'B+'
            elif percentage >= 80:
                grade = 'B'
            elif percentage >= 75:
                grade = 'B-'
            elif percentage >= 70:
                grade = 'C+'
            elif percentage >= 60:
                grade = 'C'
            elif percentage >= 50:
                grade = 'C-'
            elif percentage >= 40:
                grade = 'D'
            else: 
                grade = 'F'

            self.results['total_score'] = {
                'score': total_score,
                'grade': grade
            }
            
            # Add overall title and description based on grade
            if grade in ['A+', 'A', 'A-']:
                overall_title = "Your Performance is Excellent"
                overall_description = "Your website demonstrates excellent technical performance. Only minor improvements may be needed."
            elif grade in ['B+', 'B', 'B-']:
                overall_title = "Your Performance is Good"
                overall_description = "Your website has good technical performance, but there are some areas that could be improved for even better speed and user experience."
            elif grade in ['C+', 'C', 'C-']:
                overall_title = "Your Performance Could Be Better"
                overall_description = "Your website has some performance optimisations, but there are several areas that need attention to improve speed and reliability."
            elif grade == 'D':
                overall_title = "Your Performance Needs Improvement"
                overall_description = "Your website has significant performance issues. Address the key recommendations to improve speed and user experience."
            else:
                overall_title = "Your Performance is Poor"
                overall_description = "Your website is missing critical performance optimisations. Immediate action is required to improve speed and reliability."

            # Aggregate the most important recommendations (priority High, up to 2)
            high_priority_recos = []
            for key, metric in self.results.items():
                if isinstance(metric, dict):
                    rec = metric.get('recommendation', {})
                    if isinstance(rec, dict) and rec.get('priority') == 'High':
                        high_priority_recos.append(rec.get('text'))
            summary_recommendation = " ".join(high_priority_recos[:2]) if high_priority_recos else "Review the detailed recommendations below to further optimize your website's performance."

            self.results['overall_title'] = overall_title
            self.results['overall_description'] = overall_description
            self.results['overall_recommendation'] = summary_recommendation
            
            logger.info(f"[PERFORMANCE ANALYSIS] Completed performance analysis for URL: {self.url}. Score: {total_score}/{total_possible} ({percentage}%)")
            
            return self.results
            
        except Exception as e:
            logger.error(f"[PERFORMANCE ANALYSIS] Error in run_all(): {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {"error": f"Performance analysis failed: {str(e)}"}

    def analyze_javascript_errors(self):
        
        soup = BeautifulSoup(self.html_content, "lxml")
        errors = []
        for script in soup.find_all("script"):
            if script.string:
                script_text = script.string.strip()
                if "error" in script_text.lower():
                    errors.append(script_text[:200] + ("..." if len(script_text) > 200 else ""))
        return {
            "pass": len(errors) == 0,
            "error_count": len(errors),
            "warning_count": 0,
            "errors": errors[:10],
            "warnings": [],
            "description": "Static analysis cannot fully detect JavaScript runtime errors from static HTML.",
            "recommendation": "Use browser-based tools or headless browser testing to detect JavaScript errors at runtime.",
            "importance": "JavaScript errors can affect functionality and SEO if they disrupt page rendering.",
            "score": 0 if len(errors) == 0 else 1
        }

    def analyze_console_errors(self):
        
        errors = []
        for log in self.console_logs or []:
            if log.get('type') == 'error':
                errors.append(log.get('text', ''))
        
        has_errors = len(errors) > 0
        priority = "Low"
        recommendation_text = "No JavaScript console errors detected."
        max_score = self.MAX_SCORES.get("analyze_console_errors", 1)
        score = max_score if not has_errors else 0
        recommendation = None

        if has_errors:
            recommendation_text = f"Found {len(errors)} JavaScript console error(s). Fix these errors to avoid runtime issues and potential impact on functionality or user experience."
            priority = "High"
            recommendation = {"text": recommendation_text, "priority": priority}
        else: # not has_errors (pass is True)
            recommendation = None

        return {
            "pass": not has_errors,
            "error_count": len(errors),
            "errors": errors[:10],
            "recommendation": recommendation,
            "description": "Checks for JavaScript errors logged to the browser console during page load.",
            "importance": "Console errors can break page functionality, negatively impact user experience, and sometimes interfere with search engine crawling or rendering.",
            "blog": "",
            "score": score
        }

    def analyze_deprecated_html(self):
        
        soup = BeautifulSoup(self.html_content, "lxml")
        deprecated_tags = [
            'applet', 'basefont', 'center', 'dir', 'font', 'frame', 'frameset', 'isindex',
            'marquee', 'menu', 'noframes', 'strike', 'tt', 'bgsound', 'blink', 'spacer',
            'acronym', 'big', 'u', 'xmp', 'plaintext', 'listing', 'multicol', 'nextid',
            'nobr', 'noembed'
        ]
        deprecated_elements = []
        for tag in deprecated_tags:
            for el in soup.find_all(tag):
                html_snippet = str(el)
                text_snippet = el.get_text(strip=True)
                deprecated_elements.append({
                    "tag": tag,
                    "html": html_snippet[:200] + ("..." if len(html_snippet) > 200 else ""),
                    "content": text_snippet[:50] + ("..." if len(text_snippet) > 50 else "")
                })
        grouped = {}
        for item in deprecated_elements:
            grouped.setdefault(item["tag"], []).append(item)

        has_deprecated = len(deprecated_elements) > 0
        priority = "Low"
        recommendation_text = "No deprecated HTML elements found."
        max_score = self.MAX_SCORES.get("analyze_deprecated_html", 3)
        score = max_score if not has_deprecated else 0
        recommendation = None

        if has_deprecated:
            recommendation_text = f"Found {len(deprecated_elements)} deprecated HTML element(s). Replace deprecated tags (like {', '.join(grouped.keys())}) with modern HTML5 equivalents and use CSS for styling/layout."
            priority = "Medium"
            recommendation = {"text": recommendation_text, "priority": priority}
        else: # not has_deprecated (pass is True)
            recommendation = None

        return {
            "pass": not has_deprecated,
            "count": len(deprecated_elements),
            "elements_by_tag": grouped,
            "elements": deprecated_elements[:10],
            "description": "Identifies outdated HTML elements that may not be supported or render correctly in modern browsers.",
            "recommendation": recommendation,
            "importance": "Using deprecated tags indicates outdated practices, potentially affecting browser compatibility, accessibility, and maintainability. While direct SEO impact is low, it reflects code quality.",
            "blog": "",
            "score": score
        }

    def check_compression(self):
        max_score = self.MAX_SCORES.get("check_compression", 12)
        score = 0
        proxy_url = None  # Initialize proxy url
        try:
            compressed_size = None
            uncompressed_size = None
            compression_type = None

            # Base options (excluding proxies)
            client_options = {
                "http2": True,
                "timeout": 60.0,
            }
            if self.proxy_string:
                # Parse HOST:PORT:USER:PASS and format URL
                match = re.match(r'^([^:]+):(\d+):([^:]+):(.+)$', self.proxy_string)
                if match:
                    host, port, user, password = match.groups()
                    proxy_url = f"http://{user}:{password}@{host}:{port}" # Correct format
                    logger.info(f"[PERFORMANCE ANALYSIS] check_compression using proxy: {host}:{port}")
                else:
                    logger.warning(f"[PERFORMANCE ANALYSIS] Invalid proxy string format: {self.proxy_string}. Proceeding without proxy.")
                    proxy_url = None # Ensure proxy_url is None if format is bad
            else:
                 logger.info("[PERFORMANCE ANALYSIS] check_compression running without proxy")

            # Log arguments before client creation
            logger.debug(f"[PERFORMANCE ANALYSIS] httpx.Client args - options: {client_options}, proxy: {proxy_url}") # Log potentially None proxy_url

            # Use the 'proxy' argument (singular)
            with httpx.Client(**client_options, proxy=proxy_url) as client:
                 # Make the first request for compressed content
                 compressed_resp = client.get(
                     self.url,
                     headers={"Accept-Encoding": "gzip, deflate, br"}
                 )
                 compressed_bytes = compressed_resp.content or b""
                 compressed_size = len(compressed_bytes)
                 compression_type = compressed_resp.headers.get("Content-Encoding")

            # Need to handle the second request inside the correct context block
            uncompressed_size = None
            if compressed_size > 0:
                # Re-establish client with the same logic for the second request
                # Note: proxy_url is already defined (or None) from the first part
                with httpx.Client(**client_options, proxy=proxy_url) as client2:
                     identity_resp = client2.get(
                         self.url,
                         headers={"Accept-Encoding": "identity"}
                     )
                     uncompressed_bytes = identity_resp.content or b""
                     uncompressed_size = len(uncompressed_bytes)
            else:
                uncompressed_bytes = compressed_bytes
                uncompressed_size = compressed_size
                compression_type = None

            if uncompressed_size is not None and compressed_size is not None and uncompressed_size > 0:
                ratio = ((uncompressed_size - compressed_size) / uncompressed_size * 100)
            else:
                ratio = 0

            status = ratio > 10
            priority = "Low"
            recommendation_text = ""
            recommendation = None

            if status:
                recommendation = None
                score = max_score
            elif compression_type:
                 recommendation_text = f"Compression ({compression_type}) is enabled but the ratio ({ratio:.1f}%) is low. Review server configuration or content."
                 priority = "Medium"
                 score = max_score // 2
                 recommendation = {"text": recommendation_text, "priority": priority}
            else:
                recommendation_text = "Server-side compression (gzip, Brotli) is not enabled for HTML. Enable compression to significantly reduce file size and improve load times."
                priority = "High"
                score = 0
                recommendation = {"text": recommendation_text, "priority": priority}

            return {
                "pass": status,
                "compression_type": compression_type,
                "size_mb": round(uncompressed_size / (1024 * 1024), 3) if uncompressed_size is not None else None,
                "compressed_size_mb": round(compressed_size / (1024 * 1024), 3) if compressed_size is not None else None,
                "compression_ratio": round(ratio, 1),
                "recommendation": recommendation,
                "description": "Checks if server-side compression (like gzip or Brotli) is enabled for the HTML document, reducing its transfer size.",
                "importance": "Compression significantly reduces file sizes, leading to faster downloads, lower bandwidth usage, and improved page load speed, which is a key SEO factor.",
                "blog": "",
                "score": score
            }
        except httpx.TimeoutException as e:
             logger.error(f"[PERFORMANCE ANALYSIS] Timeout error checking compression: {e}")
             return {
                 "pass": False,
                 "error": f"Timeout occurred while checking compression: {str(e)}",
                 "recommendation": {"text": "Could not verify compression due to a timeout. Check network or proxy settings.", "priority": "Medium"},
                 "score": 0
            }
        except Exception as e:
            logger.error(f"[PERFORMANCE ANALYSIS] Error checking compression: {e}")
            return {
                "pass": False,
                "error": f"Could not check compression: {str(e)}",
                "recommendation": {"text": "Unable to verify compression status due to an error.", "priority": "Medium"},
                "score": 0
            }

    def count_resources(self):
        
        soup = BeautifulSoup(self.html_content, "lxml")
        resources = {
            "html": 1,
            "js": len(soup.find_all("script", src=True)),
            "css": len(soup.find_all("link", rel="stylesheet", href=True)),
            "images": len(soup.find_all("img", src=True)),
            "fonts": len(soup.find_all("link", rel="preload", attrs={"as": "font"})) + len(soup.find_all("link", rel="stylesheet", href=lambda h: h and ('.woff' in h or '.ttf' in h or '.otf' in h))),
            "iframes": len(soup.find_all("iframe")), 
            "other": len(soup.find_all(["audio", "video", "object", "embed"]))
        }
        total = resources["html"] + resources["js"] + resources["css"] + resources["images"] + resources["fonts"] + resources["iframes"] + resources["other"]
        resources["total"] = total

        priority = "Low"
        recommendation_text = ""
        max_score = self.MAX_SCORES.get("count_resources", 12)
        score = max_score
        recommendation = None

        if total <= 30: # status is True, pass is True
            status = True
            # recommendation_text = f"The page loads a low number of resources ({total}), which is excellent for performance."
            # priority = "Low"
            score = max_score
            recommendation = None
        elif total <= 60: # status is True, pass is True
            status = True
            # recommendation_text = f"The page loads a moderate number of resources ({total}). Ensure critical resources load quickly."
            # priority = "Low"
            score = max_score # score was already max_score here, it's a pass
            recommendation = None
        elif total <= 100: # status is False, pass is False
             status = False
             recommendation_text = f"The page loads a high number of resources ({total}). Consider reducing requests by combining files (CSS/JS), using CSS sprites, or lazy loading non-critical resources."
             priority = "Medium"
             score = max_score * 0.4
             recommendation = {"text": recommendation_text, "priority": priority}
        else: # status is False, pass is False
            status = False
            recommendation_text = f"The page loads a very high number of resources ({total}). Aggressively reduce requests through techniques like file combination, sprites, lazy loading, and removing unused assets."
            priority = "High"
            score = max_score * 0.1
            recommendation = {"text": recommendation_text, "priority": priority}

        resources["pass"] = status
        resources["recommendation"] = recommendation
        resources["description"] = "Counts the number of external resources (CSS, JS, images, fonts, etc.) requested by the page."
        resources["importance"] = "Each resource requires a separate HTTP request. Reducing the number of requests minimizes network latency and speeds up page load, improving user experience and SEO."
        resources["blog"] = ""
        resources["score"] = round(score)
        return resources

    def check_amp(self):
        
        soup = BeautifulSoup(self.html_content, "lxml")
        amp_flags = {
            "has_amp": False,
            "is_amp_page": False,
            "has_amp_link": False,
            "amp_url": None
        }

        html_tag = soup.find("html")
        is_amp_page = bool(html_tag and (html_tag.has_attr("amp") or html_tag.has_attr("⚡")))
        if is_amp_page:
             amp_flags["is_amp_page"] = True
             amp_flags["has_amp"] = True

        amphtml_link = soup.find("link", rel="amphtml", href=True)
        if amphtml_link:
            amp_flags["has_amp_link"] = True
            amp_flags["amp_url"] = amphtml_link.get("href")
            amp_flags["has_amp"] = True

        priority = "Low"
        recommendation_text = ""
        max_score = self.MAX_SCORES.get("check_amp", 8)
        score = 0
        status = False # Default to false unless a positive AMP state is confirmed
        recommendation = None

        if amp_flags["is_amp_page"]:
            has_runtime = bool(soup.find("script", src=lambda src: src and "cdn.ampproject.org" in src))
            if has_runtime:
                 status = True
                 score = max_score
                 recommendation = None
            else: # is_amp_page but no runtime (pass is False)
                 recommendation_text = "This page is declared as AMP (html tag attribute) but might be missing the AMP runtime script. Validate the AMP structure."
                 status = False
                 priority = "Medium"
                 score = max_score // 2
                 recommendation = {"text": recommendation_text, "priority": priority}
        elif amp_flags["has_amp_link"]: # pass is True
            status = True # Linking to AMP is also a positive AMP strategy
            score = max_score
            recommendation = None # Ideal state if linking to AMP
        else: # Not an AMP page and no link to one (pass is True, as it's optional)
            status = True # Not using AMP is not a failure, so pass is true
            score = 0 # No points awarded or deducted for not using optional tech here
            recommendation = None # No problem, so no recommendation

        amp_flags.update({
            "pass": status,
            "recommendation": recommendation,
            "description": "Checks if the page is an AMP page itself or links to a separate AMP version.",
            "importance": "AMP (Accelerated Mobile Pages) is a framework for creating fast-loading mobile pages. While not mandatory, valid AMP pages can receive preferential treatment in some mobile search results (like carousels).",
            "blog": "",
            "score": score
        })
        if "has_amp" in amp_flags: # Safely delete if it exists
            del amp_flags["has_amp"]
        return amp_flags

    def analyze_page_size(self):
        
        html_size = len(self.html_content)
        html_size_mb = html_size / (1024 * 1024)

        soup = BeautifulSoup(self.html_content, "lxml")
        num_scripts = len(soup.find_all("script", src=True))
        num_css = len(soup.find_all("link", rel="stylesheet", href=True))
        num_images = len(soup.find_all("img", src=True))

        est_js_size = num_scripts * 100 * 1024
        est_css_size = num_css * 50 * 1024
        est_image_size = num_images * 150 * 1024

        total_estimated_size = html_size + est_js_size + est_css_size + est_image_size
        total_size_mb = total_estimated_size / (1024 * 1024)

        priority = "Low"
        recommendation_text = ""
        max_score = self.MAX_SCORES.get("analyze_page_size", 12)
        score = max_score
        recommendation = None # Initialize recommendation to None

        if total_size_mb < 1.5:
            size_category = "Light"
            # recommendation_text = f"Estimated total page size is {total_size_mb:.2f} MB, which is excellent."
            # priority = "Low" # Not needed if recommendation is None
            status = True
            score = max_score
            recommendation = None # Explicitly None for pass = True
        elif total_size_mb < 3.0:
            size_category = "Moderate"
            # recommendation_text = f"Estimated total page size is {total_size_mb:.2f} MB. This is reasonable, but review large assets (images, JS)."
            # priority = "Low" # Not needed if recommendation is None
            status = True
            score = max_score * 0.8
            recommendation = None # Explicitly None for pass = True
        elif total_size_mb < 5.0:
            size_category = "Heavy"
            recommendation_text = f"Estimated total page size is {total_size_mb:.2f} MB. Prioritize optimizing images, minifying CSS/JS, and deferring non-critical resources."
            priority = "Medium"
            status = False
            score = max_score * 0.4
            recommendation = {"text": recommendation_text, "priority": priority}
        else:
            size_category = "Very Heavy"
            recommendation_text = f"Estimated total page size is {total_size_mb:.2f} MB. Aggressively optimize all assets, remove unused resources, and consider code splitting to improve load performance."
            priority = "High"
            status = False
            score = max_score * 0.1
            recommendation = {"text": recommendation_text, "priority": priority}

        breakdown = {
            "html_mb": round(html_size_mb, 2),
            "est_js_mb": round(est_js_size / (1024*1024), 2),
            "est_css_mb": round(est_css_size / (1024*1024), 2),
            "est_images_mb": round(est_image_size / (1024*1024), 2)
        }

        return {
            "pass": status,
            "total_estimated_size_mb": round(total_size_mb, 2),
            "size_category": size_category,
            "breakdown_estimated_mb": breakdown,
            "recommendation": recommendation, # Use the recommendation variable
            "description": "Provides a rough estimate of the total page size (HTML + estimated resources) based on the number of assets linked.",
            "importance": "Page size directly impacts load time. Smaller pages load faster, improving user experience and potentially boosting SEO rankings (Core Web Vitals). Aim for < 3MB.",
            "blog": "",
            "score": round(score)
        }

    def analyze_inline_styles(self):
        soup = BeautifulSoup(self.html_content, "lxml")
        all_elements = soup.find_all()
        elements_with_style = soup.find_all(lambda tag: tag.has_attr("style"))
        total_elements = len(all_elements)
        style_count = len(elements_with_style)
        total_style_size = sum(len(tag.get("style", "")) for tag in elements_with_style)
        common_properties = {}
        style_examples = []

        for tag in elements_with_style:
            style_attr = tag.get("style", "")
            try:
                 properties = [p.strip() for p in style_attr.split(";") if p.strip()]
                 for prop in properties:
                     if ':' in prop:
                         prop_name = prop.split(":")[0].strip().lower()
                         if prop_name:
                             common_properties[prop_name] = common_properties.get(prop_name, 0) + 1
            except Exception as e:
                 logger.warning(f"Could not parse inline style: '{style_attr}'. Error: {e}")
                 continue

            if len(style_examples) < 5:
                style_examples.append({
                    "tag": tag.name,
                    "style": style_attr[:100] + ("..." if len(style_attr) > 100 else "")
                })

        inline_percentage = (style_count / total_elements * 100) if total_elements > 0 else 0
        status = inline_percentage < 5
        priority = "Low"
        recommendation_text = ""
        max_score = self.MAX_SCORES.get("analyze_inline_styles", 4)
        score = max_score * (1 - min(1, inline_percentage / 20))
        recommendation = None # Initialize recommendation to None

        if style_count == 0:
            # recommendation_text = "No inline styles found. Using external stylesheets is the recommended practice."
            # priority = "Low"
            score = max_score
            recommendation = None # Explicitly None for pass = True (and style_count == 0)
        elif inline_percentage < 1: # This implies status is True
            # recommendation_text = f"Minimal inline styles detected ({style_count} elements, {inline_percentage:.1f}%). While low, consider moving these to external CSS for better maintainability."
            # priority = "Low"
            score = max_score * 0.8 # Score is adjusted, but it's still a pass
            recommendation = None # Explicitly None for pass = True
        elif inline_percentage < 10: # This implies status is False
            recommendation_text = f"Moderate use of inline styles ({style_count} elements, {inline_percentage:.1f}%). Moving styles to external CSS files improves caching and simplifies updates."
            priority = "Medium"
            score = max_score * 0.6
            recommendation = {"text": recommendation_text, "priority": priority}
        else: # This implies status is False
            recommendation_text = f"High usage of inline styles ({style_count} elements, {inline_percentage:.1f}%). This significantly impacts performance and maintainability. Prioritize refactoring to use external stylesheets."
            priority = "High"
            score = max_score * 0.4
            recommendation = {"text": recommendation_text, "priority": priority}

        sorted_common = dict(sorted(common_properties.items(), key=lambda item: item[1], reverse=True)[:10])

        return {
            "pass": status,
            "elements_with_style": style_count,
            "total_elements": total_elements,
            "inline_style_percentage": round(inline_percentage, 1),
            "total_style_size_kb": round(total_style_size / 1024, 1),
            "most_common_properties": sorted_common,
            "style_examples": style_examples,
            "recommendation": recommendation, # Use the recommendation variable
            "description": "Analyzes the usage of inline style attributes within HTML elements.",
            "importance": "Inline styles prevent CSS caching, increase HTML file size, and make maintenance harder. Prioritizing external stylesheets improves performance and code organization.",
            "blog": "",
            "score": round(score)
        }
    
    def generate_summary(self):
        issues = []
        recommendations = []
        
        if "javascript_errors" in self.results:
            js_errors = self.results["javascript_errors"]
            if not js_errors["pass"]:
                issues.append(f"Found {js_errors['error_count']} JavaScript errors.")
        
        if "deprecated_html" in self.results:
            deprecated = self.results["deprecated_html"]
            if not deprecated["pass"]:
                issues.append(f"Found {deprecated['count']} deprecated HTML elements.")
                recommendations.append(deprecated["recommendation"]["text"])
        
        if "compression" in self.results:
            compression = self.results["compression"]
            if not compression["pass"]:
                issues.append("Page resources are not being compressed.")
                recommendations.append(compression["recommendation"]["text"])
        
        if "resource_count" in self.results:
            resources = self.results["resource_count"]
            if resources["total"] > 50:
                issues.append(f"Page loads {resources['total']} resources, which may impact performance.")
                recommendations.append(resources["recommendation"]["text"])
        
        if "page_size" in self.results:
            page_size = self.results["page_size"]
            if not page_size["pass"]:
                issues.append(f"Page size is {page_size['total_estimated_size_mb']}MB ({page_size['size_category']}), which may slow loading.")
                recommendations.append(page_size["recommendation"]["text"])
        
        if "inline_styles" in self.results:
            inline_styles = self.results["inline_styles"]
            if not inline_styles["pass"]:
                issues.append(f"Found {inline_styles['elements_with_style']} elements with inline styles ({inline_styles['inline_style_percentage']}% of elements).")
                recommendations.append(inline_styles["recommendation"]["text"])
        
        overall = "Performance analysis completed."
        
        return {
            "overall": overall,
            "issues_count": len(issues),
            "issues": issues,
            "recommendations": recommendations
        }

    def analyze_performance_timing(self):
        max_score = self.MAX_SCORES.get("analyze_performance_timing", 15)
        proxy_url = None # Initialize proxy url
        try:
            import time
            start = time.time()
            ttfb = None

            # Base options (excluding proxies)
            client_options = {
                "http2": True,
                 "timeout": 60.0
             }
            if self.proxy_string:
                 # Parse HOST:PORT:USER:PASS and format URL
                 match = re.match(r'^([^:]+):(\d+):([^:]+):(.+)$', self.proxy_string)
                 if match:
                     host, port, user, password = match.groups()
                     proxy_url = f"http://{user}:{password}@{host}:{port}" # Correct format
                     logger.info(f"[PERFORMANCE ANALYSIS] analyze_performance_timing using proxy: {host}:{port}")
                 else:
                     logger.warning(f"[PERFORMANCE ANALYSIS] Invalid proxy string format: {self.proxy_string}. Proceeding without proxy.")
                     proxy_url = None # Ensure proxy_url is None if format is bad
            else:
                 logger.info("[PERFORMANCE ANALYSIS] analyze_performance_timing running without proxy")

            # Log arguments before client creation
            logger.debug(f"[PERFORMANCE ANALYSIS] httpx.Client args - options: {client_options}, proxy: {proxy_url}")

            # Use the 'proxy' argument (singular)
            with httpx.Client(**client_options, proxy=proxy_url) as client:
                 resp = client.get(self.url)
                 ttfb = resp.elapsed.total_seconds()

            full_load_approximation = ttfb + 0.5

            priority = "Low"
            recommendation_text = ""
            status = True
            score = max_score
            recommendation = None # Initialize recommendation to None

            if ttfb is None:
                 status = False
                 recommendation_text = "Could not measure server response time."
                 priority = "Medium"
                 score = 0
                 recommendation = {"text": recommendation_text, "priority": priority}
            elif ttfb > 1.0:
                status = False
                recommendation_text = f"Server response time (TTFB) is slow ({ttfb:.2f}s). Optimize server-side processing, database queries, and consider using a CDN."
                priority = "High"
                score = max_score * 0.2
                recommendation = {"text": recommendation_text, "priority": priority}
            elif ttfb > 0.5:
                status = False
                recommendation_text = f"Server response time (TTFB) is moderate ({ttfb:.2f}s). Investigate potential server/backend bottlenecks."
                priority = "Medium"
                score = max_score * 0.6
                recommendation = {"text": recommendation_text, "priority": priority}
            else: # ttfb <= 0.5 and not None
                # recommendation_text = f"Server response time (TTFB) is good ({ttfb:.2f}s)."
                # priority = "Low" # Not needed if recommendation is None
                recommendation = None # Explicitly None for pass = True

            return {
                "pass": status,
                "time_to_first_byte_s": round(ttfb, 2) if ttfb is not None else None,
                "recommendation": recommendation, # Use the recommendation variable
                "description": "Measures the Time To First Byte (TTFB), indicating server responsiveness.",
                "importance": "TTFB is a key metric reflecting server and network performance. Slow TTFB directly impacts user experience and Core Web Vitals (LCP often depends on it). Aim for < 0.5s.",
                "blog": "",
                "score": round(score)
            }
        except httpx.TimeoutException as e:
             logger.error(f"[PERFORMANCE ANALYSIS] Timeout error analyzing performance timing: {e}")
             return {
                 "pass": False,
                 "error": f"Timeout occurred during timing analysis: {str(e)}",
                 "recommendation": {"text": "Could not measure performance timing due to a timeout. Check network or proxy settings.", "priority": "Medium"},
                 "score": 0
            }
        except Exception as e:
            logger.error(f"[PERFORMANCE ANALYSIS] Error analyzing performance timing: {e}")
            return {
                "pass": False,
                "error": f"Timing analysis failed: {str(e)}",
                "recommendation": {"text": "Unable to measure performance timing due to an error.", "priority": "Medium"},
                "score": 0
             }

    def check_http_protocol(self):
        max_score = self.MAX_SCORES.get("check_http_protocol", 8)
        proxy_url = None # Initialize proxy url
        try:
            protocol = "Unknown"
            alt_svc = ""
            is_http2 = False
            is_http3 = False

            # Base options (excluding proxies)
            client_options = {
                 "http2": True,
                 "timeout": 30.0
             }
            if self.proxy_string:
                 # Parse HOST:PORT:USER:PASS and format URL
                 match = re.match(r'^([^:]+):(\d+):([^:]+):(.+)$', self.proxy_string)
                 if match:
                     host, port, user, password = match.groups()
                     proxy_url = f"http://{user}:{password}@{host}:{port}" # Correct format
                     logger.info(f"[PERFORMANCE ANALYSIS] check_http_protocol using proxy: {host}:{port}")
                 else:
                     logger.warning(f"[PERFORMANCE ANALYSIS] Invalid proxy string format: {self.proxy_string}. Proceeding without proxy.")
                     proxy_url = None # Ensure proxy_url is None if format is bad
            else:
                 logger.info("[PERFORMANCE ANALYSIS] check_http_protocol running without proxy")

            # Log arguments before client creation
            logger.debug(f"[PERFORMANCE ANALYSIS] httpx.Client args - options: {client_options}, proxy: {proxy_url}")

            # Use the 'proxy' argument (singular)
            with httpx.Client(**client_options, proxy=proxy_url) as client:
                 resp = client.get(self.url)
                 protocol = resp.http_version
                 alt_svc = resp.headers.get('Alt-Svc', '') or resp.headers.get('alt-svc', '')

            is_http2 = (protocol == 'HTTP/2')
            is_http3 = 'h3=' in alt_svc.lower() or ':443"; ma=' in alt_svc.lower()

            status = is_http2 or is_http3
            priority = "Low"
            recommendation_text = ""
            score = 0
            recommendation = None # Initialize recommendation to None

            if is_http3:
                # recommendation_text = f"Site supports HTTP/3 (indicated by Alt-Svc header: {alt_svc}), offering the latest performance benefits."
                # priority = "Low"
                score = max_score
                recommendation = None # Explicitly None for pass = True
            elif is_http2:
                # recommendation_text = f"Site uses HTTP/2 ({protocol}), providing good performance benefits over HTTP/1.1."
                # priority = "Low"
                score = max_score
                recommendation = None # Explicitly None for pass = True
            else:
                recommendation_text = f"Site is using an older protocol ({protocol}). Enable HTTP/2 or HTTP/3 on your server for significant performance improvements (multiplexing, header compression)."
                priority = "Medium"
                status = False # This was already here, ensuring pass is False if not http2/3
                score = 0
                recommendation = {"text": recommendation_text, "priority": priority}

            return {
                "pass": status,
                "protocol_used": protocol,
                "alt_svc_header": alt_svc,
                "supports_http2": is_http2,
                "supports_http3": is_http3,
                "recommendation": recommendation, # Use the recommendation variable
                "description": "Checks the HTTP protocol version used (HTTP/1.1, HTTP/2) and looks for HTTP/3 support via the Alt-Svc header.",
                "importance": "HTTP/2 and HTTP/3 offer significant performance advantages over HTTP/1.1, such as request multiplexing and header compression, leading to faster page loads.",
                "blog": "",
                "score": score
            }
        except httpx.TimeoutException as e:
             logger.error(f"[PERFORMANCE ANALYSIS] Timeout error checking HTTP protocols: {e}")
             return {
                 "pass": False,
                 "error": f"Timeout occurred during protocol check: {str(e)}",
                 "recommendation": {"text": "Could not verify HTTP protocol due to a timeout. Check network or proxy settings.", "priority": "Medium"},
                 "score": 0
            }
        except Exception as e:
            logger.error(f"[PERFORMANCE ANALYSIS] Error checking HTTP protocols: {e}")
            return {
                 "pass": False,
                 "error": f"Protocol check failed: {str(e)}",
                 "recommendation": {"text": "Unable to verify HTTP protocol due to an error.", "priority": "Medium"},
                 "score": 0
             }

    def analyze_image_optimisation(self):
        soup = BeautifulSoup(self.html_content, "lxml")
        imgs = soup.find_all("img")
        total = len(imgs)
        next_gen_formats = (".webp", ".avif", ".jxl")
        missing_alt = 0
        missing_dims = 0
        next_gen_count = 0
        problematic_images = []

        for img in imgs:
            src = img.get("src", "").lower()
            alt = img.get("alt")
            width = img.get("width")
            height = img.get("height")

            is_next_gen = src.endswith(next_gen_formats)
            if is_next_gen:
                next_gen_count += 1

            has_alt = alt is not None
            if not has_alt:
                missing_alt += 1

            has_dims = width and height and width.isdigit() and height.isdigit()
            if not has_dims:
                 missing_dims += 1

            if not is_next_gen or not has_alt or not has_dims:
                 if len(problematic_images) < 5:
                      problematic_images.append({
                          "src": img.get("src", "")[:100] + "..." if len(img.get("src", "")) > 100 else img.get("src", ""),
                          "alt_missing": not has_alt,
                          "dims_missing": not has_dims,
                          "not_next_gen": not is_next_gen
                      })

        passed_format = (total == 0 or next_gen_count / total > 0.8)
        passed_alt = (total == 0 or missing_alt / total < 0.1)
        passed_dims = (total == 0 or missing_dims / total < 0.1)

        status = passed_format and passed_alt and passed_dims
        recommendations = []
        priority = "Low"
        max_score = self.MAX_SCORES.get("analyze_image_optimisation", 15)
        score = max_score
        recommendation = None # Initialize recommendation to None

        if total > 0:
            if not passed_format:
                 recommendations.append(f"{total - next_gen_count} of {total} images are not using next-gen formats (WebP, AVIF). Convert images for better compression and faster loading.")
                 priority = "High"
                 score -= max_score * 0.4
            if not passed_alt:
                 recommendations.append(f"{missing_alt} of {total} images are missing 'alt' attributes. Add descriptive alt text for accessibility and SEO.")
                 if priority != "High": priority = "Medium"
                 score -= max_score * 0.3
            if not passed_dims:
                 recommendations.append(f"{missing_dims} of {total} images are missing explicit width/height attributes. Add dimensions to prevent layout shifts (CLS).")
                 if priority != "High": priority = "Medium"
                 score -= max_score * 0.3
        # Removed the else: pass block as it's not needed if recommendation logic handles the pass case.

        if not recommendations: # This means all checks passed or total_images was 0
            # recommendation_text = "Image optimisation looks good. All images seem to use next-gen formats, have alt text, and explicit dimensions."
            # priority = "Low" # Not needed if recommendation is None
            score = max_score # Ensure score is max if no issues
            recommendation = None # Explicitly None for pass = True
        else:
             recommendation_text = " ".join(recommendations)
             # Priority is already set based on the first failing check
             recommendation = {"text": recommendation_text, "priority": priority}

        return {
            "pass": status,
            "total_images": total,
            "next_gen_images_count": next_gen_count,
            "missing_alt_count": missing_alt,
            "missing_dimensions_count": missing_dims,
            "problematic_images_sample": problematic_images,
            "recommendation": recommendation, # Use the recommendation variable
            "description": "Checks if images use modern formats (WebP, AVIF), have 'alt' text, and specify dimensions.",
            "importance": "Optimized images load faster (improving LCP), alt text aids SEO and accessibility, and dimensions prevent layout shifts (improving CLS). These are crucial for user experience and Core Web Vitals.",
            "blog": "",
            "score": max(0, round(score))
        }

    def check_minification(self):
        soup = BeautifulSoup(self.html_content, "lxml")
        js_srcs = [t.get("src", "") for t in soup.find_all("script", src=True)]
        css_hrefs = [l.get("href", "") for l in soup.find_all("link", rel="stylesheet", href=True)]

        total_js = len(js_srcs)
        unminified_js_count = 0
        unminified_js_samples = []

        total_css = len(css_hrefs)
        unminified_css_count = 0
        unminified_css_samples = []

        for src in js_srcs:
            if src and '.min.' not in os.path.basename(src).lower():
                unminified_js_count += 1
                if len(unminified_js_samples) < 5:
                     unminified_js_samples.append(src)

        for href in css_hrefs:
            if href and '.min.' not in os.path.basename(href).lower():
                unminified_css_count += 1
                if len(unminified_css_samples) < 5:
                     unminified_css_samples.append(href)

        ok = (unminified_js_count == 0 and unminified_css_count == 0)
        priority = "Low"
        recommendation_text = ""
        max_score = self.MAX_SCORES.get("check_minification", 10)
        score = max_score if ok else 0

        if ok:
            recommendation_text = "All linked CSS and JS assets appear to be minified based on filename conventions."
            priority = "Low"
        else:
            issues = []
            if unminified_js_count > 0:
                 issues.append(f"{unminified_js_count} JS file(s) do not appear to be minified.")
            if unminified_css_count > 0:
                 issues.append(f"{unminified_css_count} CSS file(s) do not appear to be minified.")
            recommendation_text = " ".join(issues) + " Minify these assets to reduce file size and improve load times. Check build process or plugins."
            priority = "Medium"

        return {
            "pass": ok,
            "total_js": total_js,
            "unminified_js_count": unminified_js_count,
            "unminified_js_samples": unminified_js_samples,
            "total_css": total_css,
            "unminified_css_count": unminified_css_count,
            "unminified_css_samples": unminified_css_samples,
            "recommendation": {"text": recommendation_text, "priority": priority},
            "description": "Checks if linked JavaScript and CSS filenames suggest they are minified (contain '.min.').",
            "importance": "Minification removes unnecessary characters (whitespace, comments) from code, reducing file size and leading to faster downloads and parsing.",
            "blog": "",
            "score": score
        }