import re
import redis
import json
import os
import logging
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from django.conf import settings

logger = logging.getLogger(__name__)

MAX_SOCIAL_SCORES = {
    "facebook_page_url":      7,
    "facebook_pixel_id":      3,
    "og_tags_social":        20,
    "twitter_profile_url":    5,
    "twitter_card_tags":     10,
    "instagram_profile_url":  5,
    "linkedin_profile_url":   5,
    "youtube_channel_url":    5,
    "youtube_statistics":    10,
    "telegram_channel_url":   3,  # New: Telegram support
    "share_buttons":         15,
    "addthis_detected":       5,
    "sharethis_detected":     5
}

class SocialMediaAnalyzer:
    MAX_SCORES = MAX_SOCIAL_SCORES

    def __init__(self, html: str, url: str, task_id: str):
        self.soup = BeautifulSoup(html, "lxml")
        self.html = html
        self.url = url
        self.parsed_url = urlparse(url)
        self.domain = self.parsed_url.netloc
        self.task_id = task_id
        
        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=os.getenv('REDIS_PASSWORD', None),
                decode_responses=False
            )
            self.redis_client.ping()
            logger.info("Redis connection successful in SocialMediaAnalyzer")
            self.redis_available = True
        except (redis.exceptions.ConnectionError, AttributeError) as e:
            logger.warning(f"Redis connection failed in SocialMediaAnalyzer: {e}")
            self.redis_available = False
    
    def _save_to_redis(self, key, data):
        if self.redis_available:
            try:
                self.redis_client.hset(self.task_id, key, json.dumps(data))
                return True
            except Exception as e:
                logger.error(f"Failed to save to Redis: {e}")
        return False

    def _find_social_links(self, platform_patterns, exclude_patterns=None):
        """
        Improved social link detection using regex on full HTML content
        """
        found_links = set()
        
        # Search in full HTML content
        for pattern in platform_patterns:
            matches = re.findall(pattern, self.html, re.IGNORECASE)
            found_links.update(matches)
        
        # Filter out unwanted links (sharing, login, etc.)
        if exclude_patterns:
            filtered_links = []
            for link in found_links:
                is_excluded = False
                for exclude_pattern in exclude_patterns:
                    if re.search(exclude_pattern, link, re.IGNORECASE):
                        is_excluded = True
                        break
                if not is_excluded:
                    filtered_links.append(link)
            return filtered_links
        
        return list(found_links)

    def analyze_facebook(self):
        # Improved Facebook detection patterns
        fb_patterns = [
            r'https?://(?:www\.)?facebook\.com/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?',
            r'https?://(?:www\.)?fb\.com/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?'
        ]
        
        # Exclude sharing and generic links
        exclude_patterns = [
            r'(sharer|share\.php|login|photo\.php|dialog|plugins)',
            r'facebook\.com/tr\?',  # Pixel tracking
            r'facebook\.com/v\d+\.\d+/dialog'  # API dialogs
        ]
        
        fb_links = self._find_social_links(fb_patterns, exclude_patterns)
        fb_link = fb_links[0] if fb_links else None

        # Facebook Pixel detection (improved)
        fb_pixel_patterns = [
            r'fbq\s*\(\s*[\'"]init[\'"][\s,]*[\'"](\d+)[\'"]',
            r'facebook\.com/tr\?id=(\d+)',
            r'_fbp[\'"]?\s*:\s*[\'"]?(\d+)'
        ]
        
        fb_pixel_id = None
        for pattern in fb_pixel_patterns:
            match = re.search(pattern, self.html, re.IGNORECASE)
            if match:
                fb_pixel_id = match.group(1)
                break

        # Open Graph tags
        og_tags = {}
        for meta in self.soup.find_all('meta'):
            prop = meta.get('property') or meta.get('name')
            if prop and prop.startswith('og:'):
                content = meta.get('content', '').strip()
                if content:
                    og_tags[prop] = content

        passed_ideal = bool(fb_link) # Ideal pass is having the Facebook page link
        recommendation = None
        priority = "Low"

        if passed_ideal:
            recommendation = None # Pass is True, so no recommendation
        elif fb_pixel_id and not fb_link:
            recommendation_text = "Facebook Pixel detected but no profile link found. Add a visible link to your Facebook profile to improve social proof."
            priority = "Low"
            recommendation = {"text": recommendation_text, "priority": priority}
        elif og_tags and not fb_link:
            recommendation_text = "Open Graph tags detected but no Facebook profile link found. Add a visible link to your Facebook page to improve social proof and engagement."
            priority = "Medium"
            recommendation = {"text": recommendation_text, "priority": priority}
        else:
            recommendation_text = "No Facebook presence (profile link) detected. Consider creating and linking a Facebook business page to improve social signals and brand visibility."
            priority = "Medium"
            recommendation = {"text": recommendation_text, "priority": priority}

        page_score = self.MAX_SCORES.get("facebook_page_url", 7) if fb_link else 0
        pixel_score = self.MAX_SCORES.get("facebook_pixel_id", 3) if fb_pixel_id else 0

        total_facebook_score = page_score + pixel_score

        result = {
            "facebook": {
                "pass": passed_ideal,
                "page_url": fb_link if fb_link else "NOT FOUND",
                "pixel_id": fb_pixel_id,
                "description": "Facebook is the largest social network. A business presence helps with brand visibility and customer engagement.",
                "recommendation": recommendation,
                "importance": "Facebook presence can boost local SEO, brand visibility, and drive referral traffic.",
                "score": total_facebook_score,
            }
        }
        
        self._save_to_redis('facebook', result)
        return result

    def analyze_twitter(self):
        # Improved Twitter/X detection patterns
        twitter_patterns = [
            r'https?://(?:www\.)?twitter\.com/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?',
            r'https?://(?:www\.)?x\.com/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?'
        ]
        
        # Exclude sharing and generic links
        exclude_patterns = [
            r'(share|intent|status|tweet|login|signup)',
            r'twitter\.com/intent/',
            r'x\.com/intent/'
        ]
        
        tw_links = self._find_social_links(twitter_patterns, exclude_patterns)
        tw_link = tw_links[0] if tw_links else None

        # Twitter Card tags
        twitter_cards = {}
        for meta in self.soup.find_all('meta'):
            name = meta.get('name')
            if name and name.lower().startswith("twitter:"):
                content = meta.get('content', '').strip()
                if content:
                    twitter_cards[name] = content

        passed_ideal = bool(tw_link) # Ideal pass is having the Twitter/X profile link
        recommendation = None
        priority = "Low"

        if passed_ideal:
            recommendation = None # Pass is True, so no recommendation
        elif twitter_cards and not tw_link:
            recommendation_text = "Twitter Card meta tags detected but no profile link found. Add a visible link to your Twitter/X profile."
            priority = "Medium"
            recommendation = {"text": recommendation_text, "priority": priority}
        else:
            recommendation_text = "No Twitter/X presence (profile link) detected. Consider creating an account for updates, engagement, and social signals."
            priority = "Medium"
            recommendation = {"text": recommendation_text, "priority": priority}

        profile_score = self.MAX_SCORES.get("twitter_profile_url", 5) if tw_link else 0
        total_twitter_score = profile_score

        result = {
            "twitter": {
                "pass": passed_ideal,
                "profile_url": tw_link if tw_link else "NOT FOUND",
                "description": "Twitter (X) is a real-time information network for diverse conversations.",
                "recommendation": recommendation,
                "importance": "Twitter helps with brand visibility, content distribution, customer service, and social signals.",
                "score": total_twitter_score,
            }
        }
        
        self._save_to_redis('twitter', result)
        return result

    def analyze_instagram(self):
        # Improved Instagram detection patterns
        instagram_patterns = [
            r'https?://(?:www\.)?instagram\.com/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?'
        ]
        
        # Exclude sharing and generic links
        exclude_patterns = [
            r'(/p/|login|explore|reel|stories|guide|share)',
            r'instagram\.com/accounts/',
            r'instagram\.com/embed'
        ]
        
        insta_links = self._find_social_links(instagram_patterns, exclude_patterns)
        insta_link = insta_links[0] if insta_links else None

        passed_ideal = bool(insta_link)
        recommendation = None
        priority = "Low"

        if passed_ideal:
            recommendation = None
        else:
            recommendation_text = "No Instagram presence detected. Consider creating an account if your business is visually oriented."
            priority = "Low"
            recommendation = {"text": recommendation_text, "priority": priority}

        profile_score = self.MAX_SCORES.get("instagram_profile_url", 5) if insta_link else 0
        total_instagram_score = profile_score

        result = {
            "instagram": {
                "pass": passed_ideal,
                "profile_url": insta_link if insta_link else "NOT FOUND",
                "description": "Instagram is a visual platform for showcasing products, services, and company culture.",
                "recommendation": recommendation,
                "importance": "Instagram is valuable for visual brands, building identity, and audience engagement through imagery.",
                "score": total_instagram_score,
            }
        }
        
        self._save_to_redis('instagram', result)
        return result

    def analyze_linkedin(self):
        # Improved LinkedIn detection patterns
        linkedin_patterns = [
            r'https?://(?:www\.)?linkedin\.com/(?:in|company)/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?'
        ]
        
        # Exclude sharing and generic links
        exclude_patterns = [
            r'(share|login|pulse|feed|shareArticle|jobs|learning)',
            r'linkedin\.com/sharing/',
            r'linkedin\.com/uas/login'
        ]
        
        li_links = self._find_social_links(linkedin_patterns, exclude_patterns)
        li_link = li_links[0] if li_links else None

        passed_ideal = bool(li_link)
        recommendation = None
        priority = "Low"

        if passed_ideal:
            recommendation = None
        else:
            recommendation_text = "No LinkedIn presence detected. Consider a company page for industry authority and B2B visibility."
            priority = "Medium"
            recommendation = {"text": recommendation_text, "priority": priority}

        profile_score = self.MAX_SCORES.get("linkedin_profile_url", 5) if li_link else 0
        total_linkedin_score = profile_score

        result = {
            "linkedin": {
                "pass": passed_ideal,
                "profile_url": li_link if li_link else "NOT FOUND",
                "description": "LinkedIn is a professional networking platform valuable for B2B marketing.",
                "recommendation": recommendation,
                "importance": "LinkedIn establishes credibility, improves B2B marketing, and builds business relationships.",
                "score": total_linkedin_score,
            }
        }
        
        self._save_to_redis('linkedin', result)
        return result

    def analyze_youtube(self):
        # Improved YouTube detection patterns
        youtube_patterns = [
            r'https?://(?:www\.)?youtube\.com/(?:channel|user|c)/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?',
            r'https?://(?:www\.)?youtube\.com/@[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?',
            r'https?://(?:www\.)?youtu\.be/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?'
        ]
        
        # Exclude sharing and generic links
        exclude_patterns = [
            r'(watch\?|embed|playlist|share|shorts)',
            r'youtube\.com/watch\?v=',
            r'youtube\.com/embed/'
        ]
        
        yt_links = self._find_social_links(youtube_patterns, exclude_patterns)
        yt_link = yt_links[0] if yt_links else None

        passed_link = bool(yt_link)
        channel_id = None
        channel_name = None
        if yt_link:
            if '/channel/' in yt_link:
                channel_id = yt_link.split('/channel/')[1].split('/')[0].split('?')[0]
            elif '/user/' in yt_link:
                channel_name = yt_link.split('/user/')[1].split('/')[0].split('?')[0]
            elif '/c/' in yt_link:
                channel_name = yt_link.split('/c/')[1].split('/')[0].split('?')[0]
            elif '/@' in yt_link:
                channel_name = yt_link.split('/@')[1].split('/')[0].split('?')[0]
        
        recommendation_text_parts = []
        priority = "Low"

        if not passed_link:
            recommendation_text_parts.append("No YouTube channel detected. Consider creating one for video content like demos or tutorials.")
        
        channel_score = self.MAX_SCORES.get("youtube_channel_url", 5) if yt_link else 0

        api_key = os.getenv('YOUTUBE_API_KEY')
        stats = {}
        stats_score = 0
        stats_fetched = False
        
        if passed_link:
            if api_key and (channel_id or channel_name):
                try:
                    import httpx
                    params = {'part': 'statistics', 'key': api_key}
                    if channel_id:
                        params['id'] = channel_id
                    else:
                        params['forUsername'] = channel_name
                    with httpx.Client(http2=True, timeout=60.0) as client:
                        resp = client.get('https://www.googleapis.com/youtube/v3/channels', params=params)
                        resp.raise_for_status()
                        data = resp.json()
                        items = data.get('items', [])
                        if items:
                            stats = items[0].get('statistics', {})
                            stats_fetched = True
                            stats_score = self.MAX_SCORES.get("youtube_statistics", 10)
                            if int(stats.get('videoCount', 0)) == 0:
                                recommendation_text_parts.append("The linked YouTube channel has no videos; add content to leverage this platform.")
                                if priority == "Low": priority = "Medium"
                            elif int(stats.get('subscriberCount', 0)) < 50:
                                recommendation_text_parts.append("The linked YouTube channel has few subscribers. Consider strategies to grow your subscriber base.")
                        else:
                            recommendation_text_parts.append("Linked YouTube channel found, but could not retrieve statistics (channel not found via API or no items returned).")
                            if priority == "Low": priority = "Medium"
                            stats_score = 0 
                except Exception as e:
                    logger.error(f"[SOCIAL ANALYSIS] Error fetching YouTube stats via httpx: {e}")
                    recommendation_text_parts.append("Could not fetch YouTube channel statistics due to an API error.")
                    if priority == "Low": priority = "Medium"
                    stats_score = 0 
            elif not api_key and (channel_id or channel_name):
                recommendation_text_parts.append("Linked YouTube channel found. Add YOUTUBE_API_KEY to environment to check channel statistics.")
                stats_score = 0
        else:
            stats_score = 0

        if not passed_link:
            if not recommendation_text_parts:
                 recommendation_text_parts.append("No YouTube channel detected. Consider creating one for video content like demos or tutorials.")
            recommendation = {"text": " ".join(recommendation_text_parts), "priority": priority}
        elif recommendation_text_parts:
            recommendation = {"text": " ".join(recommendation_text_parts), "priority": priority}
        else:
            recommendation = None
        
        final_pass_status = passed_link
        total_youtube_score = channel_score + stats_score

        result = {
            "youtube": {
                "pass": final_pass_status,
                "channel_url": yt_link if yt_link else "NOT FOUND",
                "channel_id": channel_id,
                "channel_name": channel_name,
                "description": "YouTube is the second largest search engine, ideal for video marketing.",
                "recommendation": recommendation,
                "importance": "A YouTube channel can boost SEO and engagement, especially when videos are embedded or linked.",
                "blog": "",
                "statistics": {
                    'subscriberCount': int(stats.get('subscriberCount', 0)),
                    'videoCount': int(stats.get('videoCount', 0)),
                    'viewCount': int(stats.get('viewCount', 0)),
                },
                "score": total_youtube_score,
            }
        }
        
        self._save_to_redis('youtube', result)
        return result

    def analyze_telegram(self):
        # Telegram detection patterns
        telegram_patterns = [
            r'https?://(?:www\.)?telegram\.me/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?',
            r'https?://(?:www\.)?t\.me/[a-zA-Z0-9._-]+/?(?:\?[^"\s]*)?'
        ]
        
        # No major exclusions needed for Telegram
        exclude_patterns = []
        
        tg_links = self._find_social_links(telegram_patterns, exclude_patterns)
        tg_link = tg_links[0] if tg_links else None

        passed_ideal = bool(tg_link)
        recommendation = None
        priority = "Low"

        if passed_ideal:
            recommendation = None
        else:
            recommendation_text = "No Telegram presence detected. Consider creating a channel for direct communication with your audience."
            priority = "Low"
            recommendation = {"text": recommendation_text, "priority": priority}

        channel_score = self.MAX_SCORES.get("telegram_channel_url", 3) if tg_link else 0
        total_telegram_score = channel_score

        result = {
            "telegram": {
                "pass": passed_ideal,
                "channel_url": tg_link if tg_link else "NOT FOUND",
                "description": "Telegram is a messaging platform popular for channels and group communication.",
                "recommendation": recommendation,
                "importance": "Telegram channels can provide direct communication with your audience and build community.",
                "score": total_telegram_score,
            }
        }
        
        self._save_to_redis('telegram', result)
        return result
        
    def analyze_meta_tags_social(self):
        
        og_tags = {}
        for meta in self.soup.find_all('meta'):
            prop = meta.get('property') or meta.get('name')
            if prop and prop.startswith('og:'):
                content = meta.get('content', '').strip()
                if content: # Ensure content is not empty
                    og_tags[prop] = content
                    
        twitter_tags = {}
        for meta in self.soup.find_all('meta'):
            name = meta.get('name')
            if name and name.lower().startswith("twitter:"):
                content = meta.get('content', '').strip()
                if content: # Ensure content is not empty
                    twitter_tags[name] = content
        
        critical_og = ['og:title', 'og:description', 'og:image', 'og:url', 'og:type']
        missing_og = [tag for tag in critical_og if tag not in og_tags or not og_tags[tag]]
        
        critical_twitter = ['twitter:card', 'twitter:title', 'twitter:description']
        missing_twitter = [tag for tag in critical_twitter if tag not in twitter_tags or not twitter_tags[tag]]
        
        # Ideal pass: all critical OG tags AND all critical Twitter tags are present and have content.
        passed_ideal = not missing_og and not missing_twitter and bool(og_tags) and bool(twitter_tags)
        
        recommendation = None
        priority = "Low" # Default
        recommendation_text_parts = []
        
        if passed_ideal:
            recommendation = None # Pass is True, so no recommendation
        else:
            if not og_tags:
                recommendation_text_parts.append("No Open Graph (OG) tags detected.")
                priority = "High"
            elif missing_og:
                recommendation_text_parts.append(f"Missing critical Open Graph tags: {', '.join(missing_og)}.")
                if priority != "High": priority = "Medium"
            
            if not twitter_tags:
                recommendation_text_parts.append("No Twitter Card tags detected.")
                priority = "High"
            elif missing_twitter:
                recommendation_text_parts.append(f"Missing critical Twitter Card tags: {', '.join(missing_twitter)}.")
                if priority != "High": priority = "Medium"

            if recommendation_text_parts:
                 recommendation_text_parts.append("Implement these tags for better social sharing appearance.")
                 recommendation = {"text": " ".join(recommendation_text_parts), "priority": priority}
            elif not og_tags and not twitter_tags:
                 recommendation_text = "No social meta tags detected. Add Open Graph and Twitter Card tags to improve content appearance when shared on social platforms."
                 priority = "High"
                 recommendation = {"text": recommendation_text, "priority": priority}

        og_max_score = self.MAX_SCORES.get("og_tags_social", 20)
        og_score = 0
        if og_tags:
            og_score_per_tag = og_max_score / len(critical_og)
            present_og_score = sum(og_score_per_tag for tag in critical_og if tag in og_tags and og_tags[tag])
            og_score = round(present_og_score)
            if not missing_og:
                og_score = og_max_score

        twitter_max_score = self.MAX_SCORES.get("twitter_card_tags", 10)
        twitter_score = 0
        if twitter_tags:
            twitter_score_per_tag = twitter_max_score / len(critical_twitter)
            present_twitter_score = sum(twitter_score_per_tag for tag in critical_twitter if tag in twitter_tags and twitter_tags[tag])
            twitter_score = round(present_twitter_score)
            if not missing_twitter:
                 twitter_score = twitter_max_score

        total_meta_score = og_score + twitter_score

        result = {
            "social_meta_tags": {
                "pass": passed_ideal,
                "og_tags_present": bool(og_tags),
                "og_tags_count": len(og_tags),
                "og_tags": og_tags,
                "missing_og_tags": missing_og,
                "twitter_tags_present": bool(twitter_tags),
                "twitter_tags_count": len(twitter_tags),
                "twitter_tags": twitter_tags,
                "missing_twitter_tags": missing_twitter,
                "description": "Social meta tags (Open Graph, Twitter Cards) control how content appears when shared, influencing clicks.",
                "recommendation": recommendation,
                "importance": "Proper social tags increase visibility and engagement when content is shared, driving referral traffic.",
                "score": total_meta_score,
            }
        }
        
        self._save_to_redis('social_meta_tags', result)
        return result
    
    def analyze_share_buttons(self):
        
        share_patterns = [
            r'share', r'social-', r'social_', r'socialshare', r'addthis', r'sharethis',
            r'facebook', r'twitter', r'linkedin', r'pinterest', r'reddit', r'whatsapp'
        ]
        
        share_elements_html = [] # Store HTML snippets for unique check
        
        for pattern in share_patterns:
            # Find by class
            elements_by_class = self.soup.find_all(class_=re.compile(pattern, re.IGNORECASE))
            for el in elements_by_class:
                share_elements_html.append(str(el)[:150]) # Store a snippet

            # Find by ID
            elements_by_id = self.soup.find_all(id=re.compile(pattern, re.IGNORECASE))
            for el in elements_by_id:
                share_elements_html.append(str(el)[:150]) # Store a snippet
        
        # Find by specific href patterns in <a> tags
        for a_tag in self.soup.find_all('a', href=True):
            href = a_tag['href'].lower()
            if any(service_keyword in href for service_keyword in [
                'facebook.com/sharer/sharer.php', 'facebook.com/share.php', # More specific FB share
                'twitter.com/intent/tweet', 'twitter.com/share',            # More specific Twitter share
                'linkedin.com/sharearticle', 'linkedin.com/sharing/share-offsite', # More specific LinkedIn
                'pinterest.com/pin/create/button',                          # More specific Pinterest
                'reddit.com/submit',
                'whatsapp://send', 'api.whatsapp.com/send'
            ]):
                share_elements_html.append(str(a_tag)[:150])
        
        # Check for AddThis and ShareThis scripts
        addthis_script_detected = bool(re.search(r'(addthis\.com/js/|addthis_widget\.js)', self.html, re.IGNORECASE))
        sharethis_script_detected = bool(re.search(r'(platform-api\.sharethis\.com|sharethis\.com/button)', self.html, re.IGNORECASE))
        
        # Count unique identified share elements (based on their HTML snippet)
        unique_share_instances_count = len(set(share_elements_html))
        
        # Overall status: True if any kind of sharing mechanism is detected
        passed_ideal_generic_buttons = bool(unique_share_instances_count > 0 or addthis_script_detected or sharethis_script_detected)
        
        recommendation_generic = None
        if not passed_ideal_generic_buttons:
            recommendation_generic = {
                "text": "No social sharing buttons or services (like AddThis/ShareThis) detected. Consider adding them to increase content visibility through social sharing, especially for blog posts and key pages.", 
                "priority": "Medium"
            }

        recommendation_addthis = None
        if not addthis_script_detected:
            recommendation_addthis = {"text": "Consider using a sharing service like AddThis if you need a wide range of sharing options easily implemented.", "priority": "Low"}
        
        recommendation_sharethis = None
        if not sharethis_script_detected:
            recommendation_sharethis = {"text": "ShareThis offers an alternative for easy social sharing button integration.", "priority": "Low"}

        final_results_for_sharing = {
            "share_buttons": {
                "pass": passed_ideal_generic_buttons,
                "button_count": unique_share_instances_count,
                "description": "Generic social sharing buttons allow users to easily distribute your content.",
                "recommendation": recommendation_generic, 
                "importance": "Social sharing increases content visibility and referral traffic.",
                "score": self.MAX_SCORES.get("share_buttons", 15) if passed_ideal_generic_buttons else 0,
            },
            "addthis_detected": {
                "pass": addthis_script_detected,
                "description": "AddThis is a popular third-party service for social sharing buttons.",
                "recommendation": recommendation_addthis,
                "importance": "AddThis provides comprehensive sharing tools and analytics.",
                "score": self.MAX_SCORES.get("addthis_detected", 5) if addthis_script_detected else 0,
            },
            "sharethis_detected": {
                "pass": sharethis_script_detected,
                "description": "ShareThis is another widely used service for implementing social sharing functionalities.",
                "recommendation": recommendation_sharethis,
                "importance": "ShareThis can simplify adding sharing buttons and tracking their usage.",
                "score": self.MAX_SCORES.get("sharethis_detected", 5) if sharethis_script_detected else 0,
            }
        }

        self._save_to_redis('share_buttons', final_results_for_sharing["share_buttons"])
        self._save_to_redis('addthis_detected', final_results_for_sharing["addthis_detected"])
        self._save_to_redis('sharethis_detected', final_results_for_sharing["sharethis_detected"])
        return final_results_for_sharing
        
    def run_all(self):
        
        logger.info(f"Starting social media analysis for {self.url}")
        
        results = {}
        results.update(self.analyze_facebook())
        results.update(self.analyze_twitter())
        results.update(self.analyze_instagram())
        results.update(self.analyze_linkedin())
        results.update(self.analyze_youtube())
        results.update(self.analyze_telegram())  # NEW: Add Telegram analysis
        results.update(self.analyze_meta_tags_social())
        results.update(self.analyze_share_buttons())
        
        total_score = 0
        total_possible = sum(self.MAX_SCORES.values())
        
        for category_key, category_data in results.items():
            if category_key == 'total_score':
                continue
            if isinstance(category_data, dict):
                total_score += category_data.get('score', 0)
        
        percentage = round((total_score / total_possible) * 100) if total_possible > 0 else 0

        if percentage >= 98:
            grade = 'A+'
        elif percentage >= 95:
            grade = 'A'
        elif percentage >= 90:
            grade = 'A-'
        elif percentage >= 85:
            grade = 'B+'
        elif percentage >= 80:
            grade = 'B'
        elif percentage >= 75:
            grade = 'B-'
        elif percentage >= 70:
            grade = 'C+'
        elif percentage >= 60:
            grade = 'C'
        elif percentage >= 50:
            grade = 'C-'
        elif percentage >= 40:
            grade = 'D'
        else: 
            grade = 'F'

        results['total_score'] = {
            'score': total_score,
            'grade': grade
        }
        
        if grade in ['A+', 'A', 'A-']:
            overall_title = "Your Social Presence is Excellent"
            overall_description = "Your website demonstrates excellent social media integration and visibility. Only minor improvements may be needed."
        elif grade in ['B+', 'B', 'B-']:
            overall_title = "Your Social Presence is Good"
            overall_description = "Your website has good social media presence, but there are some areas that could be improved for even better engagement and reach."
        elif grade in ['C+', 'C', 'C-']:
            overall_title = "Your Social Presence Could Be Better"
            overall_description = "Your website has some social media integration, but there are several areas that need attention to improve visibility and engagement."
        elif grade == 'D':
            overall_title = "Your Social Presence Needs Improvement"
            overall_description = "Your website has significant social media issues. Address the key recommendations to improve your brand's reach and engagement."
        else:
            overall_title = "Your Social Presence is Poor"
            overall_description = "Your website is missing critical social media elements. Immediate action is required to improve your brand's visibility and engagement."

        results['overall_title'] = overall_title
        results['overall_description'] = overall_description
        
        logger.info(f"Completed social media analysis for {self.url}. Score: {total_score}/{total_possible} ({percentage}%, Grade: {grade})")
        return results


def extract_facebook_data(html: str) -> dict:
    soup = BeautifulSoup(html, 'lxml')
    
    og_tags = {}
    for meta in soup.find_all('meta'):
        prop = meta.get('property') or meta.get('name')
        if prop and prop.startswith('og:'):
            content = meta.get('content', '').strip()
            if content:
                og_tags[prop] = content

    fb_link = None
    for a in soup.find_all('a', href=True):
        href = a['href']
        if "facebook.com" in href.lower():
            if not re.search(r"(sharer|share\.php|login|photo\.php)", href, re.IGNORECASE):
                fb_link = href
                break

    fb_pixel_match = re.search(r'fbq\(\s*[\'"]init[\'"]\s*,\s*[\'"](\d+)[\'"]\)', html)
    fb_pixel_id = fb_pixel_match.group(1) if fb_pixel_match else None

    status = bool(fb_link or fb_pixel_id or og_tags)

    return {
        "pass": status,
        "page_url": fb_link,
        "pixel_id": fb_pixel_id,
        "og_tags": og_tags,
    }

def extract_twitter_data(html: str) -> dict:
    soup = BeautifulSoup(html, 'lxml')
    
    twitter_cards = {}
    for meta in soup.find_all('meta'):
        name = meta.get('name')
        if name and name.lower().startswith("twitter:"):
            content = meta.get('content', '').strip()
            if content:
                twitter_cards[name] = content

    tw_link = None
    for a in soup.find_all('a', href=True):
        href = a['href']
        if re.search(r"(twitter\\.com|x\\.com)", href, re.IGNORECASE):
            if not re.search(r"(share|intent|status|tweet|login|signup)", href, re.IGNORECASE):
                tw_link = href
                break

    status = bool(tw_link or twitter_cards)
    return {
        "pass": status,
        "profile_url": tw_link,
        "twitter_cards": twitter_cards,
    }

def extract_instagram_data(html: str) -> dict:
    soup = BeautifulSoup(html, 'lxml')
    
    insta_link = None
    for a in soup.find_all('a', href=True):
        href = a['href']
        if "instagram.com" in href.lower():
            if not re.search(r"/p/|login|explore|reel|stories|guide|share", href, re.IGNORECASE):
                insta_link = href
                break

    status = bool(insta_link)
    return {
        "pass": status,
        "profile_url": insta_link,
    }

def extract_linkedin_data(html: str) -> dict:
    soup = BeautifulSoup(html, 'lxml')
    
    li_link = None
    for a in soup.find_all('a', href=True):
        href = a['href']
        if "linkedin.com" in href.lower():
            if re.search(r"linkedin\.com/(in|company)/", href, re.IGNORECASE):
                if not re.search(r"(share|login|pulse|feed|shareArticle|jobs|learning)", href, re.IGNORECASE):
                    li_link = href
                    break

    status = bool(li_link)
    return {
        "pass": status,
        "profile_url": li_link,
    }

def extract_youtube_data(html: str) -> dict:
    soup = BeautifulSoup(html, 'lxml')
    
    yt_link = None
    for a in soup.find_all('a', href=True):
        href = a['href']
        if re.search(r"(youtube\\.com|youtu\\.be)", href, re.IGNORECASE):
            if re.search(r"(channel|user|c)/", href, re.IGNORECASE) and not re.search(r"(watch\\?|embed|playlist|share|shorts)", href, re.IGNORECASE):
                yt_link = href
                break

    status = bool(yt_link)
    channel_id = None
    channel_name = None
    if yt_link:
        if '/channel/' in yt_link:
            channel_id = yt_link.split('/channel/')[1].split('/')[0].split('?')[0]
        elif '/user/' in yt_link:
            channel_name = yt_link.split('/user/')[1].split('/')[0].split('?')[0]
        elif '/c/' in yt_link:
            channel_name = yt_link.split('/c/')[1].split('/')[0].split('?')[0]
    
    return {
        "pass": status,
        "channel_url": yt_link,
        "channel_id": channel_id,
        "channel_name": channel_name,
    }