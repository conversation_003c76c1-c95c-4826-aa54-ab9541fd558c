import httpx
import os
import logging
import json
from urllib.parse import urlparse
import re

logger = logging.getLogger(__name__)

class PageSpeedDesktopAnalyzer:
    def __init__(self, url: str, task_id: str):
        if not url.lower().startswith(('http://', 'https://')):
            url = 'https://' + url
        self.url = url

        self.task_id = task_id
        self.api_key = os.getenv('PAGESPEED_API_KEY')
        self.base_url = "https://www.googleapis.com/pagespeedonline/v5/runPagespeed"

    async def run_pagespeed_analysis(self) -> dict:
        if not self.api_key:
            logger.error(f"[PAGESPEED DESKTOP] API key not found for Task ID: {self.task_id}")
            return {"error": "PageSpeed API key is missing."}

        params = {
            'url': self.url,
            'key': self.api_key,
            'strategy': 'desktop',
            'category': 'performance',
        }

        logger.info(f"[PAGESPEED DESKTOP] Requesting analysis for {self.url} | Task ID: {self.task_id}")

        timeout = 60.0

        try:
            async with httpx.AsyncClient(http2=True, timeout=timeout) as client:
                response = await client.get(self.base_url, params=params)
                response.raise_for_status()
                data = response.json()
                logger.info(f"[PAGESPEED DESKTOP] API call successful for {self.url} | Task ID: {self.task_id}")
                return self._extract_metrics(data)

        except httpx.ReadTimeout:
            logger.error(f"[PAGESPEED DESKTOP] ReadTimeout after {timeout}s for {self.url} | Task ID: {self.task_id}")
            return {"error": "PageSpeed API read timeout; try increasing the timeout or checking network connectivity."}
        except httpx.ConnectTimeout:
            logger.error(f"[PAGESPEED DESKTOP] ConnectTimeout for {self.url} | Task ID: {self.task_id}")
            return {"error": "PageSpeed API connect timeout; network may be unreachable."}
        except httpx.HTTPStatusError as e:
            logger.error(
                f"[PAGESPEED DESKTOP] HTTP Status Error for {self.url}: "
                f"{e.response.status_code} - {e.response.text} | Task ID: {self.task_id}"
            )
            try:
                err = e.response.json().get('error', {}).get('message', e.response.text)
            except json.JSONDecodeError:
                err = e.response.text
            return {"error": f"PageSpeed API returned status {e.response.status_code}: {err}"}
        except httpx.RequestError as e:
            logger.error(f"[PAGESPEED DESKTOP] RequestError ({e.__class__.__name__}) for {self.url}: {e!r} | Task ID: {self.task_id}")
            return {"error": f"{e.__class__.__name__}: {e}"}
        except json.JSONDecodeError as e:
            logger.error(f"[PAGESPEED DESKTOP] JSON Decode Error for {self.url}: {e} | Task ID: {self.task_id}")
            return {"error": "Failed to parse PageSpeed API response."}
        except Exception as e:
            logger.error(
                f"[PAGESPEED DESKTOP] Unexpected error during analysis for {self.url}: {e} | Task ID: {self.task_id}",
                exc_info=True
            )
            return {"error": f"An unexpected error occurred: {e}"}

    def _extract_metrics(self, data: dict) -> dict:
        try:
            lh = data.get('lighthouseResult', {})
            if not lh:
                logger.warning(f"[PAGESPEED DESKTOP] Missing 'lighthouseResult' for Task ID: {self.task_id}")
                return {"error": "Incomplete PageSpeed data received."}

            audits = lh.get('audits', {})
            perf_score = lh.get('categories', {}).get('performance', {}).get('score')

            score_display = int(perf_score * 100) if perf_score is not None else None

            def get_sec(metric_id):
                m = audits.get(metric_id, {}) or {}
                if 'numericValue' in m:
                    return round(m['numericValue'] / 1000, 2)
                display = m.get('displayValue', '')
                match = re.match(r'([0-9.]+)\s*(ms|s)', display)
                if match:
                    val_str, unit = match.groups()
                    val = float(val_str)
                    return round(val / 1000, 2) if unit == 'ms' else round(val, 2)
                return None

            def get_savings(audit_id):
                a = audits.get(audit_id, {})
                d = a.get('details', {})
                return round(d.get('overallSavingsMs', 0) / 1000, 2) if 'overallSavingsMs' in d else None

            fcp = get_sec('first-contentful-paint')
            si  = get_sec('speed-index')
            lcp = get_sec('largest-contentful-paint')
            tti = get_sec('interactive')
            tbt = get_sec('total-blocking-time')
            cls = round(audits.get('cumulative-layout-shift', {}).get('numericValue', 0), 3)
            inp = get_sec('interaction-to-next-paint')

            opportunities = {}
            for audit_id, audit in audits.items():
                if audit.get('details') and 'overallSavingsMs' in audit['details'] and audit['details']['overallSavingsMs'] > 100:
                    savings = get_savings(audit_id)
                    if savings:
                        title = audit.get('title', audit_id)
                        description = audit.get('description', '')
                        if 'Reduce unused JavaScript' in title:
                            title = 'Reduce unused JavaScript'
                        elif 'Eliminate render-blocking resources' in title:
                            title = 'Eliminate render-blocking resources'
                        elif 'Properly size images' in title:
                            title = 'Properly size images'
                        elif 'Serve images in next-gen formats' in title:
                            title = 'Serve images in next-gen formats'
                        elif 'Reduce initial server response time' in title:
                            title = 'Reduce server response time (TTFB)'

                        opportunities[title] = savings

            top_opportunities = dict(sorted(opportunities.items(), key=lambda item: item[1], reverse=True)[:5])

            core_ok = lcp is not None and lcp < 2.5 and inp is not None and inp < 0.2 and cls is not None and cls < 0.1
            core_rec_text = ""
            core_priority = "Low"
            core_recommendation = None

            if core_ok:
                core_rec_text = "Core Web Vitals for desktop are good (LCP < 2.5s, INP < 0.2s, CLS < 0.1)."
                core_priority = "Low"
                core_recommendation = {"text": core_rec_text, "priority": core_priority}
            else:
                failing_vitals = []
                if lcp is None or lcp >= 2.5: failing_vitals.append(f"LCP ({lcp}s)")
                if inp is None or inp >= 0.2: failing_vitals.append(f"INP ({inp}s)")
                if cls is None or cls >= 0.1: failing_vitals.append(f"CLS ({cls})")
                core_rec_text = f"Focus on improving failing Core Web Vitals: {', '.join(failing_vitals)}. Aim for LCP < 2.5s, INP < 0.2s, and CLS < 0.1."
                core_priority = "High"
                core_recommendation = None

            core_web_vitals_result = {
                    "pass": core_ok,
                    "Largest Contentful Paint (LCP)": lcp,
                    "Interaction to Next Paint (INP)": inp,
                    "Cumulative Layout Shift (CLS)": cls,
                    "description": "Core Web Vitals (LCP, INP, CLS) measure key aspects of user experience: loading, interactivity, and visual stability.",
                    "importance": "Google uses Core Web Vitals as a ranking signal. Good scores indicate a positive user experience, which benefits SEO.",
                    "recommendation": core_recommendation
            }

            desktop_ok = perf_score is not None and perf_score >= 0.9
            desktop_priority = "Low"
            desktop_rec_text = ""
            desktop_recommendation = None

            if desktop_ok:
                desktop_rec_text = f"Desktop performance score is excellent ({score_display})."
                desktop_priority = "Low"
                desktop_recommendation = {"text": desktop_rec_text, "priority": desktop_priority}
            else:
                desktop_recommendation = None

            desktop_performance_result = {
                    "pass": desktop_ok,
                    "performance_score": score_display,
                    "First Contentful Paint (FCP)": fcp,
                    "Speed Index (SI)": si,
                    "Largest Contentful Paint (LCP)": lcp,
                    "Time to Interactive (TTI)": tti,
                    "Total Blocking Time (TBT)": tbt,
                    "Cumulative Layout Shift (CLS)": cls,
                    "Interaction To Next Paint (INP)": inp,
                    "top_opportunities_ms_savings": top_opportunities,
                    "recommendation": desktop_recommendation,
                    "description": "Overall desktop performance score and key metrics influencing it, based on Google Lighthouse.",
                    "importance": "Desktop performance impacts user satisfaction and SEO. Addressing the listed opportunities can significantly speed up your page.",
                    "blog": "",
            }

            return {
                "core_web_vitals_desktop": core_web_vitals_result,
                "performance_desktop": desktop_performance_result,
            }
        except KeyError as e:
            logger.error(
                f"[PAGESPEED DESKTOP] Missing key {e} in PageSpeed data for Task ID: {self.task_id}"
            )
            return {"error": f"Failed to extract PageSpeed data: Missing key {e}"}
        except Exception as e:
            logger.error(
                f"[PAGESPEED DESKTOP] Error extracting metrics for {self.url}: {e} | Task ID: {self.task_id}",
                exc_info=True
            )
            return {"error": f"Unexpected error processing PageSpeed data: {e}"}

    async def run_all(self) -> dict:
        return await self.run_pagespeed_analysis()
