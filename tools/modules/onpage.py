import redis
import json
import os
import re
import asyncio
import httpx
import logging
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from django.conf import settings
import spacy
from collections import Counter
from typing import Optional

logger = logging.getLogger(__name__)

MAX_SCORES = {
    "title_tag": 9,
    "meta_description": 5,
    "serp_preview": 5,
    "language_declared": 3,
    "h1_header": 7,
    "h2_h6_headers": 6,
    "keyword_consistency": 9,
    "content_amount": 8,
    "image_alt_attributes": 6,
    "canonical_tag": 4,
    "noindex_tag": 2,
    "noindex_header": 2,
    "ssl_enabled": 5,
    "https_redirect": 5,
    "robots_txt": 4,
    "blocked_by_robots_txt": 3,
    "xml_sitemap": 5,
    "analytics": 4,
    "schema_markup": 5,
}

class OnPageSEOAnalyzer:
    def __init__(self, html: str, url: str, task_id: str, proxy_string: Optional[str] = None):
        self.original_html = html
        self.soup = BeautifulSoup(html, "lxml")
        self.url = url
        self.parsed_url = urlparse(url)
        self.domain = self.parsed_url.netloc
        self.task_id = task_id
        self.proxy_string = proxy_string

        try:
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=os.getenv('REDIS_PASSWORD', None),
                decode_responses=False
            )
            self.redis_client.ping()
            logger.info("Redis connection successful in OnPageSEOAnalyzer")
            self.redis_available = True
        except (redis.exceptions.ConnectionError, AttributeError) as e:
            logger.warning(f"Redis connection failed in OnPageSEOAnalyzer: {e}")
            self.redis_available = False

    def _save_to_redis(self, key, data):
        if self.redis_available:
            try:
                self.redis_client.hset(self.task_id, key, json.dumps(data))
                return True
            except Exception as e:
                logger.error(f"Failed to save to Redis: {e}")
        return False

    def analyze_title_tag(self):
        title_element = self.soup.title
        title_text = title_element.get_text(" ", strip=True) if title_element else ''
        title_length = len(title_text)
        recommendation_text = ""
        priority = "Low"

        if not title_text:
            recommendation_text = "Your page is missing a title tag. Add a descriptive title (50-60 characters) with primary keywords at the beginning."
            priority = "High"
        elif title_length < 10:
            recommendation_text = f"Your title is too short ({title_length} characters). Expand it to 50-60 characters, prioritize primary keywords first, and use engaging language."
            priority = "High"
        elif title_length < 50:
            recommendation_text = f"Your title ({title_length} characters) is shorter than optimal. Add descriptive content to reach 50-60 characters while keeping key phrases toward the front."
            priority = "Medium"
        elif 50 <= title_length <= 60:
            recommendation_text = f"Your title length ({title_length} characters) is optimal. Ensure primary keywords are placed at the beginning for better SEO visibility."
            priority = "Low"
        elif title_length <= 70:
            recommendation_text = f"Your title ({title_length} characters) is slightly long. Shorten to 50-60 characters, preserving critical keywords upfront to avoid truncation."
            priority = "Medium"
        else:
            recommendation_text = f"Your title ({title_length} characters) is too long and may be cut off. Reduce to 50-60 characters, prioritizing essential keywords first."
            priority = "High"

        pass_status = 50 <= title_length <= 70
        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if pass_status:
            recommendation_obj = None

        if title_length == 0:
            score = 0
        elif 50 <= title_length <= 60:
            score = MAX_SCORES['title_tag']
        elif title_length < 50:
            score = max(0, int((title_length - 10) / (50 - 10) * MAX_SCORES['title_tag'])) if title_length > 10 else 0
        else:
            excess = min(title_length - 60, 30)
            score = max(0, MAX_SCORES['title_tag'] - int(excess / 30 * MAX_SCORES['title_tag']))

        result = {
            "title_tag": {
                "title": title_text,
                "length": title_length,
                "is_optimal_length": 50 <= title_length <= 60,
                "pass": pass_status,
                "description": "The title tag is displayed in search results and browser tabs, influencing clicks and SEO.",
                "recommendation": recommendation_obj,
                "importance": "Title tags are critical for defining page relevance and influencing CTR. Prioritize keywords at the beginning.",
                "blog": "",
                "score": score
            }
        }
        self._save_to_redis('title_tag', result)
        return result
    
    def analyze_meta_description(self):
        meta_desc = self.soup.find('meta', attrs={'name': 'description'})
        description = meta_desc.get('content', '').strip() if meta_desc else ''
        description_length = len(description)
        recommendation_text = ""
        priority = "Low"

        if not description:
            recommendation_text = "Your page is missing a meta description. Add a concise summary (120-160 characters) including keywords and value proposition."
            priority = "High"
        elif description_length < 70:
            recommendation_text = f"Your meta description is too short ({description_length} characters). Expand to 120-160 characters with relevant keywords and user intent focus."
            priority = "High"
        elif description_length < 120:
            recommendation_text = f"Your meta description ({description_length} characters) is shorter than optimal. Expand to 120-160 characters with clear value and intent alignment."
            priority = "Medium"
        elif 120 <= description_length <= 160:
            recommendation_text = f"Your meta description length ({description_length} characters) is optimal. Ensure it's engaging and keyword-relevant."
            priority = "Low"
        else:
            recommendation_text = f"Your meta description ({description_length} characters) is too long and may be truncated. Shorten to 120-160 characters, keeping essential keywords."
            priority = "Medium"

        pass_status = 70 <= description_length <= 180
        recommendation_obj = {"text": recommendation_text, "priority": priority}

        if pass_status:
            if 120 <= description_length <= 160:
                recommendation_text = f"Your meta description length ({description_length} characters) is optimal. Ensure it's engaging and keyword-relevant."
                priority = "Low"
            elif 70 <= description_length < 120:
                recommendation_text = f"Meta description ({description_length} characters) is acceptable but could be longer (120-160 ideal)."
                priority = "Low"
            elif 160 < description_length <= 180:
                recommendation_text = f"Meta description ({description_length} characters) is acceptable but could be shorter (120-160 ideal)."
                priority = "Low"
            recommendation_obj = None

        if description_length == 0:
            score = 0
        elif 120 <= description_length <= 160:
            score = MAX_SCORES['meta_description']
        elif 70 <= description_length < 120:
             score = max(0, int((description_length - 70) / (120 - 70) * MAX_SCORES['meta_description']))
        elif description_length < 70:
             score = 0
        else:
            excess = min(description_length - 160, 50)
            score = max(0, MAX_SCORES['meta_description'] - int(excess / 50 * MAX_SCORES['meta_description']))

        result = {
            "meta_description": {
                "content": description,
                "length": description_length,
                "is_optimal_length": 120 <= description_length <= 160,
                "pass": pass_status,
                "description": "The meta description summarizes page content in search results, influencing click-through rates.",
                "recommendation": recommendation_obj,
                "importance": "Meta descriptions influence CTR. Compelling, keyword-rich descriptions improve visibility and engagement.",
                "blog": "",
                "score": score
            }
        }
        self._save_to_redis('meta_description', result)
        return result

    def analyze_heading_tags(self):
        h1_tags = self.soup.find_all('h1')
        h1_content = [tag.get_text(strip=True) for tag in h1_tags]
        h1_count = len(h1_content)
        h1_recommendation_text = ""
        h1_priority = "Low"

        if h1_count == 0:
            h1_recommendation_text = "Missing H1 tag. Add a single H1 containing the primary keyword and summarizing the page topic."
            h1_priority = "High"
            h1_pass = False
        elif h1_count == 1:
            h1_recommendation_text = "Single H1 tag found. Ensure it includes the primary keyword and accurately reflects the page topic."
            h1_priority = "Low"
            h1_pass = True
        else:
            h1_recommendation_text = f"Multiple H1 tags ({h1_count}) found. Use only one H1 for the main page topic to avoid confusing search engines."
            h1_priority = "High"
            h1_pass = False

        other_headers = {}
        hierarchy_issues = []
        last_level = 1
        all_headings = self.soup.find_all([f'h{i}' for i in range(1, 7)])
        sub_heading_exists = False

        for i in range(2, 7):
             tag_name = f'h{i}'
             tags = self.soup.find_all(tag_name)
             content = [tag.get_text(strip=True) for tag in tags]
             count = len(content)
             other_headers[tag_name] = {"count": count, "content": content}
             if count > 0: sub_heading_exists = True

        for heading in all_headings:
             level = int(heading.name[1])
             if level > last_level + 1:
                  hierarchy_issues.append(f"Skipped heading level: Found <{heading.name}> after <h{last_level}>.")
                  break
             last_level = level

        hierarchy_recommendation_text = ""
        hierarchy_priority = "Low"
        hierarchy_pass = True

        if not sub_heading_exists and h1_pass:
             hierarchy_recommendation_text = "No H2-H6 subheadings found. Add subheadings to structure content, improve readability, and help search engines understand topic hierarchy."
             hierarchy_priority = "Medium"
             hierarchy_pass = False
        elif hierarchy_issues:
            hierarchy_recommendation_text = "Heading hierarchy is broken. Maintain sequential order (H1 → H2 → H3 etc.) without skipping levels for better structure and accessibility."
            hierarchy_priority = "Medium"
            hierarchy_pass = False
        elif sub_heading_exists:
             hierarchy_recommendation_text = "Heading structure (H2-H6) appears logical. Ensure headings are descriptive and use relevant secondary keywords."
             hierarchy_priority = "Low"

        h1_recommendation_obj = {"text": h1_recommendation_text, "priority": h1_priority}
        if h1_pass:
            h1_recommendation_obj = None

        hierarchy_recommendation_obj = {"text": hierarchy_recommendation_text, "priority": hierarchy_priority}
        if hierarchy_pass:
            hierarchy_recommendation_obj = None

        h1_score = MAX_SCORES['h1_header'] if h1_pass else 0
        h2_h6_score = 0
        if sub_heading_exists:
             if hierarchy_pass:
                  h2_h6_score = MAX_SCORES['h2_h6_headers']
             else:
                  h2_h6_score = MAX_SCORES['h2_h6_headers'] // 2
        total_score = h1_score + h2_h6_score

        result = {
            "headers": {
                "pass": h1_pass and hierarchy_pass,
                "h1": {
                    "count": h1_count,
                    "content": h1_content,
                    "pass": h1_pass,
                    "description": "The H1 tag should define the main topic of the page.",
                    "recommendation": h1_recommendation_obj,
                    "importance": "A single, relevant H1 tag is crucial for signaling the primary page topic to search engines."
                },
                "other_headers": other_headers,
                "hierarchy_recommendation": hierarchy_recommendation_obj,
                "blog": "",
                "score": total_score
            }
        }
        self._save_to_redis('heading_tags', result)
        return result

    def analyze_serp_preview(self):
        title_tag = self.soup.find('title')
        title_text = title_tag.text.strip() if title_tag else ''
        title_length = len(title_text)
        display_title = title_text[:57] + "..." if title_length > 60 else title_text

        meta_desc = self.soup.find('meta', attrs={'name': 'description'})
        description = meta_desc.get('content', '').strip() if meta_desc else ''
        description_length = len(description)
        display_description = description[:157] + "..." if description_length > 160 else description

        favicon = None
        favicon_link = self.soup.find('link', rel='icon') or self.soup.find('link', rel='shortcut icon')
        if favicon_link and favicon_link.get('href'):
            favicon = favicon_link['href']
            if not favicon.startswith(('http://', 'https://')):
                if favicon.startswith('/'):
                    favicon = f"{self.parsed_url.scheme}://{self.domain}{favicon}"
                else:
                    favicon = f"{self.parsed_url.scheme}://{self.domain}/{favicon}"

        ssl_status = self.analyze_ssl_enabled()['ssl_enabled']
        display_url = f"{'https://' if ssl_status['pass'] else 'http://'}{self.domain}"

        recommendations = []
        priority = "Low"
        issues_found = False

        title_analysis = self.analyze_title_tag()['title_tag']
        if title_analysis['pass'] is False:
             recommendations.append(title_analysis['recommendation']['text'])
             if title_analysis['recommendation']['priority'] == "High": priority = "High"
             elif title_analysis['recommendation']['priority'] == "Medium" and priority == "Low": priority = "Medium"
             issues_found = True

        meta_analysis = self.analyze_meta_description()['meta_description']
        if meta_analysis['pass'] is False:
             recommendations.append(meta_analysis['recommendation']['text'])
             if meta_analysis['recommendation']['priority'] == "High": priority = "High"
             elif meta_analysis['recommendation']['priority'] == "Medium" and priority == "Low": priority = "Medium"
             issues_found = True
        
        pass_status = not issues_found

        if not issues_found:
            recommendation_text = "Your SERP preview elements (Title, Description) appear optimized. Ensure they accurately reflect content and target user intent."
            priority = "Low"
        else:
             recommendation_text = " ".join(recommendations)

        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if pass_status:
            recommendation_obj = None
        
        score = 0
        if title_analysis['pass'] and meta_analysis['pass']:
            score = MAX_SCORES['serp_preview']
        elif title_analysis['pass']:
             score = MAX_SCORES['serp_preview'] * (MAX_SCORES['title_tag'] / (MAX_SCORES['title_tag'] + MAX_SCORES['meta_description']))
        elif meta_analysis['pass']:
             score = MAX_SCORES['serp_preview'] * (MAX_SCORES['meta_description'] / (MAX_SCORES['title_tag'] + MAX_SCORES['meta_description']))
        else:
             score = 0
        original_score_calc_title_contrib = title_analysis['score'] * (MAX_SCORES['serp_preview'] / MAX_SCORES['title_tag']) if MAX_SCORES['title_tag'] > 0 else 0
        original_score_calc_meta_contrib = meta_analysis['score'] * (MAX_SCORES['serp_preview'] / MAX_SCORES['meta_description']) if MAX_SCORES['meta_description'] > 0 else 0
        score = min(round(original_score_calc_title_contrib + original_score_calc_meta_contrib), MAX_SCORES['serp_preview'])

        result = {
            "serp_preview": {
                "pass": pass_status,
                "url": display_url,
                "title": display_title,
                "caption": display_description,
                "favicon": favicon,
                "description": "Simulates how your page might appear in search results (SERP). Title and description influence user clicks.",
                "recommendation": recommendation_obj,
                "importance": "The SERP preview directly impacts click-through rates. Optimizing title and meta description is key, though search engines may generate their own snippets.",
                "blog": "",
                "score": score
            }
        }
        self._save_to_redis('serp_preview', result)
        return result

    def analyze_keyword_consistency(self):
        nlp = None
        try:
            nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.error("Spacy model 'en_core_web_sm' not found. Cannot perform keyword analysis.")
            return {"keyword_consistency": {"pass": False, "error": "Spacy model not found.", "score": 0, "recommendation": {"text": "Install spacy model 'en_core_web_sm' to enable keyword analysis.", "priority": "Medium"}}} 

        seo_stopwords = {
            'a', 'an', 'the', 'and', 'or', 'but', 'if', 'then', 'else', 'when', 
            'to', 'of', 'for', 'with', 'by', 'on', 'in', 'at', 'from', 'here', 
            'there', 'this', 'that', 'these', 'those', 'is', 'are', 'was', 'were', 
            'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 
            'can', 'could', 'will', 'would', 'shall', 'should', 'may', 'might',
            'must', 'ought', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'its',
            'as', 'com', 'www', 'per', 'one', 'two', 'three', 'all', 'get',
            'your', 'my', 'our', 'their', 'his', 'her', 'us', 'me', 
            'click', 'page', 'site', 'website', 'web', 'home', 'contact'
        }
        generic_phrases = {
            'your site', 'your website', 'this site', 'this website', 'our site', 
            'our website', 'more information', 'learn more', 'find out', 'read more',
            'click here', 'contact us', 'get in touch', 'home page', 'main page',
            'about us', 'about me', 'our team', 'our company'
        }
        for tag in self.soup(["script", "style", "nav", "header"]):
            tag.extract()
        body_text = self.soup.body.get_text() if self.soup.body else ''
        body_text = re.sub(r'\s+', ' ', body_text).strip().lower()
        body_text = re.sub(r'[^\w\s]', ' ', body_text)
        doc = nlp(body_text)
        tokens = []
        for token in doc:
            if (token.is_alpha and 
                not token.is_stop and 
                token.text.lower() not in seo_stopwords and 
                len(token.text) >= 3):
                lemma = token.lemma_.lower().strip()
                tokens.append(lemma)
            else:
                pass
        freq_counter = Counter(tokens)
        title = self.soup.find('title').get_text(strip=True).lower() if self.soup.find('title') else ''
        title_doc = nlp(title)
        title_tokens = {token.lemma_.lower().strip() for token in title_doc 
                         if token.is_alpha and not token.is_stop and 
                         token.text.lower() not in seo_stopwords and len(token.text) >= 3}
        description_el = self.soup.find('meta', attrs={'name': 'description'})
        description = description_el.get('content', '').lower() if description_el else ''
        desc_doc = nlp(description)
        description_tokens = {token.lemma_.lower().strip() for token in desc_doc 
                               if token.is_alpha and not token.is_stop and 
                               token.text.lower() not in seo_stopwords and len(token.text) >= 3}
        headings = []
        for i in range(1, 7):
            headings.extend([tag.get_text().lower() for tag in self.soup.find_all(f'h{i}')])
        
        headings_tokens = set()
        for heading in headings:
            heading_doc = nlp(heading)
            for token in heading_doc:
                if token.is_alpha and not token.is_stop and token.text.lower() not in seo_stopwords and len(token.text) >= 3:
                    headings_tokens.add(token.lemma_.lower().strip())
        footer_tokens = set()
        for footer in self.soup.find_all("footer"):
            footer_text = footer.get_text().lower()
            footer_doc = nlp(footer_text)
            for token in footer_doc:
                if token.is_alpha and not token.is_stop and token.text.lower() not in seo_stopwords and len(token.text) >= 3:
                    footer_tokens.add(token.lemma_.lower().strip())
        text_tokens = [token.text.lower() for token in doc 
                       if token.is_alpha and token.text.lower() not in seo_stopwords and len(token.text) >= 3]
        bigrams = []
        trigrams = []
        
        for i in range(len(text_tokens) - 1):
            bigram = f"{text_tokens[i]} {text_tokens[i+1]}"
            if bigram not in generic_phrases:
                bigrams.append(bigram)
            
        for i in range(len(text_tokens) - 2):
            trigram = f"{text_tokens[i]} {text_tokens[i+1]} {text_tokens[i+2]}"
            if not any(generic in trigram for generic in generic_phrases):
                trigrams.append(trigram)
        bigram_counter = Counter(bigrams)
        trigram_counter = Counter(trigrams)
        phrase_counter = Counter()
        phrase_counter.update(bigram_counter)
        phrase_counter.update(trigram_counter)
        sorted_keywords = []
        for word, freq in freq_counter.most_common(20):
            if word.isdigit():
                continue
                
            in_footer = word in footer_tokens
            sorted_keywords.append({
                "keyword": word,
                "frequency": freq,
                "in_title": word in title_tokens,
                "in_meta_description": word in description_tokens,
                "in_headings": word in headings_tokens,
                "in_footer": in_footer
            })
        sorted_phrases = []
        for phrase, freq in phrase_counter.most_common(30):
            if any(pronoun in phrase.split() for pronoun in ['your', 'my', 'our', 'their', 'we', 'us', 'me']):
                continue
            if phrase in generic_phrases:
                continue
            in_title = phrase in title.lower()
            in_meta = phrase in description.lower()
            in_headings = any(phrase in heading.lower() for heading in headings)
            in_footer = any(phrase in footer.get_text().lower() for footer in self.soup.find_all("footer"))
            
            sorted_phrases.append({
                "phrase": phrase,
                "frequency": freq,
                "in_title": in_title,
                "in_meta_description": in_meta,
                "in_headings": in_headings,
                "in_footer": in_footer
            })
            if len(sorted_phrases) >= 10:
                break
        keyword_recommendations = []
        main_keywords = sorted_keywords[:5]
        missing_elements = []
        for kw in main_keywords:
            if not kw["in_title"] and not kw["in_meta_description"] and not kw["in_headings"]:
                missing_elements.append(f"'{kw['keyword']}'")
        
        pass_status = not missing_elements

        if missing_elements:
            keyword_recommendations.append(
                f"Top keywords {', '.join(missing_elements)} don't appear in title, meta description, or headings. "
                f"Include these important terms in key page elements to improve topical relevance."
            )
        total_words = len(tokens)
        if total_words > 0:
            for kw in main_keywords[:3]:
                density = (kw["frequency"] / total_words) * 100
                if density > 5:
                    keyword_recommendations.append(
                        f"Keyword '{kw['keyword']}' has high density ({density:.1f}%). "
                        f"Consider reducing to avoid keyword stuffing penalties."
                    )
        filtered_keywords = [kw for kw in sorted_keywords if kw["frequency"] > 1]
        
        main_count = len(main_keywords)
        if main_count:
            score = int((main_count - len(missing_elements)) / main_count * MAX_SCORES['keyword_consistency'])
        else:
            score = 0

        priority = "Low"
        if keyword_recommendations:
             priority = "Medium"
             if any("high density" in rec for rec in keyword_recommendations):
                  priority = "High"
             if any("don't appear in title" in rec for rec in keyword_recommendations):
                  priority = "High"
        else:
            keyword_recommendations.append("Keyword usage in title, meta description, and headings appears consistent with page content.")

        recommendation_obj = {"text": " ".join(keyword_recommendations), "priority": priority}
        if pass_status:
            recommendation_obj = None

        inner = {
            "pass": pass_status,
            "keywords": filtered_keywords[:10],
            "phrases": sorted_phrases[:5],
            "recommendation": recommendation_obj,
            "description": "Evaluates if prominent keywords from body content are consistently used in title, meta description, and headings.",
            "importance": "Using main keywords in key HTML elements (title, H1, meta description) reinforces topic relevance to search engines.",
            "score": score
        }
        result = {"keyword_consistency": inner}
        self._save_to_redis('keyword_consistency', result)
        return result

    async def analyze_content_amount(self, client: httpx.AsyncClient):
        recommendation_text = ""
        priority = "Low"
        score = 0
        word_count = 0
        content_density = 0
        status_pass = False
        text_content = ""
        html_string_for_size_calc = ""

        try:
            response = await client.get(self.url, timeout=10.0, follow_redirects=True)
            response.raise_for_status()  # Raise an exception for bad status codes
            html_string_for_size_calc = response.text
            soup = BeautifulSoup(response.text, "lxml")
            body = soup.body

            if not body:
                recommendation_text = "Your page has no body content. Add substantial, relevant content (aim for 300+ words) to provide value and improve search visibility."
                priority = "High"
                status_pass = False
                score = 0
                word_count = 0 # Ensure word_count is 0 if no body
            else:
                # Extract title text
                title_text = soup.title.string.strip() if soup.title and soup.title.string else ''

                # Extract all h1–h6 and p texts
                elems = soup.find_all(['h1','h2','h3','h4','h5','h6','p'])
                texts = [el.get_text(strip=True) for el in elems]

                # Combine everything
                combined = ' '.join([title_text] + texts)

                # Normalize whitespace
                normalized = ' '.join(combined.split())

                # Word and character counts
                words = normalized.split()
                word_count = len(words)
                
                text_content = normalized # for content_size calculation

                # Calculate HTML size based on the fetched document for text_html_ratio
                html_size = len(html_string_for_size_calc)
                content_size = len(text_content.encode('utf-8'))
                content_density = round((content_size / html_size) * 100) if html_size > 0 else 0

                if word_count < 300:
                    recommendation_text = f"Low word count ({word_count}). Add more unique, relevant content (aim for 300+, ideally 800+ for competitive topics) to establish authority and improve rankings."
                    priority = "High"
                    status_pass = False
                    score = 0
                elif word_count < 800:
                    recommendation_text = f"Word count ({word_count}) is adequate but could be expanded for competitive topics. Aim for 800-1500+ words for better depth."
                    priority = "Medium"
                    status_pass = True
                    score = MAX_SCORES['content_amount'] // 2
                elif word_count < 1500:
                     recommendation_text = f"Good word count ({word_count}). Ensure content thoroughly covers the topic and satisfies user intent."
                     priority = "Low"
                     status_pass = True
                     score = MAX_SCORES['content_amount'] * 3 // 4
                else:
                    recommendation_text = f"Excellent word count ({word_count}). Ensure content is well-structured, engaging, and directly addresses user intent."
                    priority = "Low"
                    status_pass = True
                    score = MAX_SCORES['content_amount']

                if content_density < 20 and word_count > 100:
                    recommendation_text += f" Low text-to-HTML ratio ({content_density}%) suggests potential code bloat. Simplify HTML structure if possible."
                    if priority == "Low": priority = "Low" # Keep low if already low, don't elevate
                    elif priority == "Medium" and word_count >= 800 : priority = "Medium" # Maintain medium if word count is decent
                    # otherwise, keep original High/Medium priority based on word count
                elif content_density > 60:
                     recommendation_text += f" High text-to-HTML ratio ({content_density}%). This is generally good."
                     # Priority remains based on word count or previous density assessment

        except httpx.HTTPStatusError as e:
            logger.warning(f"HTTP error fetching content for {self.url} in analyze_content_amount: {e}")
            recommendation_text = f"Could not fetch page content (HTTP {e.response.status_code}) to analyze content amount."
            priority = "High"
            status_pass = False
            score = 0
            word_count = 0
            content_density = 0
        except httpx.RequestError as e:
            logger.warning(f"Request error fetching content for {self.url} in analyze_content_amount: {e}")
            recommendation_text = f"Could not fetch page content from {self.url} to analyze content amount. Error: {type(e).__name__}."
            priority = "High"
            status_pass = False
            score = 0
            word_count = 0
            content_density = 0
        except Exception as e:
            logger.error(f"Unexpected error in analyze_content_amount for {self.url}: {e}")
            recommendation_text = f"An unexpected error occurred while analyzing content amount: {type(e).__name__}."
            priority = "High"
            status_pass = False
            score = 0
            word_count = 0
            content_density = 0
            
        final_recommendation_text = recommendation_text.strip()
        final_priority = priority

        recommendation_obj = {"text": final_recommendation_text, "priority": final_priority}
        
        # If status_pass is True, recommendation should be None unless there are specific negative points.
        if status_pass: 
            is_genuinely_passing_text = True # Assume true initially
            if "Low text-to-HTML ratio" in final_recommendation_text and content_density < 20 and word_count > 100:
                is_genuinely_passing_text = False # This is a negative point even if word count is good
            # Add other similar checks for negative aspects if any

            if is_genuinely_passing_text and not (priority == "High" and word_count == 0 and not html_string_for_size_calc):
                # The second part checks against an initial error state that might have status_pass=True due to later logic changes
                recommendation_obj = None
        elif not final_recommendation_text: # If not passing and text is empty, also None (should not happen)
             recommendation_obj = None


        result = {"content_amount": {
            "pass": status_pass,
            "word_count": word_count,
            "text_html_ratio_percent": content_density,
            "description": "Evaluates the amount of visible textual content. Sufficient, high-quality content is crucial for SEO and user engagement.",
            "recommendation": recommendation_obj,
            "importance": "Content quantity signals topical depth to search engines. While quality matters most, very thin content (<300 words) often struggles to rank. Aim for comprehensive coverage relevant to the topic.",
            "blog": "",
            "score": score
            }
        }
        # self._save_to_redis('content_amount', result) # Redis saving will be handled by run_all after all async tasks complete
        return result

    def analyze_image_alt_attributes(self):
        images = self.soup.find_all('img')
        total_images = len(images)
        images_with_alt = [img for img in images if img.has_attr('alt') and img['alt'].strip()]
        images_without_alt_or_empty = [img for img in images if not img.has_attr('alt') or not img['alt'].strip()]
        missing_alt_images_details = []
        for img in images_without_alt_or_empty[:10]:
            src = img.get('src', '')
            missing_alt_images_details.append({
                "src": src[:100] + ('...' if len(src) > 100 else ''),
                "element": str(img)[:150] + ('...' if len(str(img)) > 150 else '')
            })

        num_missing = len(images_without_alt_or_empty)
        percent_missing = round((num_missing / total_images) * 100) if total_images > 0 else 0
        all_have_alt = (num_missing == 0) if total_images > 0 else True

        recommendation_text = ""
        priority = "Low"
        score = 0

        if total_images == 0:
            recommendation_text = "No images found on the page. Consider adding relevant images with descriptive alt text to enhance content and potentially rank in image search."
            priority = "Low"
            score = MAX_SCORES['image_alt_attributes']
            pass_status = True
        elif all_have_alt:
            recommendation_text = "All images have alt attributes. Ensure they are descriptive and concise (under 100 characters) for accessibility and SEO."
            priority = "Low"
            score = MAX_SCORES['image_alt_attributes']
            pass_status = True
        elif percent_missing <= 10:
            recommendation_text = f"{num_missing} ({percent_missing}%) image(s) lack descriptive alt text. Add concise alt attributes to improve accessibility and image SEO."
            priority = "Medium"
            score = int((1 - percent_missing / 100) * MAX_SCORES['image_alt_attributes'] * 0.8)
            pass_status = True
        elif percent_missing <= 50:
            recommendation_text = f"{num_missing} ({percent_missing}%) images lack alt text. Add descriptive alt attributes to all informative images for accessibility and SEO."
            priority = "High"
            score = int((1 - percent_missing / 100) * MAX_SCORES['image_alt_attributes'] * 0.5)
            pass_status = False
        else:
            recommendation_text = f"Critical: {num_missing} ({percent_missing}%) images are missing alt text. This significantly impacts accessibility and SEO. Add descriptive alt text to all images immediately."
            priority = "High"
            score = int((1 - percent_missing / 100) * MAX_SCORES['image_alt_attributes'] * 0.2)
            pass_status = False

        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if pass_status:
            recommendation_obj = None

        result = {"image_alt_attributes": {
            "pass": pass_status,
            "total_images": total_images,
            "images_with_alt": len(images_with_alt),
            "images_without_alt": num_missing,
            "percent_missing": percent_missing,
            "missing_alt_images_sample": missing_alt_images_details,
            "description": "Checks if <img> tags have non-empty 'alt' attributes, which describe image content for accessibility and search engines.",
            "recommendation": recommendation_obj,
            "importance": "Alt text is crucial for accessibility (screen readers) and helps search engines understand image content. Missing alt text negatively impacts both.",
            "blog": "",
            "score": score
        }}
        self._save_to_redis('image_alt_attributes', result)
        return result

    def analyze_canonical_tag(self):
        canonical = self.soup.find('link', attrs={'rel': 'canonical'})
        canonical_url = canonical['href'].strip() if canonical and canonical.has_attr('href') else None

        def _normalize(u):
            if not u: return None, None
            try:
                 if not u.startswith(('http://', 'https://')):
                     if u.startswith('/'):
                         u = f"{self.parsed_url.scheme}://{self.parsed_url.netloc}{u}"
                     else:
                          base_path = self.parsed_url.path.rsplit('/', 1)[0]
                          u = f"{self.parsed_url.scheme}://{self.parsed_url.netloc}{base_path}/{u}"
                 p = urlparse(u)
                 netloc = p.netloc.lower().replace('www.', '')
                 path = p.path.rstrip('/') or '/'
                 return netloc, path
            except Exception as e:
                 logger.error(f"Error parsing URL '{u}': {e}")
                 return None, None

        curr_netloc, curr_path = _normalize(self.url)
        canon_netloc, canon_path = _normalize(canonical_url)

        priority = "Low"
        recommendation_text = ""
        passed = False
        score = 0

        if not canonical_url:
            recommendation_text = "Missing canonical tag. Add a self-referencing rel='canonical' link tag with the absolute URL of this page to prevent duplicate content issues."
            priority = "High"
            passed = False
            score = 0
        elif curr_netloc is None or canon_netloc is None:
             recommendation_text = f"Could not properly parse current URL or canonical URL ({canonical_url}). Check URL formats."
             priority = "Medium"
             passed = False
             score = 0
        elif curr_netloc == canon_netloc and curr_path == canon_path:
            recommendation_text = "Self-referencing canonical tag is correctly implemented using an absolute URL."
            priority = "Low"
            passed = True
            score = MAX_SCORES['canonical_tag']
        else:
            recommendation_text = f"Canonical URL ({canonical_url}) points to a different page than the current URL ({self.url}). Ensure this is intentional (e.g., for syndicated content or parameter variations) and correctly points to the preferred version."
            priority = "High"
            passed = False
            score = MAX_SCORES['canonical_tag'] // 2

        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if passed:
            recommendation_obj = None

        result = {"canonical_tag": {
            "pass": passed,
            "canonical_url": canonical_url,
            "description": "The canonical tag specifies the preferred version of a page for search engines, consolidating signals and preventing duplicate content.",
            "recommendation": recommendation_obj,
            "importance": "Crucial for preventing duplicate content issues, consolidating link equity, and ensuring the correct page version is indexed and ranked.",
            "blog": "",
            "score": score
        }}
        self._save_to_redis('canonical_tag', result)
        return result

    def analyze_noindex_tag(self):
        robots_meta = self.soup.find('meta', attrs={'name': 'robots'})
        googlebot_meta = self.soup.find('meta', attrs={'name': 'googlebot'})
        content = ""
        has_noindex = False
        recommendation_text = ""
        priority = "Low"
        score = MAX_SCORES['noindex_tag']

        tag_to_check = googlebot_meta if googlebot_meta else robots_meta

        if tag_to_check:
            content = tag_to_check.get('content', '').lower()
            has_noindex = 'noindex' in content
            if has_noindex:
                recommendation_text = "CRITICAL: This page uses a 'noindex' meta tag, preventing search engines from indexing it. Remove the tag if this page should appear in search results."
                priority = "High"
                score = 0
            else:
                recommendation_text = "Page is indexable via robots meta tag (no 'noindex' directive found)."
                priority = "Low"
        else:
            recommendation_text = "No 'robots' or 'googlebot' meta tag found. Page will likely be indexed by default. Add a tag if specific directives (e.g., noindex, nofollow) are needed."
            priority = "Low"

        pass_status_noindex_tag = has_noindex
        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if pass_status_noindex_tag:
            recommendation_obj = None

        result = {"noindex_tag": {
                "pass": pass_status_noindex_tag,
                "content": content,
                "description": "Checks for a 'noindex' directive within the robots or googlebot meta tag. 'pass: true' means the page IS NOT indexable according to this tag.",
                "recommendation": recommendation_obj,
                "importance": "The 'noindex' directive explicitly tells search engines not to include this page in their index. Incorrect use can remove important pages from search results.",
                "blog": "",
                "score": score
            }
        }
        self._save_to_redis('noindex_tag', result['noindex_tag'])
        return result

    def analyze_language(self):
        html_tag = self.soup.find('html')
        declared_lang = html_tag.get('lang', '') if html_tag else ''

        lang_rec = ""
        lang_priority = "Low"
        lang_pass = True
        declared_score = 0

        if not declared_lang:
            lang_rec = "Missing language declaration. Add the 'lang' attribute to the <html> tag (e.g., <html lang='en'>) for accessibility and search engine understanding."
            lang_priority = "Medium"
            lang_pass = False
            declared_score = 0
        else:
            lang_rec = f"Language declared as '{declared_lang}'. Ensure this accurately reflects the primary content language."
            lang_priority = "Low"
            declared_score = MAX_SCORES['language_declared']
            lang_pass = True

        recommendation_text = lang_rec
        priority = lang_priority
        pass_status_language = lang_pass
        recommendation_obj = {"text": recommendation_text.strip(), "priority": priority}
        if pass_status_language:
            recommendation_obj = None

        result = {
            "language": {
                "pass": pass_status_language,
                "declared": declared_lang,
                "hreflang": {
                    "exists": False,
                    "count": 0,
                    "tags": [],
                    "errors": []
                },
                "description": "Checks for HTML 'lang' attribute to specify content language.",
                "recommendation": recommendation_obj,
                "importance": "'lang' attribute aids accessibility and search engine understanding. Hreflang tags (not checked here) are for international SEO.",
                "blog": "",
                "score": declared_score
            }
        }
        self._save_to_redis('language', result)
        return result

    def analyze_ssl_enabled(self):
        is_ssl = self.url.startswith('https://')
        recommendation_text = ""
        priority = "Low"
        score = 0

        if is_ssl:
            recommendation_text = "Site uses HTTPS. Ensure valid SSL certificates, redirect HTTP traffic, and check for mixed content issues."
            priority = "Low"
            score = MAX_SCORES['ssl_enabled']
        else:
            recommendation_text = "CRITICAL: Site is not using HTTPS. Implement SSL immediately. HTTPS is a ranking factor, secures user data, and builds trust."
            priority = "High"
            score = 0

        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if is_ssl:
            recommendation_obj = None

        result = {
            "ssl_enabled": {
                "pass": is_ssl,
                "description": "Checks if the website connection uses HTTPS encryption.",
                "recommendation": recommendation_obj,
                "importance": "HTTPS is essential for security, user trust, and SEO rankings. Google uses HTTPS as a ranking signal.",
                "blog": "",
                "score": score
            }
        }
        self._save_to_redis('ssl_enabled', result)
        return result

    def analyze_analytics(self):
        detected_analytics = []
        for script in self.soup.find_all('script'):
            src = script.get('src', '') or ''
            if 'gtag/js?id=G-' in src:
                if 'Google Analytics 4' not in detected_analytics:
                    detected_analytics.append('Google Analytics 4')
                continue
            if 'google-analytics.com/analytics.js' in src or 'gtag/js?id=UA-' in src:
                if 'Universal Analytics' not in detected_analytics:
                    detected_analytics.append('Universal Analytics')
                continue
            if 'googletagmanager.com/gtm.js' in src:
                if 'Google Tag Manager' not in detected_analytics:
                    detected_analytics.append('Google Tag Manager')
                continue
        html_text = str(self.soup)
        if "fbq('init'" in html_text and 'Facebook Pixel' not in detected_analytics:
            detected_analytics.append('Facebook Pixel')
        if '_paq.push' in html_text and 'Matomo (Piwik)' not in detected_analytics:
            detected_analytics.append('Matomo (Piwik)')
        html_low = html_text.lower()
        if 'google-analytics.com/analytics.js' in html_text and 'Universal Analytics' not in detected_analytics:
            detected_analytics.append('Universal Analytics')
        if "ga('create'," in html_text and 'Universal Analytics' not in detected_analytics:
            detected_analytics.append('Universal Analytics')
        if "gtag('config', 'g-" in html_low and 'Google Analytics 4' not in detected_analytics:
            detected_analytics.append('Google Analytics 4')
        if "gtag('config', 'ua-" in html_low and 'Universal Analytics' not in detected_analytics:
            detected_analytics.append('Universal Analytics')
        if 'googletagmanager.com/ns.html' in html_text and 'Google Tag Manager' not in detected_analytics:
            detected_analytics.append('Google Tag Manager')

        passed = bool(detected_analytics)
        recommendation_text = ""
        priority = "Low"
        score = 0

        if passed:
            recommendation_text = f"Detected analytics tools: {', '.join(detected_analytics)}. Verify configuration, exclude internal traffic, and ensure GDPR/CCPA compliance."
            priority = "Low"
            score = MAX_SCORES['analytics']
        else:
            recommendation_text = "No analytics tools detected. Implement Google Analytics or similar to track user behavior, measure performance, and inform strategy."
            priority = "Medium"
            score = 0

        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if passed:
            recommendation_obj = None

        result = {
            "analytics": {
                "pass": passed,
                "detected_tools": detected_analytics,
                "description": "Checks for common web analytics tracking codes (Google Analytics, GTM, etc.).",
                "recommendation": recommendation_obj,
                "importance": "Analytics provide essential data for understanding user behavior, traffic sources, and conversion paths, informing SEO and content strategy.",
                "blog": "",
                "score": score
            }
        }
        self._save_to_redis('analytics', result)
        return result

    def analyze_schema_markup(self):
        schema_details = {"json_ld": [], "microdata": [], "rdfa": []}
        detected_types = Counter()
        has_schema = False

        json_ld_scripts = self.soup.find_all('script', type='application/ld+json')
        for script in json_ld_scripts:
            try:
                schema_data = json.loads(script.string)
                items_to_process = []
                if isinstance(schema_data, dict):
                    items_to_process.append(schema_data)
                elif isinstance(schema_data, list):
                    items_to_process.extend(schema_data)

                for item in items_to_process:
                    if isinstance(item, dict):
                        schema_type = item.get('@type')
                        if schema_type:
                            schema_details["json_ld"].append({"type": schema_type, "data": item}) 
                            detected_types[schema_type] += 1
                            has_schema = True
            except json.JSONDecodeError as e:
                pass
            except Exception as e:
                 pass

        microdata_items = self.soup.find_all(itemscope=True)
        for item in microdata_items:
            item_type = item.get('itemtype')
            if item_type and 'schema.org' in item_type:
                schema_type = item_type.split('/')[-1]
                schema_details["microdata"].append({"type": schema_type, "element": str(item)[:150]})
                detected_types[schema_type] += 1
                has_schema = True

        rdfa_types = self.soup.find_all(typeof=True)
        rdfa_props = self.soup.find_all(property=True)
        if rdfa_types or rdfa_props:
             schema_details["rdfa"].append({"detected": True, "type_count": len(rdfa_types), "prop_count": len(rdfa_props)})
             has_schema = True

        priority = "Low"
        recommendation_text = ""
        score = 0

        if has_schema:
            common_types = [f"{t} ({c})" for t, c in detected_types.most_common(5)]
            formats_found = [k for k, v in schema_details.items() if v]
            recommendation_text = f"Schema markup detected ({', '.join(formats_found)}). Common types: {', '.join(common_types)}. Validate using Google's Rich Results Test and ensure data matches visible content."
            priority = "Low"
            score = MAX_SCORES['schema_markup']
            if "microdata" in formats_found or "rdfa" in formats_found:
                recommendation_text += " Consider migrating to JSON-LD for easier implementation and maintenance."
                priority = "Medium"
        else:
            recommendation_text = "No Schema.org markup detected. Implement structured data (JSON-LD recommended) for relevant content (articles, products, events, etc.) to enable rich snippets."
            priority = "Medium"
            score = 0

        passed_status_schema = has_schema
        
        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if passed_status_schema:
            recommendation_obj = None
        
        result = {
            "schema_markup": {
                "pass": passed_status_schema,
                "formats_found": [k for k, v in schema_details.items() if v],
                "detected_types_count": dict(detected_types),
                "common_types": detected_types.most_common(5),
                "description": "Checks for Schema.org structured data markup (JSON-LD, Microdata, RDFa), which helps search engines understand content context.",
                "recommendation": recommendation_obj,
                "importance": "Schema markup enables rich results in SERPs (ratings, FAQs, etc.), improving visibility and CTR. JSON-LD is Google's preferred format.",
                "blog": "",
                "score": score
            }
        }
        self._save_to_redis('schema_markup', result)
        return result

    async def async_analyze_https_redirect(self, client: httpx.AsyncClient):
        recommendation_text = ""
        priority = "Low"
        score = 0
        passed = False
        uses_permanent_redirect = False
        redirect_status = None

        if self.url.startswith('https://'):
            http_url = 'http://' + self.parsed_url.netloc + self.parsed_url.path + (f"?{self.parsed_url.query}" if self.parsed_url.query else "")
            try:
                response = await client.head(http_url, follow_redirects=False, timeout=30.0)
                redirect_status = response.status_code
                if 300 <= redirect_status < 400:
                     location = response.headers.get('location')
                     if location and location.strip().startswith('https://'):
                          is_redirected_to_https = True
                          uses_permanent_redirect = (redirect_status == 301 or redirect_status == 308)
                          passed = uses_permanent_redirect
                          if uses_permanent_redirect:
                               recommendation_text = "HTTP requests permanently redirect (301/308) to HTTPS. This is the correct setup."
                               priority = "Low"
                               score = MAX_SCORES['https_redirect']
                          else:
                               recommendation_text = f"HTTP redirects to HTTPS, but uses a temporary status ({redirect_status}). Use a permanent 301 or 308 redirect for SEO consolidation."
                               priority = "High"
                               score = MAX_SCORES['https_redirect'] // 2
                     else:
                          recommendation_text = f"HTTP redirects ({redirect_status}), but not directly to HTTPS (Location: {location}). Ensure a direct, permanent redirect to the HTTPS version."
                          priority = "High"
                          passed = False
                else:
                     recommendation_text = f"HTTP version does not redirect to HTTPS (Status: {redirect_status}). Implement a permanent 301/308 redirect immediately."
                     priority = "High"
                     passed = False
                     score = 0

            except httpx.RequestError as e:
                 logger.warning(f"HTTP redirect check failed for {http_url}: {e}")
                 recommendation_text = f"Could not check HTTP redirect for {http_url} (Error: {e}). Ensure the HTTP version is accessible and redirects correctly."
                 priority = "Medium"
                 passed = False
                 score = 0
            except Exception as e:
                 logger.error(f"Unexpected error checking HTTP redirect: {e}")
                 recommendation_text = f"Unexpected error during HTTP redirect check: {e}."
                 priority = "Medium"
                 passed = False
                 score = 0
        else:
            recommendation_text = "Site is currently accessed via HTTP. Implement HTTPS and permanent 301/308 redirects immediately."
            priority = "High"
            passed = False
            score = 0
            uses_permanent_redirect = False

        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if passed:
            recommendation_obj = None

        result = {
            "https_redirect": {
                "pass": passed,
                "uses_permanent_redirect": uses_permanent_redirect,
                "status_code": redirect_status,
                "description": "Checks if the non-secure HTTP version of the URL automatically redirects to the secure HTTPS version using a permanent (301/308) redirect.",
                "recommendation": recommendation_obj,
                "importance": "A permanent redirect from HTTP to HTTPS is crucial for security, user trust, consolidating SEO signals, and preventing duplicate content.",
                "blog": "",
                "score": score
            }
        }
        self._save_to_redis('https_redirect', result)
        return result

    async def async_analyze_robots_txt(self, client: httpx.AsyncClient):
        robots_url = f"{self.parsed_url.scheme}://{self.domain}/robots.txt"
        has_robots = False
        content = None
        error_message = None
        recommendation_text = ""
        priority = "Low"
        score = 0

        try:
            response = await client.get(robots_url, timeout=10.0, follow_redirects=True)
            if response.status_code == 200:
                has_robots = True
                content = response.text
                if not content.strip():
                     recommendation_text = "robots.txt file is empty. Add directives (e.g., User-agent: *, Disallow: /admin/) and a Sitemap: entry if applicable."
                     priority = "Medium"
                     score = MAX_SCORES['robots_txt'] // 2
                elif "Disallow: /" in content.replace(" ", ""):
                     recommendation_text = "WARNING: robots.txt contains 'Disallow: /', which blocks all crawlers. Ensure this is intentional, otherwise remove or modify it."
                     priority = "High"
                     score = 0
                else:
                     recommendation_text = "robots.txt found. Verify directives are correct, essential resources (CSS/JS) are not blocked, and consider adding a Sitemap link."
                     priority = "Low"
                     score = MAX_SCORES['robots_txt']
            elif 400 <= response.status_code < 500:
                 recommendation_text = f"robots.txt not found (Status: {response.status_code}). Create a robots.txt file to control crawler access and specify sitemap location."
                 priority = "Medium"
                 score = 0
                 has_robots = False
            else:
                 recommendation_text = f"Error accessing robots.txt (Status: {response.status_code}). Check server configuration and file permissions."
                 priority = "High"
                 score = 0
                 error_message = f"Status code {response.status_code}"
                 has_robots = False

        except httpx.RequestError as e:
            logger.warning(f"Could not fetch robots.txt from {robots_url}: {e}")
            recommendation_text = f"Could not fetch robots.txt (Error: {e}). Ensure the file exists and the server is responding correctly."
            priority = "Medium"
            score = 0
            error_message = str(e)
            has_robots = False
        except Exception as e:
            logger.error(f"Unexpected error fetching robots.txt: {e}")
            recommendation_text = f"Unexpected error fetching robots.txt: {e}."
            priority = "High"
            score = 0
            error_message = str(e)
            has_robots = False

        passed = has_robots and not ("Disallow: /" in (content or "").replace(" ", ""))

        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if passed:
            recommendation_obj = None

        result = {
            "robots_txt":{
                "pass": passed,
                "url": robots_url,
                "content": content[:5000] if content else None,
                "error": error_message,
                "description": "The robots.txt file instructs search engine crawlers which parts of your site should or shouldn't be crawled.",
                "recommendation": recommendation_obj,
                "importance": "Controls crawler access, prevents crawling of sensitive areas, and can specify sitemap locations. Misconfiguration can block important content.",
                "blog": "",
                "score": score
            }
        }
        return result

    async def async_analyze_xml_sitemap(self, client: httpx.AsyncClient, robots_result):
        sitemap_urls_from_robots = []
        robots_content = robots_result.get('robots_txt', {}).get('content')
        if robots_content:
            matches = re.findall(r'(?i)^\s*Sitemap:\s*(.*)', robots_content, re.MULTILINE)
            sitemap_urls_from_robots = [u.strip() for u in matches if u.strip()]

        potential_sitemap_urls = [
             f"{self.parsed_url.scheme}://{self.domain}/sitemap.xml",
             f"{self.parsed_url.scheme}://{self.domain}/sitemap_index.xml"
         ]
        all_potential_urls = list(set(sitemap_urls_from_robots + potential_sitemap_urls))
        found_sitemaps = []
        error_message = None
        recommendation_text = ""
        priority = "Low"
        score = 0

        tasks = [client.head(url, timeout=10.0, follow_redirects=True) for url in all_potential_urls]
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            for i, res in enumerate(results):
                url = all_potential_urls[i]
                if isinstance(res, httpx.Response) and res.status_code == 200:
                    content_type = res.headers.get('content-type', '').lower()
                    if 'xml' in content_type or 'text/plain' in content_type:
                         found_sitemaps.append(url)
                    else:
                         pass
                elif isinstance(res, Exception):
                     pass
        except Exception as e:
            error_message = f"Error checking sitemap locations: {e}"

        passed = bool(found_sitemaps)

        if found_sitemaps:
            recommendation_text = f"XML sitemap(s) found at: {', '.join(found_sitemaps)}. Ensure they are submitted to search consoles, up-to-date, and contain only indexable, canonical URLs."
            priority = "Low"
            score = MAX_SCORES['xml_sitemap']
        else:
            recommendation_text = "No XML sitemap detected at common locations or in robots.txt. Create and submit an XML sitemap to help search engines discover all important pages."
            priority = "Medium"
            score = 0
            if error_message:
                 recommendation_text += f" ({error_message})"

        recommendation_obj = {"text": recommendation_text, "priority": priority}
        if passed:
            recommendation_obj = None

        result = {
            "xml_sitemap": {
                "pass": passed,
                "sitemap_urls_found": found_sitemaps,
                "sitemap_urls_in_robots": sitemap_urls_from_robots,
                "error": error_message,
                "description": "Checks for XML sitemaps, which list important URLs to help search engine discovery and crawling.",
                "recommendation": recommendation_obj,
                "importance": "XML sitemaps help ensure search engines can find and crawl all relevant pages, especially on large or complex sites. They are crucial for efficient indexing.",
                "blog": "",
                "score": score
            }
        }
        return result

    async def run_async_technical(self):
        proxy_url_formatted = None
        if self.proxy_string:
            match = re.match(r'^([^:]+):(\d+):([^:]+):(.+)$', self.proxy_string)
            if match:
                host, port, user, password = match.groups()
                proxy_url_formatted = f"http://{user}:{password}@{host}:{port}"
                logger.info(f"run_async_technical using proxy: {host}:{port}")
            else:
                logger.warning(f"Invalid proxy string format in OnPageSEOAnalyzer: {self.proxy_string}. Proceeding without proxy for technical checks.")
        else:
            logger.debug("run_async_technical running without proxy")
            
        async with httpx.AsyncClient(http2=True, timeout=15.0, proxy=proxy_url_formatted) as client:
            try:
                 response = await client.head(self.url, follow_redirects=True, timeout=10.0)
                 x_robots_tag = response.headers.get('x-robots-tag', '')
                 header_content = x_robots_tag
                 has_noindex_header = 'noindex' in x_robots_tag.lower()
                 pass_status_noindex_header = has_noindex_header

                 noindex_header_priority = "Low"
                 noindex_header_score = MAX_SCORES['noindex_header']
                 noindex_header_rec = ""

                 if has_noindex_header:
                      noindex_header_rec = "CRITICAL: X-Robots-Tag HTTP header contains 'noindex', preventing indexing. Remove this if the page should be indexed."
                      noindex_header_priority = "High"
                      noindex_header_score = 0
                 else:
                      noindex_header_rec = "No 'noindex' directive found in X-Robots-Tag header. Page is indexable via header."

                 recommendation_obj_noindex_header = {"text": noindex_header_rec, "priority": noindex_header_priority}
                 if pass_status_noindex_header:
                     recommendation_obj_noindex_header = None

                 noindex_header_result = {
                      "noindex_header": {
                          "pass": pass_status_noindex_header,
                          "content": header_content,
                          "description": "Checks for a 'noindex' directive in the X-Robots-Tag HTTP header. 'pass: true' means the page IS NOT indexable according to this header.",
                          "recommendation": recommendation_obj_noindex_header,
                          "importance": "The X-Robots-Tag header can prevent indexing, similar to a meta robots tag. It's often used for non-HTML files.",
                          "blog": "",
                          "score": noindex_header_score
                      }
                  }
                 self._save_to_redis('noindex_header', noindex_header_result['noindex_header'])
            except Exception as e:
                 noindex_header_result = {
                     "noindex_header": {
                          "pass": False,
                          "content": f"Error: {str(e)}",
                          "description": "Checks for a 'noindex' directive in the X-Robots-Tag HTTP header. 'pass: false' here indicates an error prevented the check.",
                          "recommendation": {"text": f"Could not verify X-Robots-Tag header due to an error: {str(e)}. Unable to confirm header-based indexability.", "priority": "Medium"},
                          "importance": "The X-Robots-Tag header can prevent indexing, similar to a meta robots tag. It's often used for non-HTML files.",
                          "blog": "",
                          "score": 0
                     }
                 }
                 self._save_to_redis('noindex_header', noindex_header_result['noindex_header'])

            https_task = self.async_analyze_https_redirect(client)
            robots_task = self.async_analyze_robots_txt(client)
            robots_result_data = await robots_task
            sitemap_task = self.async_analyze_xml_sitemap(client, robots_result_data)
            # Add content_amount task
            content_amount_task = self.analyze_content_amount(client)
            
            redirect_result_data, sitemap_result_data, content_amount_result_data = await asyncio.gather(
                https_task, 
                sitemap_task,
                content_amount_task # Gather content_amount result
            )
            
            self._save_to_redis('https_redirect', redirect_result_data['https_redirect'])
            self._save_to_redis('robots_txt', robots_result_data['robots_txt'])
            self._save_to_redis('xml_sitemap', sitemap_result_data['xml_sitemap'])
            self._save_to_redis('content_amount', content_amount_result_data['content_amount']) # Save content_amount result
            
            combined_results = {}
            combined_results.update(redirect_result_data)
            combined_results.update(robots_result_data)
            combined_results.update(sitemap_result_data)
            combined_results.update(noindex_header_result)
            combined_results.update(content_amount_result_data) # Add content_amount result to combined_results
            return combined_results

    def run_all(self):
        results = {}
        results.update(self.analyze_title_tag())
        results.update(self.analyze_meta_description())
        results.update(self.analyze_serp_preview())
        results.update(self.analyze_language())
        results.update(self.analyze_heading_tags())
        results.update(self.analyze_keyword_consistency())
        results.update(self.analyze_image_alt_attributes())
        results.update(self.analyze_canonical_tag())
        results.update(self.analyze_noindex_tag())
        results.update(self.analyze_ssl_enabled())
        results.update(self.analyze_analytics())
        results.update(self.analyze_schema_markup())
        try:
            tech_results = asyncio.run(self.run_async_technical())
        except Exception as e:
            tech_results = {"error": f"Async analysis error: {e}"}
        results.update(tech_results)
        total_score = sum(
            metric_data.get('score', 0)
            for key, metric_data in results.items()
            if isinstance(metric_data, dict) and 'score' in metric_data
        )
        total_possible = sum(MAX_SCORES.values())
        percentage = round((total_score / total_possible) * 100) if total_possible > 0 else 0
        if percentage >= 98:
            grade = 'A+'
        elif percentage >= 95:
            grade = 'A'
        elif percentage >= 90:
            grade = 'A-'
        elif percentage >= 85:
            grade = 'B+'
        elif percentage >= 80:
            grade = 'B'
        elif percentage >= 75:
            grade = 'B-'
        elif percentage >= 70:
            grade = 'C+'
        elif percentage >= 60:
            grade = 'C'
        elif percentage >= 50:
            grade = 'C-'
        elif percentage >= 40:
            grade = 'D'
        else: 
            grade = 'F'
            
        results['total_score'] = {
            'score': total_score,
            'grade': grade
        }

        if grade in ['A+', 'A', 'A-']:
            overall_title = "Your On-Page SEO is Excellent"
            overall_description = "Your page demonstrates strong on-page SEO practices. Only minor improvements may be needed to achieve perfection."
        elif grade in ['B+', 'B', 'B-']:
            overall_title = "Your On-Page SEO is Good"
            overall_description = "Your page has good on-page SEO, but there are some areas that could be improved for even better results."
        elif grade in ['C+', 'C', 'C-']:
            overall_title = "Your On-Page SEO Could Be Better"
            overall_description = "Your page has some level of on-page SEO optimisation but could be improved further. Addressing the highlighted issues will help boost your rankings."
        elif grade == 'D':
            overall_title = "Your On-Page SEO Needs Improvement"
            overall_description = "Your page has significant on-page SEO issues. Address the key recommendations to improve your search visibility."
        else:
            overall_title = "Your On-Page SEO is Poor"
            overall_description = "Your page is missing critical on-page SEO elements. Immediate action is required to improve your rankings."

        results['overall_title'] = overall_title
        results['overall_description'] = overall_description
        return results