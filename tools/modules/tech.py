from bs4 import BeautifulSoup
import dns.resolver
import socket
import re
import json
import logging
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

MAX_TECH_SCORES = {
    "ssl_enabled":    20,
    "robots_meta":    18,
    "charset":        14,
    "technologies":   14,
    "web_server":     10,
    "server_ip":       7,
    "dns_servers":     7,
    "dmarc_record":    5,
    "spf_record":      5
}


class TechnologyReviewAnalyzer:
    MAX_SCORES = MAX_TECH_SCORES
    
    def __init__(self, url: str, html_content: str, headers: dict, task_id: str = None):
        self.url = url
        self.html = html_content
        self.headers = headers
        self.task_id = task_id
        self.soup = BeautifulSoup(html_content, 'lxml')
        self.parsed_url = urlparse(url)
        self.domain = self.parsed_url.netloc
        if self.domain.startswith("www."):
            self.domain = self.domain[4:]
        self.ssl_enabled = self.parsed_url.scheme == "https"
        logger.info(f"TechnologyReviewAnalyzer initialized for URL: {url}")

    def analyze(self) -> dict:
        logger.info(f"Analyzing Technology Review factors for {self.url}")
        results = {
            "ssl_enabled": self.ssl_enabled,
            "robots_meta": self._get_robots_meta(),
            "dns_servers": self._get_dns_servers(),
            "web_server": self._get_web_server_info(),
            "charset": self._get_charset_info(),
            "dmarc_record": self._get_dmarc_record(),
            "spf_record": self._get_spf_record(),
            "server_ip": self._get_server_ip(),
            "technologies": self._get_technology_stack()
        }
        return results

    def _get_robots_meta(self) -> dict:
        logger.info("Analyzing robots meta tags")
        meta = self.soup.find("meta", attrs={"name": "robots"})
        max_score = self.MAX_SCORES.get('robots_meta', 18)
        score = max_score
        recommendation_text = ""
        priority = "Low"
        recommendation = None
        importance = ""
        description = "The robots meta tag tells search engines whether they should index your page and follow its links."

        if meta:
            content = meta.get("content", "").lower()
            noindex = "noindex" in content
            if noindex:
                importance = "This meta tag can prevent search engines from indexing your content, which directly impacts your visibility in search results."
                recommendation_text = "The noindex directive is preventing this page from appearing in search results. Remove it if you want the page to be discoverable."
                score = 0
                priority = "High"
                recommendation = {"text": recommendation_text, "priority": priority}
            else:
                importance = "The robots meta tag is present and allows indexing and following (e.g., 'index, follow' or no 'noindex' directive), which is generally good for SEO."
                recommendation = None
                score = max_score
            return {
                "noindex": noindex,
                "content": meta.get("content"),
                "description": description,
                "importance": importance,
                "recommendation": recommendation,
                "blog": "",
                "score": score
            }
        description = "The robots meta tag tells search engines whether they should index your page and follow its links."
        importance = "Without a specific robots meta tag, search engines will typically index your page and follow links by default, which is generally desirable for visibility."
        recommendation = None
        return {
            "noindex": False,
            "description": description,
            "importance": importance,
            "recommendation": recommendation,
            "blog": "",
            "score": max_score
        }

    def _get_dns_servers(self) -> dict:
        logger.info(f"Analyzing DNS servers for {self.domain}")
        max_score = self.MAX_SCORES.get('dns_servers', 7)
        score = 0
        recommendation_text = ""
        priority = "Low"
        recommendation = None
        try:
            answers = dns.resolver.resolve(self.domain, 'NS')
            nameservers = [str(rdata.target).rstrip('.') for rdata in answers]
            count = len(nameservers)
            description = "DNS servers (nameservers) are responsible for translating your domain name to an IP address."
            passed_check = True # Assume pass unless specific failure

            if count < 2:
                recommendation_text = "You only have one nameserver. Add at least one more for redundancy to prevent downtime if your primary nameserver fails."
                importance = "Having multiple DNS servers is critical for website reliability. If your single nameserver fails, your website becomes completely inaccessible."
                score = 0
                priority = "High"
                recommendation = {"text": recommendation_text, "priority": priority}
                passed_check = False # Fail if less than 2
            elif count >= 2 and count < 4:
                recommendation = None
                importance = "Your multiple DNS servers provide good redundancy, protecting your website from DNS-related outages."
                score = max_score * 2 // 3
            else: # count >= 4
                recommendation = None
                importance = "Your extensive DNS server setup provides excellent redundancy and global distribution for optimal reliability."
                score = max_score
            return {
                "pass": passed_check,
                "nameservers": nameservers,
                "count": count,
                "description": description,
                "recommendation": recommendation,
                "importance": importance,
                "blog": "",
                "score": score
            }
        except Exception as e:
            logger.error(f"Error retrieving DNS servers: {str(e)}")
            recommendation_text = "We couldn't check your DNS servers. Verify your domain's DNS configuration with your registrar."
            importance = "DNS server configuration is fundamental to website accessibility. Without proper DNS setup, users can't reach your website."
            priority = "High"
            recommendation = {"text": recommendation_text, "priority": priority}
            return {
                "pass": False,
                "nameservers": [],
                "count": 0,
                "error": str(e),
                "description": "DNS servers (nameservers) are responsible for translating your domain name to an IP address.",
                "recommendation": recommendation,
                "importance": importance,
                "blog": "",
                "score": 0
            }

    def _get_web_server_info(self) -> dict:
        logger.info("Analyzing web server information")
        server = self.headers.get("Server", "")
        powered_by = self.headers.get("X-Powered-By", "")
        info = server if server else "Unknown"
        if powered_by:
            info += f" (Powered by: {powered_by})"
        description = "The web server software handles HTTP requests and serves your website content to visitors."
        max_score = self.MAX_SCORES.get('web_server', 10)
        score = max_score
        recommendation_text = ""
        priority = "Low"
        recommendation = None
        passed = not (server or powered_by)
        importance = ""

        if passed:
            recommendation = None
            score = max_score
            importance = "Hiding server information (like 'Server' and 'X-Powered-By' headers) is a good security practice as it avoids revealing specific software versions that might have known vulnerabilities."
        else:
            importance = "Exposing server information can potentially reveal vulnerabilities to attackers. This information reveals the technologies powering your website."
            recommendation_text = "Consider hiding detailed server information to enhance security by configuring your server to not send these headers."
            score = max_score // 2
            priority = "Low"
            recommendation = {"text": recommendation_text, "priority": priority}
        
        return {
            "pass": passed,
            "server": info,
            "description": description,
            "recommendation": recommendation,
            "importance": importance,
            "blog": "",
            "score": score
        }

    def _get_charset_info(self) -> dict:
        logger.info("Analyzing character encoding")
        charset = None
        charset_meta = self.soup.find("meta", attrs={"charset": True})
        if charset_meta:
            charset = charset_meta.get("charset")
        else:
            meta = self.soup.find("meta", attrs={"http-equiv": "Content-Type"})
            if meta:
                content = meta.get("content", "")
                match = re.search(r'charset=([^\s;]+)', content)
                if match:
                    charset = match.group(1)
        if not charset:
            content_type = self.headers.get("Content-Type", "")
            match = re.search(r'charset=([^\s;]+)', content_type)
            if match:
                charset = match.group(1)
        
        effective_charset = charset if charset else "utf-8"
        is_utf8 = effective_charset.lower().startswith("utf-8") or effective_charset.lower().startswith("utf8")
        
        passed = is_utf8 and charset is not None

        description = "Character encoding defines how characters are stored in your HTML document and affects how special characters display."
        max_score = self.MAX_SCORES.get('charset', 14)
        score = 0
        recommendation_text = ""
        priority = "Low"
        recommendation = None
        
        final_charset_reported = charset if charset else "Not specified (defaults to UTF-8 by modern browsers)"

        if passed:
            importance = "UTF-8 supports international characters and is the recommended encoding for the modern web, ensuring proper display of content across all languages."
            score = max_score
            recommendation = None
        else:
            if charset and (charset.lower() in ["iso-8859-1"]):
                importance = "ISO-8859-1 is a standard encoding that works well for Western European languages but has limitations for international content."
                recommendation_text = "Consider switching to UTF-8 encoding for better international character support."
                score = max_score * 2 // 3
                priority = "Medium"
            elif not charset:
                 importance = "Character encoding is not explicitly set. While modern browsers default to UTF-8, explicitly setting it is best practice."
                 recommendation_text = "Set character encoding to UTF-8 in your HTML or HTTP headers for best compatibility."
                 score = max_score // 2
                 priority = "Medium"
            else:
                importance = "Non-standard character encodings can cause display issues with special characters and symbols, especially for international visitors."
                recommendation_text = "Use UTF-8 encoding for the best compatibility with all languages and modern browsers."
                score = 0
                priority = "High"
            recommendation = {"text": recommendation_text, "priority": priority}
            
        is_standard_reported = charset.lower() in ["utf-8", "utf8", "iso-8859-1"] if charset else False

        source = "meta tag" if charset_meta else ("HTTP header" if "Content-Type" in self.headers and 'charset=' in self.headers["Content-Type"] else "Not specified")
        
        return {
            "pass": passed,
            "charset": final_charset_reported,
            "source": source,
            "is_standard": is_standard_reported,
            "description": description,
            "importance": importance,
            "recommendation": recommendation,
            "blog": "",
            "score": score
        }

    def _get_dmarc_record(self) -> dict:
        logger.info(f"Analyzing DMARC record for {self.domain}")
        max_score = self.MAX_SCORES.get('dmarc_record', 5)
        score = 0
        recommendation_text = ""
        priority = "Low"
        recommendation = None
        has_record = False
        policy = "none"

        try:
            dmarc_domain = f"_dmarc.{self.domain}"
            answers = dns.resolver.resolve(dmarc_domain, 'TXT')
            records = []
            for rdata in answers:
                record_text = rdata.to_text().strip('"')
                if record_text.startswith("v=DMARC1"):
                    records.append(record_text)
            
            if records:
                has_record = True
                match = re.search(r'p=(\w+)', records[0])
                if match:
                    policy = match.group(1).lower()

            description = "DMARC (Domain-based Message Authentication, Reporting & Conformance) helps protect your domain from email spoofing and phishing attacks."
            
            if not has_record:
                importance = "Without DMARC, your domain is vulnerable to email spoofing, which can damage your brand reputation and lead to phishing attacks targeting your customers."
                recommendation_text = "Set up a DMARC record to protect your domain from email spoofing and improve email deliverability."
                score = 0
                priority = "High"
                recommendation = {"text": recommendation_text, "priority": priority}
            else:
                if policy == "reject":
                    importance = "Your DMARC 'reject' policy provides maximum protection against email spoofing by instructing receiving servers to reject suspicious emails."
                    score = max_score
                    recommendation = None
                elif policy == "quarantine":
                    importance = "Your DMARC 'quarantine' policy provides moderate protection by instructing receiving servers to treat suspicious emails with caution."
                    recommendation_text = "Your DMARC policy is providing good protection. Consider moving to a 'reject' policy for maximum security once you're confident legitimate emails aren't affected."
                    score = max_score * 2 // 3
                    priority = "Low"
                    recommendation = {"text": recommendation_text, "priority": priority}
                else:
                    importance = "Your DMARC record is configured to monitor potential abuse without taking action (or has a less strict policy), which provides visibility but limited protection."
                    recommendation_text = "Consider strengthening your DMARC policy from 'none' to 'quarantine' or 'reject' once you've analyzed the monitoring reports."
                    score = max_score // 3
                    priority = "Medium"
                    recommendation = {"text": recommendation_text, "priority": priority}
            return {
                "pass": has_record and policy == "reject",
                "record": records[0] if records else None,
                "policy": policy if has_record else None,
                "description": description,
                "importance": importance,
                "recommendation": recommendation,
                "blog": "",
                "score": score
            }
        except Exception as e:
            logger.error(f"Error retrieving DMARC record: {str(e)}")
            recommendation_text = "Implement a DMARC record to improve email security and deliverability. We encountered an error trying to check it."
            importance = "DMARC is crucial for protecting your brand reputation by preventing others from sending emails that appear to come from your domain."
            priority = "High"
            recommendation = {"text": recommendation_text, "priority": priority}
            return {
                "pass": False,
                "record": None,
                "policy": None,
                "error": str(e),
                "description": "DMARC (Domain-based Message Authentication, Reporting & Conformance) helps protect your domain from email spoofing and phishing attacks.",
                "importance": importance,
                "recommendation": recommendation,
                "blog": "",
                "score": 0
            }

    def _get_spf_record(self) -> dict:
        logger.info(f"Analyzing SPF record for {self.domain}")
        max_score = self.MAX_SCORES.get('spf_record', 5)
        score = 0
        recommendation_text = ""
        priority = "Low"
        recommendation = None
        has_record = False
        policy_strength = "none"

        try:
            answers = dns.resolver.resolve(self.domain, 'TXT')
            records = []
            for rdata in answers:
                record_text = rdata.to_text().strip('"')
                if record_text.startswith("v=spf1"):
                    records.append(record_text)
            
            if records:
                has_record = True
                if " -all" in records[0]:
                    policy_strength = "strict"
                elif " ~all" in records[0]:
                    policy_strength = "moderate"
                elif " ?all" in records[0]:
                    policy_strength = "neutral"
                elif " +all" in records[0]:
                    policy_strength = "permissive"

            description = "SPF (Sender Policy Framework) specifies which servers are authorized to send email on behalf of your domain."
            
            if not has_record:
                importance = "Without SPF, emails from your domain are more likely to be marked as spam and your domain is vulnerable to spoofing attacks."
                recommendation_text = "Set up an SPF record to improve email deliverability and prevent unauthorized use of your domain for sending emails."
                score = 0
                priority = "High"
                recommendation = {"text": recommendation_text, "priority": priority}
            else:
                if policy_strength == "strict":
                    importance = "Your '-all' directive provides maximum protection by instructing receiving servers to reject emails from unauthorized sources."
                    score = max_score
                    recommendation = None
                elif policy_strength == "moderate":
                    importance = "Your '~all' directive provides good protection by suggesting that unauthorized emails should be marked as suspicious."
                    recommendation_text = "Your SPF record provides good protection. Consider using '-all' for maximum security if all legitimate email sources are included in your SPF record."
                    score = max_score * 2 // 3
                    priority = "Low"
                    recommendation = {"text": recommendation_text, "priority": priority}
                elif policy_strength == "neutral":
                    importance = "Your '?all' directive provides minimal protection as it tells receiving servers to take no action on unauthorized emails."
                    recommendation_text = "Replace '?all' with '~all' or '-all' in your SPF record for better protection against email spoofing."
                    score = max_score // 3
                    priority = "Medium"
                    recommendation = {"text": recommendation_text, "priority": priority}
                else:
                    importance = "Your SPF record is either too permissive (e.g., using '+all') or not configured with a strong mechanism ('-all' or '~all'), offering little to no protection against spoofing."
                    recommendation_text = "Strengthen your SPF record. Ensure it ends with '~all' (softfail) or ideally '-all' (hardfail) and does not use '+all'."
                    score = 0
                    priority = "High"
                    recommendation = {"text": recommendation_text, "priority": priority}
            return {
                "pass": has_record and policy_strength == "strict",
                "record": records[0] if records else None,
                "policy_strength": policy_strength if has_record else None,
                "description": description,
                "importance": importance,
                "recommendation": recommendation,
                "blog": "",
                "score": score
            }
        except Exception as e:
            logger.error(f"Error retrieving SPF record: {str(e)}")
            recommendation_text = "Set up an SPF record to improve email deliverability and security. We encountered an error trying to check it."
            importance = "SPF records are essential for email deliverability and preventing your domain from being used in phishing attempts."
            priority = "High"
            recommendation = {"text": recommendation_text, "priority": priority}
            return {
                "pass": False,
                "record": None,
                "policy_strength": None,
                "error": str(e),
                "description": "SPF (Sender Policy Framework) specifies which servers are authorized to send email on behalf of your domain.",
                "importance": importance,
                "recommendation": recommendation,
                "blog": "",
                "score": 0
            }

    def _get_server_ip(self) -> dict:
        logger.info(f"Resolving server IP for {self.domain}")
        max_score = self.MAX_SCORES.get('server_ip', 7)
        score = 0
        recommendation_text = ""
        priority = "Low"
        recommendation = None
        primary_ip = None
        ip_addresses = []
        passed = False

        try:
            ip_addresses = socket.gethostbyname_ex(self.domain)[2]
            primary_ip = ip_addresses[0] if ip_addresses else None
            description = "The server IP address is the numerical label assigned to your web server that allows users to connect to your website."
            
            if primary_ip:
                ip_count = len(ip_addresses)
                if ip_count > 1:
                    importance = "Multiple IP addresses indicate load balancing or geographic distribution, which can improve reliability and performance."
                    score = max_score
                    passed = True
                    recommendation = None
                else: # ip_count == 1
                    importance = "Your single IP address is the unique identifier for your web server. Having only one IP can create a single point of failure."
                    recommendation_text = "Consider implementing a redundant hosting solution with multiple IPs for improved reliability."
                    score = max_score * 2 // 3
                    priority = "Medium"
                    passed = True # Still passes the check of having an IP, but recommendation given
                    recommendation = {"text": recommendation_text, "priority": priority}
            else: # No primary_ip
                importance = "A resolvable IP address is fundamental for website accessibility."
                recommendation_text = "We couldn't resolve any IP addresses for your domain. Check your DNS configuration."
                score = 0
                priority = "High"
                passed = False
                recommendation = {"text": recommendation_text, "priority": priority}
            
            return {
                "pass": passed and ip_count > 1, # Pass is only true if multiple IPs and resolution was successful
                "ip": primary_ip,
                "all_ips": ip_addresses,
                "description": description,
                "importance": importance,
                "recommendation": recommendation,
                "blog": "",
                "score": score
            }
        except Exception as e:
            logger.error(f"Error resolving server IP: {str(e)}")
            recommendation_text = "Check your domain's DNS configuration to ensure it correctly points to your web server's IP address. We encountered an error trying to resolve it."
            importance = "Without properly configured IP addresses in DNS, your website will be inaccessible to visitors."
            priority = "High"
            recommendation = {"text": recommendation_text, "priority": priority}
            return {
                "pass": False,
                "ip": None,
                "all_ips": [],
                "error": str(e),
                "description": "The server IP address is the numerical label assigned to your web server that allows users to connect to your website.",
                "importance": importance,
                "recommendation": recommendation,
                "blog": "",
                "score": 0
            }

    def _get_technology_stack(self) -> dict:
        logger.info("Analyzing technology stack")
        techs = []
        content_lower = self.html.lower()
        max_score = self.MAX_SCORES.get('technologies', 14)
        score = 0
        if "wp-content" in content_lower or "wp-includes" in content_lower:
            techs.append({"name": "WordPress", "version": "Unknown", "category": "CMS"})
        if "drupal" in content_lower:
            techs.append({"name": "Drupal", "version": "Unknown", "category": "CMS"})
        if "joomla" in content_lower:
            techs.append({"name": "Joomla", "version": "Unknown", "category": "CMS"})
        if "magento" in content_lower:
            techs.append({"name": "Magento", "version": "Unknown", "category": "E-commerce"})
        if "shopify" in content_lower or "cdn.shopify.com" in content_lower:
            techs.append({"name": "Shopify", "version": "Cloud", "category": "E-commerce"})
        if "wix" in content_lower or "wixsite.com" in content_lower:
            techs.append({"name": "Wix", "version": "Cloud", "category": "Website Builder"})
        if "squarespace" in content_lower:
            techs.append({"name": "Squarespace", "version": "Cloud", "category": "Website Builder"})
        if "webflow" in content_lower:
            techs.append({"name": "Webflow", "version": "Cloud", "category": "Website Builder"})
        if "jquery" in content_lower:
            match = re.search(r'jquery[-\.]?([0-9\.]+)', content_lower)
            version = match.group(1) if match else "Unknown"
            techs.append({"name": "jQuery", "version": version, "category": "JavaScript Library"})
        if "bootstrap" in content_lower:
            match = re.search(r'bootstrap[-\.]?([0-9\.]+)', content_lower)
            version = match.group(1) if match else "Unknown"
            techs.append({"name": "Bootstrap", "version": version, "category": "UI Framework"})
        if "react" in content_lower and ("reactdom" in content_lower.replace(" ", "") or "react-dom" in content_lower):
            techs.append({"name": "React", "version": "Detected", "category": "JavaScript Framework"})
        if "vue" in content_lower and (re.search(r'vue\.js', content_lower) or re.search(r'vue@', content_lower)):
            techs.append({"name": "Vue.js", "version": "Detected", "category": "JavaScript Framework"})
        if "angular" in content_lower:
            techs.append({"name": "Angular", "version": "Detected", "category": "JavaScript Framework"})
        if "tailwind" in content_lower:
            techs.append({"name": "Tailwind CSS", "version": "Detected", "category": "CSS Framework"})
        if "google-analytics" in content_lower or "gtag" in content_lower or "ga(" in content_lower:
            techs.append({"name": "Google Analytics", "version": "Detected", "category": "Analytics"})
        if "gtm.js" in content_lower or "googletagmanager" in content_lower:
            techs.append({"name": "Google Tag Manager", "version": "Detected", "category": "Tag Management"})
        if "facebook.com/tr?" in content_lower or "connect.facebook.net" in content_lower:
            techs.append({"name": "Facebook Pixel", "version": "Detected", "category": "Analytics"})
        if "hotjar" in content_lower:
            techs.append({"name": "Hotjar", "version": "Detected", "category": "Analytics"})
        if "hubspot" in content_lower:
            techs.append({"name": "HubSpot", "version": "Detected", "category": "Marketing"})
        if "matomo" in content_lower or "piwik" in content_lower:
            techs.append({"name": "Matomo/Piwik", "version": "Detected", "category": "Analytics"})
        if "cloudflare" in content_lower or "cloudflare-nginx" in str(self.headers).lower():
            techs.append({"name": "Cloudflare", "version": "Detected", "category": "CDN/Security"})
        if "akamai" in content_lower or "akamai" in str(self.headers).lower():
            techs.append({"name": "Akamai", "version": "Detected", "category": "CDN"})
        if "fastly" in content_lower or "fastly" in str(self.headers).lower():
            techs.append({"name": "Fastly", "version": "Detected", "category": "CDN"})
        if "cloudfront" in content_lower or "cloudfront.net" in content_lower:
            techs.append({"name": "Amazon CloudFront", "version": "Detected", "category": "CDN"})
        server = self.headers.get("Server", "").lower()
        if "apache" in server:
            techs.append({"name": "Apache", "version": server.replace("apache/", ""), "category": "Web Server"})
        elif "nginx" in server:
            techs.append({"name": "Nginx", "version": server.replace("nginx/", ""), "category": "Web Server"})
        elif "iis" in server or "microsoft-iis" in server:
            techs.append({"name": "Microsoft IIS", "version": server.replace("microsoft-iis/", ""), "category": "Web Server"})

        recommendations = []
        if any(tech["name"] == "WordPress" for tech in techs):
            recommendations.append({"text": "Keep WordPress core, themes, and plugins updated to ensure security and performance.", "priority": "Medium"})
        if any(tech["category"] == "JavaScript Framework" for tech in techs):
            recommendations.append({"text": "Consider implementing server-side rendering (SSR) or static site generation (SSG) to improve SEO for JavaScript frameworks.", "priority": "Medium"})
        if any(tech["name"] == "Cloudflare" for tech in techs):
            recommendations.append({"text": "Optimize your Cloudflare settings by enabling features like Brotli compression and Auto Minify.", "priority": "Low"})
        if not any(tech["category"] == "CDN" for tech in techs) and not any(tech["name"] == "Cloudflare" for tech in techs): 
            recommendations.append({"text": "Consider implementing a CDN to improve page load speed for global visitors.", "priority": "Low"})
        if not any(tech["category"] == "Analytics" for tech in techs):
            recommendations.append({"text": "Add an analytics solution to track user behavior and site performance.", "priority": "Low"})

        importance = "Your technology stack affects website performance, security, and SEO. "
        if len(techs) > 0:
            importance += "Regular maintenance of these technologies will ensure optimal website health. "
            if any(tech["category"] in ["CMS", "E-commerce", "Website Builder"] for tech in techs):
                importance += "CMS and website platforms should be kept updated to prevent security vulnerabilities. "
            if any(tech["category"] in ["JavaScript Framework", "JavaScript Library"] for tech in techs):
                importance += "JavaScript frameworks can impact page load performance and SEO if not optimized properly. "
            if any(tech["category"] in ["CDN", "CDN/Security"] for tech in techs):
                importance += "CDN usage helps improve global site speed and security."
        else:
            importance += "We could not detect common technologies, which might indicate a custom solution or limited technology footprint."

        if not recommendations:
            recommendations.append({"text": "Regularly update your technology stack to maintain security and performance.", "priority": "Medium"})

        if len(techs) > 0:
            score = max_score
            passed_check = True
        else:
            score = 0
            passed_check = False
            recommendations = None # Set to None if no techs found

        return {
            "pass": passed_check,
            "technologies": techs,
            "technology_count": len(techs),
            "description": "Analysis of the technologies and frameworks used by your website, including CMS platforms, JavaScript libraries, analytics tools, and infrastructure services.",
            "recommendations": recommendations, # Will be None if passed_check is False
            "importance": importance,
            "blog": "",
            "score": score
        }

    def run_all(self) -> dict:
        logger.info(f"[TECHNOLOGY REVIEW ANALYSIS] Running complete analysis for URL: {self.url}")
        try:
            analysis_result = self.analyze()
            result = {
                "ssl_enabled": {
                    "pass": analysis_result.get("ssl_enabled", False),
                    "description": "SSL (Secure Sockets Layer) encrypts the connection between your website and visitors, protecting sensitive data and improving security.",
                    "importance": "SSL is crucial for website security, user trust, and SEO. Google uses HTTPS as a ranking signal, and browsers warn users when visiting non-HTTPS sites.",
                    "recommendation": None if analysis_result.get("ssl_enabled", False) else {
                        "text": "Enable SSL/HTTPS for your website to improve security, user trust, and search rankings.",
                        "priority": "High"
                    },
                    "blog": ""
                },
                "robots_meta": analysis_result.get("robots_meta", {}),
                "dns_servers": analysis_result.get("dns_servers", {}),
                "web_server": analysis_result.get("web_server", {}),
                "charset": analysis_result.get("charset", {}),
                "dmarc_record": analysis_result.get("dmarc_record", {}),
                "spf_record": analysis_result.get("spf_record", {}),
                "server_ip": analysis_result.get("server_ip", {}),
                "technologies": analysis_result.get("technologies", {})
            }
            total_score = 0
            total_possible = sum(self.MAX_SCORES.values())
            
            ssl_is_enabled = result.get("ssl_enabled", {}).get("pass", False)
            ssl_score = self.MAX_SCORES.get('ssl_enabled', 20) if ssl_is_enabled else 0
            total_score += ssl_score
            result["ssl_enabled"]["score"] = ssl_score

            for key, metric_data in result.items():
                if key == "ssl_enabled":
                    continue
                if key in self.MAX_SCORES and isinstance(metric_data, dict):
                    current_score = metric_data.get('score', 0)
                    total_score += current_score
                    
                    if metric_data.get('recommendation') is not None:
                        issue_text = metric_data['recommendation'].get('text', f"Issue with {key}")
                        issue_priority = metric_data['recommendation'].get('priority', "Low")
                        
                        if key == "robots_meta" and metric_data.get("noindex", False):
                            pass
                        elif key == "dns_servers" and metric_data.get("count", 2) < 2:
                            pass
                        elif key == "charset" and not metric_data.get("pass", True):
                            pass
                        elif key == "dmarc_record" and not metric_data.get("pass", True):
                            pass
                        elif key == "spf_record" and not metric_data.get("pass", True):
                            pass
                        elif key == "server_ip" and not metric_data.get("pass", True):
                            pass
                        elif key == "web_server" and metric_data.get("pass", True) is False:
                            pass
                        elif key not in ["robots_meta", "dns_servers", "charset", "dmarc_record", "spf_record", "server_ip", "web_server", "technologies"]:
                            pass

            percentage = round((total_score / total_possible) * 100) if total_possible > 0 else 0
           
            if percentage >= 98:
                grade = 'A+'
            elif percentage >= 95:
                grade = 'A'
            elif percentage >= 90:
                grade = 'A-'
            elif percentage >= 85:
                grade = 'B+'
            elif percentage >= 80:
                grade = 'B'
            elif percentage >= 75:
                grade = 'B-'
            elif percentage >= 70:
                grade = 'C+'
            elif percentage >= 60:
                grade = 'C'
            elif percentage >= 50:
                grade = 'C-'
            elif percentage >= 40:
                grade = 'D'
            else: 
                grade = 'F'

            result['total_score'] = {
                'score': total_score,
                'grade': grade,
            }
            logger.info(f"[TECHNOLOGY REVIEW ANALYSIS] Analysis completed successfully. Score: {total_score}/{total_possible} ({percentage}%, Grade: {grade})")

            if grade in ['A+', 'A', 'A-']:
                overall_title = "Your Technology Review is Excellent"
                overall_description = "Your website demonstrates excellent Technology Review. Only minor improvements may be needed."
            elif grade in ['B+', 'B', 'B-']:
                overall_title = "Your Technology Review is Good"
                overall_description = "Your website has good Technology Review, but there are some areas that could be improved for even better results."
            elif grade in ['C+', 'C']:
                overall_title = "Your Technology Review Could Be Better"
                overall_description = "Your website has some Technology Review optimisation, but there are several areas that need attention to improve search visibility and reliability."
            elif grade == 'F':
                overall_title = "Your Technology Review is Poor"
                overall_description = "Your website is missing critical Technology Review elements. Immediate action is required to improve your site's visibility and security."
            else:
                overall_title = "Your Technology Review Needs Improvement"
                overall_description = "Your website has significant Technology Review issues. Address the key recommendations to improve your search visibility and reliability."

            result['overall_title'] = overall_title
            result['overall_description'] = overall_description

            return result
        except Exception as e:
            logger.error(f"[TECHNOLOGY REVIEW ANALYSIS] Error in run_all method: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "error": str(e),
                "recommendations": ["An error occurred during Technology Review analysis."]
            }