import re
import json
import logging
from collections import defaultdict
from bs4 import BeautifulSoup

# try:
#     import phonenumbers
#     HAS_PHONENUMBERS = True
# except ImportError:
#     HAS_PHONENUMBERS = False

logger = logging.getLogger(__name__)

class LocalSEOAnalyzer:
    MAX_SCORES = {
        # "contact_info": {
        #     "has_address": 20,
        #     "has_phone": 15,
        #     "max_score": 35
        # },
        # "business_schema": {
        #     "has_local_business_schema": 20,
        #     "schema_types_present": 10,
        #     "max_score": 30
        # },
        "google_business": {
            "has_gmb_links": 0,
            "has_gmb_embeds": 0,
            "has_gbp_mentions": 0,
            "max_score": 0
        }
    }
    
    def __init__(self, html_content: str, url: str = None, task_id: str = None):
        self.html_content = html_content
        self.url = url
        self.task_id = task_id
        logger.info(f"LocalSEOAnalyzer initialized for URL: {url}")
        
        try:
            import redis
            from django.conf import settings
            import os
            
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=os.getenv('REDIS_PASSWORD', None),
                decode_responses=False
            )
            self.redis_client.ping()
            logger.info("Redis connection successful in LocalSEOAnalyzer")
            self.redis_available = True
        except Exception as e:
            logger.warning(f"Redis connection failed in LocalSEOAnalyzer: {e}")
            self.redis_available = False
    
    def _save_to_redis(self, key, data):
        if hasattr(self, 'redis_available') and self.redis_available and self.task_id:
            try:
                import json
                self.redis_client.hset(self.task_id, key, json.dumps(data))
                return True
            except Exception as e:
                logger.error(f"Failed to save to Redis: {e}")
        return False
    
    @staticmethod
    def is_valid_phone(phone: str) -> bool:
        cleaned = re.sub(r'[^\d+]', '', phone)
        return 7 <= len(cleaned) <= 15
    
    def extract_google_business_info(self, html_content: str) -> dict:
        soup = BeautifulSoup(html_content, 'lxml')
        text = soup.get_text(separator=" ", strip=True)
        has_gmb_links = False
        gmb_urls = []
        
        gmb_patterns = [
            r'https?://www\.google\.com/maps/place/[^"\s]+',
            r'https?://www\.google\.com/maps/search/[^"\s]+',
            r'https?://www\.google\.com/maps/dir/[^"\s]+',
            r'https?://www\.google\.com/maps/embed\?[^"\s]+',
            r'https?://goo\.gl/maps/[^"\s]+',
            r'https?://maps\.app\.goo\.gl/[^"\s]+',
            r'https?://maps\.google\.com/[^"\s]+'
        ]
        
        for pattern in gmb_patterns:
            matches = re.findall(pattern, text)
            if matches:
                has_gmb_links = True
                gmb_urls.extend(matches)
                
        for a in soup.find_all("a", href=True):
            href = a["href"]
            if any(domain in href.lower() for domain in ["google.com/maps", "goo.gl/maps", "maps.app.goo.gl", "maps.google.com"]):
                if not any(href in url for url in gmb_urls):
                    has_gmb_links = True
                    gmb_urls.append(href)
                    
        gmb_embeds = []
        for iframe in soup.find_all("iframe", src=True):
            src = iframe["src"]
            if any(domain in src.lower() for domain in ["google.com/maps", "maps.google.com"]):
                gmb_embeds.append(src)
                
        has_gmb_embeds = len(gmb_embeds) > 0
        
        gbp_mentions = re.search(r'(?:google business profile|google my business|our google listing)', text, re.IGNORECASE)
        has_gbp_mentions = bool(gbp_mentions)
        
        find_on_google_links = soup.find_all(string=re.compile(r'(?:find|see|view) (?:us|our business|our office) on google', re.IGNORECASE))
        has_find_on_google = len(find_on_google_links) > 0
        
        has_gmb_references = has_gmb_links or has_gmb_embeds or has_gbp_mentions or has_find_on_google

        recommendation_text = ""
        priority = "Low"
        recommendation_obj = None
        passed = has_gmb_references

        if not passed:
            recommendation_text = "Add Google Maps links or embeds to connect your website with your Google Business Profile and strengthen local signals."
            priority = "Medium"
            recommendation_obj = {"text": recommendation_text, "priority": priority}
        else:
            recommendation_obj = None
        
        return {
            "pass": passed,
            "has_gmb_references": has_gmb_references,
            "has_gmb_links": has_gmb_links,
            "has_gmb_embeds": has_gmb_embeds,
            "has_gbp_mentions": has_gbp_mentions,
            "gmb_urls": gmb_urls[:5] if len(gmb_urls) > 5 else gmb_urls,
            "gmb_embeds": gmb_embeds[:2] if len(gmb_embeds) > 2 else gmb_embeds,
            "description": "This analysis checks for Google Business Profile integration. Links and embeds improve local search visibility and provide location context for visitors.",
            "recommendation": recommendation_obj,
            "importance": "Google Business Profile references strengthen local search signals, build user trust, and enhance location-based search performance.",
            "blog": ""
        }
    
    def get_google_business(self) -> dict:
        logger.info("Analyzing Google Business Profile references")
        extracted_data = self.extract_google_business_info(self.html_content)

        score = 0
        extracted_data['score'] = score

        result = {
            "google_business": extracted_data
        }
        return result
    
    def run_all(self) -> dict:
        logger.info(f"[LOCAL SEO ANALYSIS] Running complete analysis for URL: {self.url}")
        
        results = {}
        calculated_scores = {}

        try:
            # contact_info_result = self.get_contact_info()
            # results["contact_info"] = contact_info_result.get("contact_info", {})
            # calculated_scores["contact_info"] = results["contact_info"].get("score", 0)
            
            # business_schema_result = self.get_business_schema()
            # results["business_schema"] = business_schema_result.get("business_schema", {})
            # calculated_scores["business_schema"] = results["business_schema"].get("score", 0)
            
            google_business_result = self.get_google_business()
            results["google_business"] = google_business_result.get("google_business", {})
            
            results["pass"] = True
            
            if hasattr(self, 'redis_available') and self.redis_available and self.task_id:
                try:
                    import json
                    cache_key = f"localseo-result:{self.task_id}" 
                    self.redis_client.set(cache_key, json.dumps(results, default=str), ex=3600)
                    logger.info(f"Cached local SEO analysis results in Redis with key {cache_key}")
                except Exception as e:
                    logger.error(f"Failed to cache local SEO analysis results: {e}")
            
            logger.info(f"[LOCAL SEO ANALYSIS] Analysis completed successfully for URL: {self.url}. Scoring and grading removed from this module.")
            return results
            
        except Exception as e:
            logger.error(f"[LOCAL SEO ANALYSIS] Error in run_all method: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            error_result = {
                "pass": False,
                "error": str(e),
                "recommendations": ["An error occurred during local SEO analysis."],
            }
            
            if hasattr(self, 'redis_available') and self.redis_available and self.task_id:
                try:
                    import json
                    cache_key = f"localseo-result:{self.task_id}"
                    self.redis_client.set(cache_key, json.dumps(error_result, default=str), ex=3600)
                    logger.info(f"Cached local SEO error results in Redis with key {cache_key}")
                except Exception as redis_e:
                    logger.error(f"Failed to cache local SEO error results: {redis_e}")
            
            return error_result
