import asyncio
from playwright.async_api import async_playwright, TimeoutError
from typing import Dict, Any, Optional, List
import logging
import base64
import io
# from django.core.cache import cache # Commenting out, as redis_client is preferred
from colorama import Fore, Style
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


STEALTH_INIT_SCRIPT = """
Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
window.chrome = { runtime: {} };
Object.defineProperty(navigator, 'languages', { get: () => ['en-US', 'en'] });
Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5] });
"""

class PageContentFetcher:
    def __init__(self, 
                 redis_client_instance,
                 viewport: Dict[str, int] = None,
                 user_agent: str = None,
                 proxy_string: Optional[str] = None):

        self.redis_client = redis_client_instance
        self.viewport = viewport or {"width": 1920, "height": 1080}
        self.user_agent = user_agent or "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.166 Safari/537.36"
        self.proxy = self._parse_proxy_string(proxy_string)
        
        self.viewports = {
            'desktop': {"width": 1920, "height": 1080},
            'tablet': {"width": 768, "height": 1024},
            'mobile': {"width": 375, "height": 667}
        }
    
    def _parse_proxy_string(self, proxy_string: Optional[str]) -> Optional[Dict[str, str]]:
        if not proxy_string:
            return None

        match = re.match(r'^([^:]+):(\d+):([^:]+):(.+)$', proxy_string)
        if match:
            host, port, username, password = match.groups()
            return {
                "server": f"http://{host}:{port}",
                "username": username,
                "password": password
            }
        else:
            logger.warning(f"Invalid proxy string format: {proxy_string}. Expected HOST:PORT:USER:PASS.")
            return None

    async def get_page_content(self,
                              url: str, 
                              timeout: int = 60000, 
                              wait_until: str = 'load',
                              headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        result = {
            "url": url,
            "html": "",
            "title": "",
            "success": False,
            "error": None,
            "screenshots": {},
            "console": []
        }
        
        browser = None
        context = None
        page = None

        async def attempt_fetch(use_proxy: bool, attempt_timeout: int):
            nonlocal browser, context, page, result
            browser_options = {
                "headless": True,
                "channel": "chrome"
            }
            context_options = {
                "viewport": self.viewport,
                "user_agent": self.user_agent,
                "ignore_https_errors": True
            }

            if use_proxy and self.proxy:
                logger.info(f"Attempting fetch using proxy: {self.proxy['server']}")
                context_options["proxy"] = self.proxy
            else:
                logger.info("Attempting fetch directly (no proxy)")

            try:
                async with async_playwright() as p:
                    browser = await p.chromium.launch(**browser_options)
                    context = await browser.new_context(**context_options)
                    await context.add_init_script(STEALTH_INIT_SCRIPT)
                    
                    if headers:
                        await context.set_extra_http_headers(headers)
                    
                    page = await context.new_page()
                    page.on("console", lambda msg: result["console"].append({"type": msg.type, "text": msg.text}))
                    
                    logger.info(f"Navigating to {url} (wait_until='{wait_until}', timeout={attempt_timeout}ms)")
                    response = await page.goto(url, timeout=attempt_timeout, wait_until=wait_until)
                    
                    if response and response.status == 403:
                        logger.warning(f"Access forbidden (403) for {url}")
                        result["error"] = "Access forbidden - You don't have permission to access this resource"
                        result["status_code"] = 403
                        return False
                    
                    if not response:
                        result["error"] = "No response received"
                        return False
                    
                    if not response.ok:
                        status_messages = {
                            404: "Page not found - The requested URL does not exist",
                            500: "Internal server error - The server encountered an error",
                            502: "Bad gateway - The server received an invalid response",
                            503: "Service unavailable - The server is temporarily unable to handle the request"
                        }
                        error_message = status_messages.get(response.status, f"HTTP {response.status}: {response.status_text}")
                        result["error"] = error_message
                        result["status_code"] = response.status
                        return False
                    
                    for device_type in ['desktop', 'tablet', 'mobile']:
                        logger.info(f"Taking {device_type} screenshot for {url}")
                        await page.set_viewport_size(self.viewports[device_type])
                        await asyncio.sleep(1)
                        screenshot_bytes = await page.screenshot(full_page=False)
                        screenshot_b64 = base64.b64encode(screenshot_bytes).decode('utf-8')
                        cache_key_base = hex(hash(url))[2:]
                        cache_key = f"screenshot:{cache_key_base}:{device_type}"
                        self.redis_client.set(cache_key, screenshot_b64, ex=86400)
                        result["screenshots"][device_type] = cache_key
                        print(f"{Fore.GREEN}✓ {device_type}{Fore.CYAN} screenshot cached with key {Fore.YELLOW}{cache_key}{Style.RESET_ALL}")
                    
                    result["html"] = await page.content()
                    result["title"] = await page.title()
                    result["success"] = True
                    result["error"] = None
                    return True
                    
            except TimeoutError as e:
                logger.warning(f"TimeoutError during navigation: {str(e)}")
                result["error"] = str(e)
                return False
            except Exception as e:
                logger.error(f"Unexpected error during fetch attempt: {str(e)}")
                result["error"] = str(e)
                return False
            finally:
                if page and not page.is_closed():
                    try: await page.close()
                    except Exception as page_close_err: logger.warning(f"Error closing page: {page_close_err}")
                if context:
                    try: await context.close()
                    except Exception as context_close_err: logger.warning(f"Error closing context: {context_close_err}")
                if browser and browser.is_connected():
                    try: await browser.close()
                    except Exception as browser_close_err: logger.warning(f"Error closing browser: {browser_close_err}")

        direct_timeout = 20000
        logger.info(f"--- Starting Direct Fetch Attempt (Timeout: {direct_timeout}ms) ---")
        success = await attempt_fetch(use_proxy=False, attempt_timeout=direct_timeout)

        if not success and self.proxy:
            proxy_timeout = 20000
            logger.info(f"--- Direct Fetch Failed. Starting Proxy Fetch Attempt (Timeout: {proxy_timeout}ms) ---")
            success = await attempt_fetch(use_proxy=True, attempt_timeout=proxy_timeout)
            if not success:
                 logger.error(f"Proxy fetch also failed for {url}. Final error: {result['error']}")
        elif not success:
             logger.error(f"Direct fetch failed for {url} and no proxy configured. Final error: {result['error']}")

        
        return result
