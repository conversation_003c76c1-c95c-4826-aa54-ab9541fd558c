import logging
import redis
import json
import os
import asyncio
import random
import httpx
import textstat # Ensure textstat is imported
from bs4 import BeautifulSoup, Tag
from urllib.parse import urlparse, urljoin, quote_plus
from django.conf import settings
from ..modules.getcontent import Page<PERSON>ontentFetcher
from typing import Optional, List, Dict, Any, Union
import re
import dataforseo_client # type: ignore
from dataforseo_client.models.backlinks_summary_live_request_info import BacklinksSummaryLiveRequestInfo # type: ignore
from dataforseo_client.models.backlinks_backlinks_live_request_info import BacklinksBacklinksLiveRequestInfo # type: ignore
from dataforseo_client.models.backlinks_domain_pages_live_request_info import BacklinksDomainPagesLiveRequestInfo # type: ignore
from dataforseo_client.models.backlinks_anchors_live_request_info import BacklinksAnchorsLiveRequestInfo # type: ignore


logger = logging.getLogger(__name__)

DEFAULT_DELAY_RANGE = (0.5, 1.0) 

MAX_LINKS_SCORES = {
    "dataforseo_summary":          25, # Covers Domain/Page Strength, Total Backlinks, Referring Domains, Edu/Gov, Subnets/IPs
    "dataforseo_backlinks_detail": 20, # Covers Nofollow/Dofollow, Top Backlinks
    "dataforseo_top_pages":        15,
    "dataforseo_anchors":          10,
    "dataforseo_referring_domains":10, # Covers TLDs, Geo
    "analyze_on_page_links":       10, # Reduced score as some aspects are covered by backlink tools
    "analyze_friendly_links":      5,
    "analyze_broken_links":        5
}

class LinksAnalyzer:
    MAX_SCORES = MAX_LINKS_SCORES
    DATAFORSEO_API_BASE_URL = "https://api.dataforseo.com/v3/backlinks"

    def __init__(self, url, task_id=None, html=None, proxy_string: Optional[str] = None):
        
        # Ensure URL has a scheme
        parsed_initial_url = urlparse(url)
        if not parsed_initial_url.scheme:
            logger.info(f"URL '{url}' is missing a scheme. Prepending 'https://'.")
            url = f"https://{url}"
        
        self.url = url
        self.task_id = task_id
        self.html = html
        self.soup = None if not html else BeautifulSoup(html, 'lxml')
        self.parsed_url = urlparse(url)
        self.domain = self.parsed_url.netloc
        self.proxy_string = proxy_string # Ensure proxy_string is assigned

        self.dataforseo_login = os.getenv('DATAFORSEO_API_LOGIN')
        self.dataforseo_password = os.getenv('DATAFORSEO_API_PASSWORD')

        if not self.dataforseo_login or not self.dataforseo_password:
            logger.error("DataForSEO API credentials (DATAFORSEO_API_LOGIN, DATAFORSEO_API_PASSWORD) are not set in environment variables.")
            # Potentially raise an error or set a flag to disable DataForSEO calls

        self.redis_client = None # Initialize redis_client to None
        try:
            # Store the client instance if connection is successful
            self.redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=os.getenv('REDIS_PASSWORD', None),
                decode_responses=False
            )
            self.redis_client.ping()
            logger.info("Redis connection successful in LinksAnalyzer")
            self.redis_available = True
        except (redis.ConnectionError, AttributeError) as e:
            logger.warning(f"Redis connection failed in LinksAnalyzer: {e}")
            self.redis_available = False
        
        logger.info(f"Initialized LinksAnalyzer for {url}")
    
    def _save_to_redis(self, key, data):
        if self.redis_available and self.task_id and self.redis_client:
            try:
                self.redis_client.hset(self.task_id, key, json.dumps(data))
                return True
            except Exception as e:
                logger.error(f"Failed to save to Redis: {e}")
        return False

    def _get_dataforseo_client(self):
        configuration = dataforseo_client.Configuration(
            username=self.dataforseo_login,
            password=self.dataforseo_password,
            host="https://api.dataforseo.com"
        )
        api_client = dataforseo_client.ApiClient(configuration)
        return dataforseo_client.BacklinksApi(api_client)

    async def fetch_content(self):
        """Fetch page content using PageContentFetcher if HTML is not available"""
        if self.html:
            self.soup = BeautifulSoup(self.html, 'lxml')
            return True

        try:
            # Use PageContentFetcher to get content
            content_fetcher = PageContentFetcher(
                redis_client_instance=self.redis_client,
                proxy_string=self.proxy_string
            )
            
            result = await content_fetcher.get_page_content(self.url)
            
            if result.get('success') and result.get('html'):
                self.html = result['html']
                self.soup = BeautifulSoup(self.html, 'lxml')
                logger.info(f"Successfully fetched content for {self.url}")
                return True
            else:
                error_msg = result.get('error', 'Unknown error')
                logger.error(f"Failed to fetch content for {self.url}: {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"Exception while fetching content for {self.url}: {e}")
            return False

    async def analyze_dataforseo_summary(self):
        max_score = self.MAX_SCORES.get("dataforseo_summary", 25)
        score = 0
        default_error_result = {
            "pass": False, "description": "DataForSEO Summary data for backlink profile overview.",
            "importance": "Provides crucial metrics like Domain/Page Rank, backlink counts, referring domains, and TLD distribution.",
            "recommendation": {"text": "Error fetching DataForSEO Summary data. Check logs.", "priority": "High"},
            "blog": "", "score": 0, "data": {}
        }

        if not self.dataforseo_login or not self.dataforseo_password:
            logger.error("DataForSEO credentials not set. Skipping summary analysis.")
            return {**default_error_result, "recommendation": {"text": "DataForSEO credentials not configured.", "priority": "High"}}

        try:
            api_instance = self._get_dataforseo_client()
            request_info = [BacklinksSummaryLiveRequestInfo(target=self.domain)]
            api_result_task = await asyncio.to_thread(api_instance.summary_live, backlinks_summary_live_request_info=request_info)

            # The rest of your parsing logic remains the same, but use api_result_task as the response
            # DataForSEO SDK returns .tasks[0].result, similar to your previous logic
            if not api_result_task or not hasattr(api_result_task, 'tasks') or not api_result_task.tasks:
                logger.error(f"DataForSEO summary request failed for {self.domain} (task error or no API response).")
                return default_error_result

            task_results_list = getattr(api_result_task.tasks[0], 'result', None)
            if task_results_list is None:
                logger.warning(f"DataForSEO summary for {self.domain} returned no 'result' key (it was null/None). Assuming no data. Task data: {api_result_task}")
                return {
                    **default_error_result,
                    "recommendation": {"text": f"No summary data items found by DataForSEO for {self.domain} (result was null). The domain might be new or have no profile.", "priority": "Medium"},
                    "data": {"raw_task_response": str(api_result_task)}
                }
            if not isinstance(task_results_list, list):
                logger.error(f"DataForSEO summary for {self.domain} 'result' field was not a list. Type: {type(task_results_list)}. Task data: {api_result_task}")
                return {**default_error_result, "recommendation": {"text": "Unexpected data format from DataForSEO summary (result not a list).", "priority": "High"}}
            if not task_results_list:
                logger.info(f"DataForSEO summary for {self.domain} returned an empty 'result' list. Assuming no data for this target.")
                return {
                    **default_error_result,
                    "recommendation": {"text": f"No summary data items found by DataForSEO for {self.domain} (result list was empty). The domain might be new or have no profile.", "priority": "Medium"},
                    "data": {"raw_task_response": str(api_result_task)}
                }
            summary_data = task_results_list[0]
            if not isinstance(summary_data, dict):
                # SDK returns model objects, convert to dict
                summary_data = summary_data.to_dict() if hasattr(summary_data, 'to_dict') else dict(summary_data)

            total_backlinks = summary_data.get("backlinks", 0)
            referring_domains = summary_data.get("referring_domains", 0)
            domain_rank = summary_data.get("rank", 0)
            edu_backlinks = summary_data.get("referring_links_tld_count", {}).get("edu", 0)
            gov_backlinks = summary_data.get("referring_links_tld_count", {}).get("gov", 0)
            referring_subnets = summary_data.get("referring_subnets", 0)
            referring_ips = summary_data.get("referring_ips", 0)

            pass_criteria = domain_rank > 30 and total_backlinks > 100 and referring_domains > 20
            reco_parts = []
            priority = "Low"
            if domain_rank < 20:
                reco_parts.append(f"Domain Rank ({domain_rank}) is low. Focus on overall SEO and quality backlink acquisition.")
                priority = "High"
            elif domain_rank < 40:
                reco_parts.append(f"Domain Rank ({domain_rank}) is moderate. Continue building authority.")
                priority = "Medium"
            else:
                reco_parts.append(f"Domain Rank ({domain_rank}) is good.")
            if total_backlinks < 50:
                reco_parts.append(f"Low total backlinks ({total_backlinks}). Increase link building efforts.")
                if priority != "High": priority = "Medium"
            elif total_backlinks < 200:
                reco_parts.append(f"Moderate backlinks ({total_backlinks}). Aim for more quality links.")
                if priority == "Low": priority = "Medium"
            else:
                reco_parts.append(f"Good number of backlinks ({total_backlinks}).")
            if referring_domains < 10:
                reco_parts.append(f"Low referring domains ({referring_domains}). Diversify your link sources.")
                if priority != "High": priority = "Medium"
            elif referring_domains < 50:
                reco_parts.append(f"Moderate referring domains ({referring_domains}). Continue diversifying.")
                if priority == "Low": priority = "Medium"
            else:
                reco_parts.append(f"Good diversity of referring domains ({referring_domains}).")
            if edu_backlinks > 0 or gov_backlinks > 0:
                reco_parts.append(f"Found {edu_backlinks} .edu and {gov_backlinks} .gov backlinks, which are valuable.")
            recommendation_text = " ".join(reco_parts) if reco_parts else "Backlink profile summary looks reasonable. Continue monitoring."
            score += (min(domain_rank, 100) / 100) * (max_score * 0.3)
            score += (min(total_backlinks, 1000) / 1000) * (max_score * 0.3)
            score += (min(referring_domains, 200) / 200) * (max_score * 0.3)
            if edu_backlinks > 0: score += max_score * 0.05
            if gov_backlinks > 0: score += max_score * 0.05
            score = min(max_score, round(score))
            result = {
                "pass": pass_criteria,
                "domain_rank": domain_rank,
                "total_backlinks": total_backlinks,
                "referring_domains": referring_domains,
                "edu_backlinks": edu_backlinks,
                "gov_backlinks": gov_backlinks,
                "referring_subnets": referring_subnets,
                "referring_ips": referring_ips,
                "data": summary_data,
                "description": "Overview of the domain's backlink profile strength, including key metrics like Domain Rank, total backlinks, and referring domain counts from DataForSEO.",
                "importance": "High-level metrics from the Summary API indicate overall domain authority and link profile health. Edu/gov links and diverse IPs/subnets are positive signals.",
                "recommendation": {"text": recommendation_text, "priority": priority},
                "blog": "",
                "score": score
            }
            self._save_to_redis('dataforseo_summary', result)
            return result
        except Exception as e:
            logger.error(f"Error in analyze_dataforseo_summary for {self.domain}: {e}", exc_info=True)
            return default_error_result

    async def analyze_dataforseo_backlinks_detail(self):
        max_score = self.MAX_SCORES.get("dataforseo_backlinks_detail", 20)
        score = 0
        default_error_result = {
            "pass": False, "description": "Detailed analysis of individual backlinks, including follow status and top links.",
            "importance": "Understanding individual backlink attributes (dofollow/nofollow, anchor text, source strength) is crucial for evaluating link quality.",
            "recommendation": {"text": "Error fetching detailed backlinks data from DataForSEO. Check logs.", "priority": "High"},
            "blog": "", "score": 0, "data": {}
        }

        if not self.dataforseo_login or not self.dataforseo_password:
            return {**default_error_result, "recommendation": {"text": "DataForSEO credentials not configured.", "priority": "High"}}

        try:
            # Use DataForSEO client SDK
            api_instance = self._get_dataforseo_client()
            request_info = BacklinksBacklinksLiveRequestInfo(
                target=self.domain,
                limit=100,
                order_by=["backlink_rank,desc"]
            )
            
            # Execute the API call in a thread pool to avoid blocking
            api_result_task = await asyncio.to_thread(
                api_instance.backlinks_live,
                backlinks_backlinks_live_request_info=request_info
            )

            if not api_result_task:
                logger.error(f"DataForSEO backlinks_detail request failed for {self.domain} (task error or no API response).")
                return default_error_result

            task_results_list = api_result_task.get("result")
            if task_results_list is None:
                logger.warning(f"DataForSEO backlinks_detail for {self.domain} (Task ID: {api_result_task.get('id')}) returned no 'result' key (it was null/None). Assuming no data. Task data: {json.dumps(api_result_task, default=str)}")
                return {**default_error_result, "recommendation": {"text": f"No detailed backlinks data structure found by DataForSEO for {self.domain} (result was null).", "priority": "Medium"}, "data": {"raw_task_response": api_result_task}}

            if not isinstance(task_results_list, list):
                logger.error(f"DataForSEO backlinks_detail for {self.domain} (Task ID: {api_result_task.get('id')}) 'result' field was not a list. Type: {type(task_results_list)}. Task data: {json.dumps(api_result_task, default=str)}")
                return {**default_error_result, "recommendation": {"text": "Unexpected data format from DataForSEO backlinks_detail (result not a list).", "priority": "High"}}

            if not task_results_list: # Empty list
                logger.info(f"DataForSEO backlinks_detail for {self.domain} (Task ID: {api_result_task.get('id')}) returned an empty 'result' list. Assuming no data items for this target.")
                return {**default_error_result, "total_fetched_for_detail": 0, "top_backlinks_sample": [], "recommendation": {"text": f"No detailed backlinks found by DataForSEO for {self.domain} (result list was empty).", "priority": "Medium"}, "data": {"raw_task_response": api_result_task}}

            first_result_item = task_results_list[0]
            if not isinstance(first_result_item, dict):
                logger.error(f"DataForSEO backlinks_detail for {self.domain} (Task ID: {api_result_task.get('id')}) 'result[0]' is not a dictionary. Type: {type(first_result_item)}. Task data: {json.dumps(api_result_task, default=str)}")
                return {**default_error_result, "recommendation": {"text": "Unexpected data format for backlinks_detail item container.", "priority": "High"}}
                
            backlinks_items = first_result_item.get("items")

            if backlinks_items is None:
                logger.warning(f"DataForSEO backlinks_detail for {self.domain} (Task ID: {api_result_task.get('id')}) had a result structure, but no 'items' key or 'items' is null. First result item: {json.dumps(first_result_item, default=str)}. Assuming no individual backlinks.")
                backlinks_items = []
            
            if not isinstance(backlinks_items, list):
                logger.error(f"DataForSEO backlinks_detail for {self.domain} (Task ID: {api_result_task.get('id')}) 'items' is not a list: {type(backlinks_items)}. First result item: {json.dumps(first_result_item, default=str)}")
                return {**default_error_result, "recommendation": {"text": f"Unexpected 'items' format from DataForSEO backlinks_detail for {self.domain}.", "priority": "High"}, "data": {"raw_task_response": api_result_task}}

            total_fetched_backlinks = len(backlinks_items)
            dofollow_count = 0
            nofollow_count = 0

            for bl in backlinks_items:
                if bl.get("dofollow", False): # Check based on actual field name
                    dofollow_count += 1
                else:
                    nofollow_count +=1 # Assume not dofollow is nofollow or other

            dofollow_percentage = (dofollow_count / total_fetched_backlinks * 100) if total_fetched_backlinks > 0 else 0

            pass_criteria = dofollow_percentage >= 60 # Example: at least 60% dofollow links in sample

            reco_parts = []
            priority = "Low"

            if total_fetched_backlinks == 0:
                reco_parts.append("No backlinks returned in the detailed sample. This might indicate a very new site or issues fetching data.")
                priority = "Medium"
            else:
                reco_parts.append(f"Analyzed a sample of {total_fetched_backlinks} backlinks.")
                if dofollow_percentage < 50:
                    reco_parts.append(f"Low proportion of dofollow links ({dofollow_percentage:.1f}%). Aim for more 'dofollow' links as they pass link equity.")
                    priority = "Medium"
                else:
                    reco_parts.append(f"Good proportion of dofollow links ({dofollow_percentage:.1f}%).")
            
            recommendation_text = " ".join(reco_parts) if reco_parts else "Backlink details analyzed. Monitor for quality."

            # Scoring
            if total_fetched_backlinks > 0:
                score += (dofollow_percentage / 100) * (max_score * 0.7)
            if total_fetched_backlinks > 20 : # Points for having a decent sample size
                score += max_score * 0.3
            score = min(max_score, round(score))
            
            # Top backlinks are essentially the backlinks_items if ordered by rank
            top_backlinks_sample = backlinks_items[:20] # Show top 20 from the fetched sample

            result = {
                "pass": pass_criteria,
                "total_fetched_for_detail": total_fetched_backlinks,
                "dofollow_count": dofollow_count,
                "nofollow_count": nofollow_count,
                "dofollow_percentage": round(dofollow_percentage, 2),
                "top_backlinks_sample": top_backlinks_sample,
                "data": {"items": backlinks_items}, # Store raw items
                "description": "Provides details on individual backlinks, including their 'dofollow' status. 'Dofollow' links pass SEO value, while 'nofollow' links typically do not.",
                "importance": "A healthy ratio of 'dofollow' links is vital for SEO. Top backlinks indicate the most powerful links pointing to your site.",
                "recommendation": {"text": recommendation_text, "priority": priority},
                "blog": "",
                "score": score
            }
            self._save_to_redis('dataforseo_backlinks_detail', result)
            return result

        except Exception as e:
            logger.error(f"Error in analyze_dataforseo_backlinks_detail for {self.domain}: {e}", exc_info=True)
            return default_error_result

    async def analyze_dataforseo_top_pages(self):
        max_score = self.MAX_SCORES.get("dataforseo_top_pages", 15)
        score = 0
        default_error_result = {
            "pass": False, "description": "Identifies the pages on your domain with the most backlinks.",
            "importance": "Knowing your top-linked pages helps understand which content attracts links and where link equity is concentrated.",
            "recommendation": {"text": "Error fetching top pages data from DataForSEO. Check logs.", "priority": "High"},
            "blog": "", "score": 0, "data": {}
        }

        if not self.dataforseo_login or not self.dataforseo_password:
            return {**default_error_result, "recommendation": {"text": "DataForSEO credentials not configured.", "priority": "High"}}

        try:
            # Use DataForSEO client SDK
            api_instance = self._get_dataforseo_client()
            request_info = BacklinksDomainPagesLiveRequestInfo(
                target=self.domain,
                limit=20,
                order_by=["backlinks,desc"]
            )
            
            # Execute the API call in a thread pool to avoid blocking
            api_result_task = await asyncio.to_thread(
                api_instance.domain_pages_live,
                backlinks_domain_pages_live_request_info=request_info
            )

            if not api_result_task:
                logger.error(f"DataForSEO top_pages request failed for {self.domain} (task error or no API response).")
                return default_error_result

            task_results_list = api_result_task.get("result")
            if task_results_list is None:
                logger.warning(f"DataForSEO top_pages for {self.domain} (Task ID: {api_result_task.get('id')}) returned no 'result' key (it was null/None). Assuming no data. Task data: {json.dumps(api_result_task, default=str)}")
                return {**default_error_result, "recommendation": {"text": f"No top pages data structure found by DataForSEO for {self.domain} (result was null).", "priority": "Medium"}, "data": {"raw_task_response": api_result_task}}

            if not isinstance(task_results_list, list):
                logger.error(f"DataForSEO top_pages for {self.domain} (Task ID: {api_result_task.get('id')}) 'result' field was not a list. Type: {type(task_results_list)}. Task data: {json.dumps(api_result_task, default=str)}")
                return {**default_error_result, "recommendation": {"text": "Unexpected data format from DataForSEO top_pages (result not a list).", "priority": "High"}}

            if not task_results_list: # Empty list
                logger.info(f"DataForSEO top_pages for {self.domain} (Task ID: {api_result_task.get('id')}) returned an empty 'result' list. Assuming no data items for this target.")
                return {**default_error_result, "top_pages_count": 0, "top_pages_sample": [], "recommendation": {"text": f"No top pages found by DataForSEO for {self.domain} (result list was empty).", "priority": "Medium"}, "data": {"raw_task_response": api_result_task}}

            first_result_item = task_results_list[0]
            if not isinstance(first_result_item, dict):
                logger.error(f"DataForSEO top_pages for {self.domain} (Task ID: {api_result_task.get('id')}) 'result[0]' is not a dictionary. Type: {type(first_result_item)}. Task data: {json.dumps(api_result_task, default=str)}")
                return {**default_error_result, "recommendation": {"text": "Unexpected data format for top_pages item container.", "priority": "High"}}

            top_pages_items = first_result_item.get("items")

            if top_pages_items is None:
                logger.warning(f"DataForSEO top_pages for {self.domain} (Task ID: {api_result_task.get('id')}) had a result structure, but no 'items' key or 'items' is null. First result item: {json.dumps(first_result_item, default=str)}. Assuming no top pages.")
                top_pages_items = []
            
            if not isinstance(top_pages_items, list):
                logger.error(f"DataForSEO top_pages for {self.domain} (Task ID: {api_result_task.get('id')}) 'items' is not a list: {type(top_pages_items)}. First result item: {json.dumps(first_result_item, default=str)}")
                return {**default_error_result, "recommendation": {"text": f"Unexpected 'items' format from DataForSEO top_pages for {self.domain}.", "priority": "High"}, "data": {"raw_task_response": api_result_task}}

            pass_criteria = len(top_pages_items) > 0

            reco_parts = []
            priority = "Low"

            if not top_pages_items:
                reco_parts.append(f"No specific top pages by backlinks were identified for {self.domain}. Ensure your important pages are attracting links.")
                priority = "Medium"
            else:
                reco_parts.append(f"Found {len(top_pages_items)} top pages by backlink count. These pages are your most linked assets.")
                if len(top_pages_items) < 5:
                     reco_parts.append("Consider strategies to build links to more key pages on your site.")
                     if priority != "Medium" : priority = "Medium"


            recommendation_text = " ".join(reco_parts)

            if len(top_pages_items) > 0: score += max_score * 0.5
            if len(top_pages_items) >=5 : score += max_score * 0.3
            if len(top_pages_items) >=10 : score += max_score * 0.2
            score = min(max_score, round(score))

            result = {
                "pass": pass_criteria,
                "top_pages_count": len(top_pages_items),
                "top_pages_sample": top_pages_items, # Already limited by API call
                "data": {"items": top_pages_items},
                "description": "Lists pages on your domain that have received the highest number of backlinks, indicating their perceived authority and popularity.",
                "importance": "Identifying these 'link magnets' helps you understand what content resonates and where your site's link equity is concentrated. You can leverage these pages or replicate their success.",
                "recommendation": {"text": recommendation_text, "priority": priority},
                "blog": "",
                "score": score
            }
            self._save_to_redis('dataforseo_top_pages', result)
            return result

        except Exception as e:
            logger.error(f"Error in analyze_dataforseo_top_pages for {self.domain}: {e}", exc_info=True)
            return default_error_result

    async def analyze_dataforseo_anchors(self):
        max_score = self.MAX_SCORES.get("dataforseo_anchors", 10)
        score = 0
        default_error_result = {
            "pass": False, "description": "Analyzes the most common anchor texts used in backlinks pointing to your domain.",
            "importance": "Anchor text diversity is important for a natural link profile. Over-optimized anchors can be a spam signal.",
            "recommendation": {"text": "Error fetching anchor text data from DataForSEO. Check logs.", "priority": "High"},
            "blog": "", "score": 0, "data": {}
        }

        if not self.dataforseo_login or not self.dataforseo_password:
            return {**default_error_result, "recommendation": {"text": "DataForSEO credentials not configured.", "priority": "High"}}
            
        try:
            # Use DataForSEO client SDK
            api_instance = self._get_dataforseo_client()
            request_info = BacklinksAnchorsLiveRequestInfo(
                target=self.domain,
                limit=50,
                order_by=["referring_domains,desc"]
            )
            
            # Execute the API call in a thread pool to avoid blocking
            api_result_task = await asyncio.to_thread(
                api_instance.anchors_live,
                backlinks_anchors_live_request_info=request_info
            )
            
            if not api_result_task:
                logger.error(f"DataForSEO anchors request failed for {self.domain} (task error or no API response).")
                return default_error_result

            task_results_list = api_result_task.get("result")
            if task_results_list is None:
                logger.warning(f"DataForSEO anchors for {self.domain} (Task ID: {api_result_task.get('id')}) returned no 'result' key (it was null/None). Assuming no data. Task data: {json.dumps(api_result_task, default=str)}")
                return {**default_error_result, "recommendation": {"text": f"No anchor text data structure found by DataForSEO for {self.domain} (result was null).", "priority": "Medium"}, "data": {"raw_task_response": api_result_task}}

            if not isinstance(task_results_list, list):
                logger.error(f"DataForSEO anchors for {self.domain} (Task ID: {api_result_task.get('id')}) 'result' field was not a list. Type: {type(task_results_list)}. Task data: {json.dumps(api_result_task, default=str)}")
                return {**default_error_result, "recommendation": {"text": "Unexpected data format from DataForSEO anchors (result not a list).", "priority": "High"}}

            if not task_results_list: # Empty list
                logger.info(f"DataForSEO anchors for {self.domain} (Task ID: {api_result_task.get('id')}) returned an empty 'result' list. Assuming no data items for this target.")
                return {**default_error_result, "unique_anchors_in_sample":0, "top_anchors_sample":[], "recommendation": {"text": f"No anchor text data found by DataForSEO for {self.domain} (result list was empty).", "priority": "Medium"}, "data": {"raw_task_response": api_result_task}}

            first_result_item = task_results_list[0]
            if not isinstance(first_result_item, dict):
                logger.error(f"DataForSEO anchors for {self.domain} (Task ID: {api_result_task.get('id')}) 'result[0]' is not a dictionary. Type: {type(first_result_item)}. Task data: {json.dumps(api_result_task, default=str)}")
                return {**default_error_result, "recommendation": {"text": "Unexpected data format for anchors item container.", "priority": "High"}}

            anchor_items = first_result_item.get("items")

            if anchor_items is None:
                logger.warning(f"DataForSEO anchors for {self.domain} (Task ID: {api_result_task.get('id')}) had a result structure, but no 'items' key or 'items' is null. First result item: {json.dumps(first_result_item, default=str)}. Assuming no anchors.")
                anchor_items = []

            if not isinstance(anchor_items, list):
                logger.error(f"DataForSEO anchors for {self.domain} (Task ID: {api_result_task.get('id')}) 'items' is not a list: {type(anchor_items)}. First result item: {json.dumps(first_result_item, default=str)}")
                return {**default_error_result, "recommendation": {"text": f"Unexpected 'items' format from DataForSEO anchors for {self.domain}.", "priority": "High"}, "data": {"raw_task_response": api_result_task}}
            
            # Analyze diversity - e.g. count unique anchors vs total backlinks/domains for those anchors
            unique_anchors_count = len(anchor_items)
            total_referring_domains_for_top_anchors = sum(a.get("referring_domains", 0) for a in anchor_items)
            
            # Basic check for over-optimization: if one anchor dominates heavily
            is_diverse = True
            if anchor_items:
                top_anchor_percentage = (anchor_items[0].get("referring_domains", 0) / total_referring_domains_for_top_anchors * 100) if total_referring_domains_for_top_anchors else 0
                if top_anchor_percentage > 50: # Example threshold
                    is_diverse = False

            pass_criteria = unique_anchors_count > 5 and is_diverse

            reco_parts = []
            priority = "Low"

            if not anchor_items:
                reco_parts.append(f"No anchor text data found for {self.domain}. This is unusual if backlinks exist.")
                priority = "Medium"
            else:
                reco_parts.append(f"Found {unique_anchors_count} unique anchor texts in the top sample.")
                if not is_diverse and anchor_items:
                    reco_parts.append(f"The top anchor '{anchor_items[0].get('anchor', '')}' accounts for a large percentage ({top_anchor_percentage:.1f}%) of links. Aim for more anchor text diversity to appear natural.")
                    priority = "Medium"
                else:
                    reco_parts.append("Anchor text distribution appears reasonably diverse. Continue to encourage varied, natural anchor text.")

            recommendation_text = " ".join(reco_parts)

            if unique_anchors_count > 5: score += max_score * 0.4
            if unique_anchors_count > 10: score += max_score * 0.3
            if is_diverse: score += max_score * 0.3
            score = min(max_score, round(score))

            result = {
                "pass": pass_criteria,
                "unique_anchors_in_sample": unique_anchors_count,
                "top_anchors_sample": anchor_items[:20], # Show top 20 from the fetched sample
                "is_diverse_sample": is_diverse,
                "data": {"items": anchor_items},
                "description": "Shows the most frequently used anchor texts in links pointing to your site. Anchor text helps search engines understand the context of the linked page.",
                "importance": "A natural and diverse anchor text profile is crucial. Over-optimization with exact-match keywords can be penalized. Aim for a mix of branded, naked URL, and topical anchors.",
                "recommendation": {"text": recommendation_text, "priority": priority},
                "blog": "",
                "score": score
            }
            self._save_to_redis('dataforseo_anchors', result)
            return result

        except Exception as e:
            logger.error(f"Error in analyze_dataforseo_anchors for {self.domain}: {e}", exc_info=True)
            return default_error_result

    async def analyze_dataforseo_referring_domains(self):
        max_score = self.MAX_SCORES.get("dataforseo_referring_domains", 10)
        score = 0
        default_error_result = {
            "pass": False, "description": "Analysis of referring domains by TLD and geography.",
            "importance": "Understanding the TLDs (e.g., .com, .org) and geographic origin of your backlinks provides insights into your link profile's diversity and relevance.",
            "recommendation": {"text": "Error fetching referring domains data from DataForSEO. Check logs.", "priority": "High"},
            "blog": "", "score": 0, "data": {}
        }

        if not self.dataforseo_login or not self.dataforseo_password:
            return {**default_error_result, "recommendation": {"text": "DataForSEO credentials not configured.", "priority": "High"}}

        # This section is for fetching TLD/Country distribution, relying on cached summary data.
        # The main try-except for the whole analyze_dataforseo_referring_domains function handles broader API call issues.
        # This inner try-except is specifically for cache access.
        logger.info(f"Attempting to use cached summary data for TLD/Country distribution for {self.domain}.")
        summary_cache_key = "dataforseo_summary" # Key used in _save_to_redis for summary
        cached_summary_data_for_tlds = None
        
        if self.redis_available and self.task_id and self.redis_client:
            try:
                raw_cached = self.redis_client.hget(self.task_id, summary_cache_key)
                if raw_cached:
                    # Ensure raw_cached is a string for JSON parsing
                    if isinstance(raw_cached, bytes):
                        raw_cached = raw_cached.decode('utf-8')
                    
                    if isinstance(raw_cached, str):
                        cached_summary_full_result = json.loads(raw_cached)
                    cached_summary_data_for_tlds = cached_summary_full_result.get("data") 
                    if cached_summary_data_for_tlds:
                        logger.info(f"Successfully retrieved cached 'data' from summary for TLD/Country analysis of {self.domain}.")
                    else:
                        logger.warning(f"Cached summary for {self.domain} found, but no 'data' field within it containing raw summary_data.")
                else:
                    logger.info(f"No cached summary data found in Redis for {self.domain} under task_id {self.task_id} and key {summary_cache_key} for TLD/Country analysis.")
            except Exception as e: # Catch potential errors during cache access/parsing
                logger.error(f"Error accessing or parsing cached summary for TLD/Country analysis of {self.domain}: {e}")
                cached_summary_data_for_tlds = None # Ensure it's None if cache access failed
        else:
            logger.info(f"Redis not available or no task_id, cannot fetch cached summary for TLD/Country analysis of {self.domain}.")

        top_tlds = {}
        top_countries = {}

        if cached_summary_data_for_tlds and isinstance(cached_summary_data_for_tlds, dict):
            top_tlds = cached_summary_data_for_tlds.get("referring_links_tld_count", {})
            top_countries = cached_summary_data_for_tlds.get("referring_links_country_count", {})
            if top_tlds or top_countries:
                logger.info(f"Using TLD/Country data from cached summary for {self.domain}. TLDs: {len(top_tlds)}, Countries: {len(top_countries)}")
            else:
                logger.info(f"Cached summary data for {self.domain} did not contain 'referring_links_tld_count' or 'referring_links_country_count'.")
        else:
            logger.warning(f"No usable cached summary data (or it wasn't a dict) for TLD/Country analysis of {self.domain}. Distribution will be empty.")
            
        pass_criteria = len(top_tlds) > 1 and len(top_countries) > 0 

        reco_parts = []
        priority = "Low"

        if not top_tlds and not top_countries:
            reco_parts.append(f"Could not determine TLD or country distribution for referring domains of {self.domain}. This data helps assess link profile relevance.")
            priority = "Medium"
        else:
            if top_tlds:
                reco_parts.append(f"Top TLDs linking to you include: {', '.join(list(top_tlds.keys())[:5])}.")
            if top_countries:
                 reco_parts.append(f"Top countries linking to you include: {', '.join(list(top_countries.keys())[:5])}.")
            reco_parts.append("A diverse and geographically relevant link profile is beneficial.")

        recommendation_text = " ".join(reco_parts)

        if len(top_tlds) > 1: score += max_score * 0.25
        if len(top_tlds) > 3: score += max_score * 0.25
        if len(top_countries) > 0: score += max_score * 0.25
        if len(top_countries) > 2 and any(c_count > 5 for c_count in top_countries.values()): # e.g. at least 2 countries with >5 links
            score += max_score * 0.25
        score = min(max_score, round(score))
        
        # Sort TLDs and Countries for consistent output
        sorted_top_tlds = dict(sorted(top_tlds.items(), key=lambda item: item[1], reverse=True)[:10])
        sorted_top_countries = dict(sorted(top_countries.items(), key=lambda item: item[1], reverse=True)[:10])


        result = {
            "pass": pass_criteria,
            "top_tlds": sorted_top_tlds,
            "top_countries": sorted_top_countries,
            "data": {"tlds_from_summary": top_tlds, "countries_from_summary": top_countries}, # Indicate source
            "description": "Shows the distribution of Top-Level Domains (TLDs like .com, .org) and geographic origins of websites linking to your domain.",
            "importance": "Understanding TLD and country distribution helps assess the relevance and diversity of your backlink sources. Links from relevant TLDs and geographic regions can be more valuable.",
            "recommendation": {"text": recommendation_text, "priority": priority},
            "blog": "",
            "score": score
        }
        self._save_to_redis('dataforseo_referring_domains', result)
        return result
    
    async def analyze_on_page_links(self):
        max_score = self.MAX_SCORES.get("analyze_on_page_links", 10) # Adjusted score
        priority = "Low"
        if not self.soup:
            logger.warning("Soup object not available for on-page link analysis.")
            return {
                "pass": False,
                "total_links": 0,
                "external_links": 0,
                "nofollow_links": 0,
                "external_percentage": 0,
                "nofollow_percentage": 0,
                "description": "",
                "importance": "",
                "recommendation": {"text": "HTML content not available for analysis.", "priority": "Medium"},
                "blog": "",
                "score": 0
            }

        links = self.soup.find_all('a', href=True)
        total_links = len(links)
        external_links = 0
        nofollow_links = 0

        for link in links:
            # Ensure the link is a Tag object and supports the get attribute before accessing 'href'
            if not isinstance(link, Tag) or not hasattr(link, 'get'):
                continue

            href = link.get('href')
            if not href:
                continue
                
            # Ensure href is a string
            if not isinstance(href, str):
                if hasattr(href, '__str__'):
                    href = str(href)
                else:
                    continue
                    
            parsed_href = urlparse(href)

            # Ensure netloc is not empty and different from self.domain for external links
            if parsed_href.netloc and parsed_href.netloc != self.domain:
                external_links += 1
            
            # Check for mailto, tel, etc. and don't count them as typical "external" for this specific check's purpose
            # unless they are truly off-domain http/https links.
            # The current logic correctly identifies http/https external links based on netloc.

            rel_attr = link.get('rel', None)
            if rel_attr is None:
                rel_attr = []
            elif isinstance(rel_attr, str): # rel="nofollow" or rel="nofollow noopener"
                rel_attr = rel_attr.split()
            elif not isinstance(rel_attr, list):
                rel_attr = []
            if 'nofollow' in rel_attr:
                nofollow_links += 1

        external_percentage = round((external_links / total_links) * 100) if total_links > 0 else 0
        nofollow_percentage = round((nofollow_links / total_links) * 100) if total_links > 0 else 0

        reco_parts = []
        issues_found = False

        if total_links == 0:
            reco_parts.append("No links found on the page. Add internal links to relevant content and external links to authoritative sources where appropriate.")
            priority = "Medium"
            issues_found = True
        else:
            reco_parts.append(f"Found {total_links} total links.")
            if external_percentage > 70: # Adjusted threshold
                 reco_parts.append(f"High external link ratio ({external_percentage}%). Ensure external links are necessary, high-quality, and open in a new tab if appropriate. Prioritize internal linking where possible.")
                 priority = "Medium"
                 issues_found = True
            elif external_percentage > 40: # Moderate external links
                 reco_parts.append(f"External link ratio ({external_percentage}%) is moderate. Review to ensure they add value.")
                 if priority != "Medium": priority = "Low" # Keep medium if already set
            else:
                 reco_parts.append(f"External link ratio ({external_percentage}%) is reasonable.")

            if nofollow_percentage > 50: # Adjusted threshold
                 reco_parts.append(f"High nofollow link ratio ({nofollow_percentage}%). Use 'nofollow' judiciously, primarily for sponsored/paid links, user-generated content, or genuinely untrusted sources. Avoid nofollowing most internal links or valuable external editorial links.")
                 if priority != "Medium": priority = "Medium"
                 issues_found = True
            elif nofollow_percentage > 25:
                 reco_parts.append(f"Nofollow link usage ({nofollow_percentage}%) is noticeable. Double-check if all are appropriate.")
                 if priority == "Low": priority = "Low" # Keep low
            else:
                 reco_parts.append(f"Nofollow link usage ({nofollow_percentage}%) seems reasonable.")

        if not issues_found and total_links > 0 :
             reco_parts.append("On-page link structure appears balanced. Regularly review internal and external linking strategy.")
             priority = "Low"
        elif not issues_found and total_links == 0: # Handled by first if
            pass


        recommendation_text = " ".join(reco_parts)

        desc = (
             "Analyzes the distribution of internal vs. external links and the usage of the 'nofollow' attribute on this specific page. "
             "A balanced link structure aids navigation and distributes link equity effectively."
        )
        imp = (
             "Internal links help site navigation and spread link equity throughout your site. External links can provide value to users but too many can dilute PageRank or signal low quality if not curated. 'Nofollow' tells search engines not to pass equity, "
             "useful for ads or untrusted content, but shouldn't be overused on internal or valuable external editorial links."
        )

        # Scoring logic:
        score = max_score
        if total_links == 0:
            score = 0 # Heavily penalize no links
        else:
            if external_percentage > 70: # Higher penalty for too many externals
                 score -= max_score * 0.4
            elif external_percentage > 40:
                 score -= max_score * 0.2
            
            if nofollow_percentage > 50: # Higher penalty for too many nofollows
                 score -= max_score * 0.4
            elif nofollow_percentage > 25:
                 score -= max_score * 0.2
        
        score = max(0, round(score))


        result = {
            "pass": total_links > 0 and external_percentage <= 70 and nofollow_percentage <= 50, # Adjusted pass criteria
            "total_links": total_links,
            "external_links": external_links,
            "internal_links": total_links - external_links, # Added internal links
            "nofollow_links": nofollow_links,
            "external_percentage": external_percentage,
            "nofollow_percentage": nofollow_percentage,
            "description": desc,
            "importance": imp,
            "recommendation": {"text": recommendation_text, "priority": priority},
            "blog": "",
            "score": score
        }

        self._save_to_redis('on_page_links', result)
        return result
    
    async def analyze_friendly_links(self):
        max_score = self.MAX_SCORES.get("analyze_friendly_links", 5) # Adjusted score
        priority = "Low"
        if not self.soup:
            logger.warning("Soup object not available for friendly link analysis.")
            return {
                "pass": False,
                "friendly_percentage": 0,
                "unfriendly_links_sample": [],
                "description": "Analyzes the readability of link anchor text using the Flesch Reading Ease score.",
                "importance": "Clear and concise anchor text improves user experience and helps search engines understand the linked page's context.",
                "recommendation": {"text": "HTML content not available for analysis.", "priority": "Medium"},
                "blog": "",
                "score": 0
            }

        links = self.soup.find_all('a', href=True)
        if not links:
             return {
                "pass": True, 
                "friendly_percentage": 100, 
                "unfriendly_links_sample": [],
                "description": "Analyzes the readability of link anchor text using the Flesch Reading Ease score.",
                "importance": "Clear and concise anchor text improves user experience and helps search engines understand the linked page's context.",
                "recommendation": {"text": "No links found on the page to analyze for anchor text readability.", "priority": "Low"},
                "blog": "",
                "score": max_score # Full score if no links to analyze
            }
            
        friendly_link_count = 0
        analyzed_link_count = 0
        unfriendly_links_sample = []
        MAX_UNFRIENDLY_SAMPLE = 10
        
        for link in links:
            # Explicitly check if it's a Tag object
            if not isinstance(link, Tag):
                continue
                
            anchor_text = link.get_text(strip=True)
            href = link.get('href', '')
            
            # Ensure href is a string
            if not isinstance(href, str):
                if hasattr(href, '__str__'):
                    href = str(href)
                else:
                    href = ''
            
            # Skip image links (no text), mailto, tel, etc. for readability score
            if not anchor_text or link.find('img') or href.startswith(('mailto:', 'tel:', 'javascript:')):
                continue

            analyzed_link_count += 1
            try:
                # textstat might struggle with very short texts, consider minimum length
                if len(anchor_text.split()) < 3: # If less than 3 words, assume friendly or not scorable
                    friendly_link_count +=1
                    continue

                f_score = textstat.flesch_reading_ease(anchor_text) # type: ignore
                if f_score >= 50: # Adjusted Flesch score threshold for "friendly"
                    friendly_link_count += 1
                elif len(unfriendly_links_sample) < MAX_UNFRIENDLY_SAMPLE:
                    unfriendly_links_sample.append({"url": href, "text": anchor_text, "score": round(f_score,1)})
            except Exception as e: # Catch any error from textstat
                logger.debug(f"Could not calculate Flesch score for anchor text '{anchor_text}': {e}")
                # Decide if unscoreable links count as friendly or not. Here, assume neutral/skip.
                pass 

        if analyzed_link_count == 0: # All links were images, mailto, etc.
             friendly_percentage = 100 # Or 0 depending on interpretation. 100 means "no issues found".
             recommendation_text = "No links with analyzeable anchor text found. Ensure text links use clear language."
             priority = "Low"
        else:
            friendly_percentage = round((friendly_link_count / analyzed_link_count) * 100)
            if friendly_percentage >= 75: # Adjusted threshold
                 recommendation_text = f"{friendly_percentage}% of link anchor texts have good readability. Keep anchor text clear and descriptive."
                 priority = "Low"
            elif friendly_percentage >= 50:
                 recommendation_text = f"{friendly_percentage}% of link anchor texts have standard readability. Review the less readable anchors (sample below) and consider simplifying for clarity."
                 priority = "Low" # Still low, but a note
            else:
                 recommendation_text = f"{friendly_percentage}% of link anchor texts may be difficult to read. Simplify complex anchor text (sample below) for better user experience and SEO."
                 priority = "Medium"

        desc = "Analyzes the readability of link anchor text on this page using the Flesch Reading Ease score. Higher scores indicate easier readability."
        imp = "Clear, concise, and descriptive anchor text improves user experience and helps search engines understand the linked page's context, contributing to better SEO."
        
        score = round(max_score * (friendly_percentage / 100))

        result = {
            "pass": friendly_percentage >= 50 if analyzed_link_count > 0 else True, # Adjusted pass
            "friendly_percentage": friendly_percentage,
            "analyzed_text_links": analyzed_link_count,
            "unfriendly_links_sample": unfriendly_links_sample,
            "description": desc,
            "importance": imp,
            "recommendation": {"text": recommendation_text, "priority": priority},
            "blog": "",
            "score": score
        }

        self._save_to_redis('friendly_links', result)
        return result

    async def _check_link_status(self, client, url):
        
        try:
            # Use a common user-agent to avoid being blocked
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
            response = await client.head(url, timeout=15, headers=headers) # Reduced timeout slightly
            
            # If HEAD is disallowed (405) or other client error that might allow GET, try GET
            if response.status_code == 405 or (400 <= response.status_code < 500 and response.status_code not in [404, 403, 410]): 
                 response = await client.get(url, timeout=15, headers=headers)
                 
            if response.status_code >= 400:
                return url, "broken", response.status_code
            else:
                return url, "working", response.status_code
        except httpx.TimeoutException:
            logger.warning(f"Timeout checking link: {url}")
            return url, "timeout", None
        except httpx.TooManyRedirects:
            logger.warning(f"Too many redirects for link: {url}")
            return url, "redirect_loop", None # Specific status for redirect loops
        except httpx.ConnectError:
            logger.warning(f"Connection error for link: {url}")
            return url, "connection_error", None
        except httpx.RemoteProtocolError:
            logger.warning(f"Remote protocol error for link: {url}")
            return url, "protocol_error", None
        except httpx.RequestError as e:
            # General request error if not caught by more specific ones
            logger.warning(f"Request error checking link {url}: {type(e).__name__} - {e}")
            return url, "error", type(e).__name__
        except Exception as e: # Catch-all for other unexpected errors
            logger.error(f"Unexpected error checking link {url}: {type(e).__name__} - {e}")
            return url, "error", type(e).__name__

    async def analyze_broken_links(self):
        max_score = self.MAX_SCORES.get("analyze_broken_links", 5) # Adjusted score
        priority = "Low"
        # proxy_url_formatted = None # Proxies not used here for individual link checks by default
        if not self.soup:
            logger.warning("Soup object not available for broken link analysis.")
            return {
                "pass": False,
                "total_checked": 0,
                "broken_count": 0,
                "broken_percentage": 0,
                "sample_broken_links": [],
                "description": "Checks for broken links (4xx/5xx errors, timeouts) on the page.",
                "importance": "Broken links negatively impact user experience and SEO by creating dead ends for users and crawlers.",
                "recommendation": {"text": "HTML content not available for analysis.", "priority": "Medium"},
                "blog": "",
                "score": 0
            }

        links = self.soup.find_all('a', href=True)
        if not links:
             return {
                "pass": True, 
                "total_checked": 0,
                "broken_count": 0,
                "broken_percentage": 0,
                "sample_broken_links": [],
                "description": "Checks for broken links (4xx/5xx errors, timeouts) on the page.",
                "importance": "Broken links negatively impact user experience and SEO by creating dead ends for users and crawlers.",
                "recommendation": {"text": "No links found on the page to check.", "priority": "Low"},
                "blog": "",
                "score": max_score # Full score if no links
            }

        tasks = []
        urls_to_check = set() # Using a set to avoid checking the same URL multiple times
        
        MAX_LINKS_TO_CHECK = 75 # Limit the number of links to check to avoid long processing times
        
        for link in links:
            # Type check for BeautifulSoup Tag elements
            if not isinstance(link, Tag): # Ensure it's a Tag object
                continue
                
            href = link.get('href')
            if not href:
                continue
                
            # Ensure href is a string
            if not isinstance(href, str):
                if hasattr(href, '__str__'):
                    href = str(href)
                else:
                    continue
                    
            if href.startswith(('#', 'javascript:')): # Skip fragment identifiers and javascript pseudo-links
                continue
            
            try:
                # Resolve relative URLs to absolute
                absolute_url = urljoin(self.url, href.strip())
                parsed_link_url = urlparse(absolute_url)

                # Only check http and https links, and avoid re-checking
                if parsed_link_url.scheme in ['http', 'https'] and absolute_url not in urls_to_check:
                    if len(urls_to_check) < MAX_LINKS_TO_CHECK:
                        urls_to_check.add(absolute_url)
                    else:
                        logger.info(f"Reached max links to check ({MAX_LINKS_TO_CHECK}). Skipping further links for broken link analysis.")
                        break 
            except Exception as e:
                logger.warning(f"Could not parse or resolve URL for broken link check: {href} - {e}")
        
        if not urls_to_check:
             return {
                "pass": True, 
                "total_checked": 0,
                "broken_count": 0,
                "broken_percentage": 0,
                "sample_broken_links": [],
                "description": "Checks for broken links (4xx/5xx errors, timeouts) on the page.",
                "importance": "Broken links negatively impact user experience and SEO by creating dead ends for users and crawlers.",
                "recommendation": {"text": "No checkable (HTTP/HTTPS) links found on the page, or all were skipped (e.g. mailto, tel).", "priority": "Low"},
                "blog": "",
                "score": max_score # Full score if no checkable links
            }

        # Proxies generally not recommended for bulk link checking due to cost and potential for proxy issues
        # unless specifically dealing with geo-restrictions for the checks themselves.
        # For now, direct requests.

        async with httpx.AsyncClient(http2=True, follow_redirects=True, verify=False) as client: # verify=False for SSL issues, use with caution
            for url_to_chk in urls_to_check:
                 tasks.append(self._check_link_status(client, url_to_chk))
            
            link_check_results = await asyncio.gather(*tasks, return_exceptions=True)

        broken_links_list = []
        total_checked = 0
        broken_count = 0
        MAX_BROKEN_SAMPLE = 20

        for res_item in link_check_results:
            if isinstance(res_item, Exception): # Should not happen if _check_link_status catches its own
                logger.error(f"Unhandled exception during link check gathering: {res_item}")
                # Count as broken if an exception occurred at this stage, assuming the URL was valid
                broken_count +=1
                total_checked +=1 # Still counts as checked
                if len(broken_links_list) < MAX_BROKEN_SAMPLE:
                     broken_links_list.append({"url": "Unknown due to gather error", "status": "gather_exception", "details": str(res_item)})
                continue 
            
            # Ensure res_item is a tuple/list with 3 elements
            if not isinstance(res_item, (tuple, list)) or len(res_item) != 3:
                logger.warning(f"Unexpected result format for link check: {res_item}")
                broken_count += 1
                total_checked += 1
                if len(broken_links_list) < MAX_BROKEN_SAMPLE:
                     broken_links_list.append({"url": "Unknown format", "status": "format_error", "details": str(res_item)})
                continue
                
            checked_url, status, status_code_or_error_msg = res_item
            total_checked += 1
            if status not in ['working']: # Any non-working status is "broken" for this purpose
                broken_count += 1
                if len(broken_links_list) < MAX_BROKEN_SAMPLE:
                    broken_links_list.append({"url": checked_url, "status": status, "details": status_code_or_error_msg})

        broken_percentage = round((broken_count / total_checked) * 100) if total_checked > 0 else 0

        if broken_count == 0:
            recommendation_text = f"Great! No broken links found among the {total_checked} links checked on this page (up to {MAX_LINKS_TO_CHECK} were sampled)."
            priority = "Low"
        else:
            reco_parts = [
                f"Found {broken_count} broken/problematic link(s) ({broken_percentage}%) out of {total_checked} checked (sample below).",
                "Broken links hurt user experience and SEO by leading to dead ends or errors.",
                "Review the list and fix them by updating URLs or removing the links."
            ]
            if len(urls_to_check) == MAX_LINKS_TO_CHECK and total_checked == MAX_LINKS_TO_CHECK:
                reco_parts.append(f"Note: Analysis was limited to the first {MAX_LINKS_TO_CHECK} checkable links found on the page.")
            recommendation_text = " ".join(reco_parts)
            priority = "High" if broken_percentage > 10 else "Medium" # Adjust priority based on severity

        desc = f"Checks for broken or problematic links (e.g., 404s, timeouts, server errors) on this specific page, sampling up to {MAX_LINKS_TO_CHECK} links."
        imp = "Broken links severely damage user experience and can harm your SEO by creating dead ends for users and search engine crawlers, potentially leading to lost link equity and lower rankings."

        # Scoring: Full score if no broken links. Penalty based on percentage.
        score = max_score
        if total_checked > 0:
            score = round(max_score * (1 - (broken_count / total_checked)))
        score = max(0, score) # Ensure score is not negative

        result_data = {
            "pass": broken_count == 0,
            "total_links_on_page": len(links), # Total links found by BS4 initially
            "total_http_links_identified_for_check": len(urls_to_check), # How many were identified as checkable
            "total_checked": total_checked, # How many were actually processed by _check_link_status
            "broken_count": broken_count,
            "broken_percentage": broken_percentage,
            "sample_broken_links": broken_links_list, 
            "description": desc,
            "importance": imp,
            "recommendation": {"text": recommendation_text, "priority": priority},
            "blog": "",
            "score": score
        }

        self._save_to_redis('broken_links', result_data)
        return result_data

    async def analyze_all(self):
        if not self.soup and not self.html: # Ensure HTML is fetched for on-page analyses
            logger.info("HTML not available for on-page analysis, fetching now...")
            html_success = await self.fetch_content()
            if not html_success:
                # Return error if HTML fetch fails, as on-page tools depend on it.
                # DataForSEO tools could still run if self.domain is valid.
                # For a unified report, it's better to have HTML.
                return {
                    "error": "Failed to fetch page content, cannot perform full link analysis.",
                    "url": self.url,
                    "total_score": {"score": 0, "grade": "F"},
                    "overall_title": "Analysis Incomplete",
                    "overall_description": "Could not fetch the page content, so on-page link analysis could not be performed. Backlink data from DataForSEO might be missing or incomplete as well if the primary target URL was invalid."
                }
        
        results = {}
        # DataForSEO analyses
        results["dataforseo_summary"] = await self.analyze_dataforseo_summary()
        results["dataforseo_backlinks_detail"] = await self.analyze_dataforseo_backlinks_detail()
        results["dataforseo_top_pages"] = await self.analyze_dataforseo_top_pages()
        results["dataforseo_anchors"] = await self.analyze_dataforseo_anchors()
        results["dataforseo_referring_domains"] = await self.analyze_dataforseo_referring_domains()
        
        # On-page analyses (require self.soup to be populated)
        if self.soup:
            results["on_page_links"] = await self.analyze_on_page_links()
            results["friendly_links"] = await self.analyze_friendly_links()
            results["broken_links"] = await self.analyze_broken_links()
        else: # Fill with error/default if soup wasn't available
            default_onpage_error = {"score": 0, "recommendation": {"text": "HTML content not available.", "priority": "High"}, "pass": False}
            results["on_page_links"] = {**default_onpage_error, "description": "On-page link structure analysis."}
            results["friendly_links"] = {**default_onpage_error, "description": "Anchor text readability analysis."}
            results["broken_links"] = {**default_onpage_error, "description": "Broken links check."}


        total_score = 0
        # Ensure MAX_SCORES reflects the new structure for total_possible calculation
        total_possible = sum(self.MAX_SCORES.values())

        for category_key, category_data in results.items():
            if category_key in ['total_score', 'overall_title', 'overall_description', 'error', 'url']: # Skip meta keys
                continue
            if isinstance(category_data, dict) and 'score' in category_data:
                total_score += category_data.get('score', 0)
            else:
                logger.warning(f"Category {category_key} missing or has no score: {category_data}")


        percentage = round((total_score / total_possible) * 100) if total_possible > 0 else 0

        # Grade calculation (can be adjusted)
        if percentage >= 95: grade = 'A+'
        elif percentage >= 90: grade = 'A'
        elif percentage >= 85: grade = 'A-'
        elif percentage >= 80: grade = 'B+'
        elif percentage >= 75: grade = 'B'
        elif percentage >= 70: grade = 'B-'
        elif percentage >= 65: grade = 'C+'
        elif percentage >= 60: grade = 'C'
        elif percentage >= 55: grade = 'C-'
        elif percentage >= 50: grade = 'D'
        else: grade = 'F'

        results['total_score'] = {
            'score': total_score,
            'grade': grade,
            'percentage': percentage,
            'total_possible_score': total_possible
        }
        
        if grade in ['A+', 'A', 'A-']:
            overall_title = "Excellent Link Profile & On-Page Links"
            overall_description = "Your website demonstrates excellent overall link health, strong backlink metrics from DataForSEO, and well-structured on-page links. Only minor refinements might be beneficial."
        elif grade in ['B+', 'B', 'B-']:
            overall_title = "Good Link Profile & On-Page Links"
            overall_description = "Your website has a good link profile according to DataForSEO and on-page analysis, but there are areas for improvement to achieve optimal SEO performance."
        elif grade in ['C+', 'C', 'C-']:
            overall_title = "Average Link Profile & On-Page Links"
            overall_description = "Your website's link profile and on-page link structure are average. Several areas need attention to improve authority, user experience, and SEO rankings."
        elif grade == 'D':
            overall_title = "Link Profile & On-Page Links Need Significant Improvement"
            overall_description = "Your website has notable issues in its backlink profile (via DataForSEO) and/or on-page link structure. Address the key recommendations to improve SEO and user trust."
        else: # F
            overall_title = "Poor Link Profile & On-Page Links"
            overall_description = "Your website shows critical deficiencies in its backlink profile and/or on-page linking. Immediate and comprehensive action is required to improve your SEO and user experience."

        results['overall_title'] = overall_title
        results['overall_description'] = overall_description
        
        # Save the entire consolidated result to Redis if needed
        if self.redis_available and self.task_id and self.redis_client:
            try:
                # Use a more specific key for the full result if individual parts are already saved
                full_result_key = f"links-analysis-full:{self.task_id}"
                # Convert complex objects to string if necessary, or ensure JSON serializability
                self.redis_client.set(full_result_key, json.dumps(results, default=str), ex=3600) # Cache for 1 hour
                logger.info(f"Cached full links analysis results in Redis with key {full_result_key}")
            except Exception as e:
                logger.error(f"Failed to cache full links analysis results for task {self.task_id}: {e}")

        return results

async def analyze_links(url, task_id=None, html=None):
    proxy_string_val = None
    if not html:
        proxy_string_val = getattr(settings, 'PROXY_STRING', None)
    
    analyzer = LinksAnalyzer(
        url=url,
        task_id=task_id,
        html=html,
        proxy_string=proxy_string_val
    )
    
    results = await analyzer.analyze_all()
    return results