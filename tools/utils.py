import re
from urllib.parse import urlparse, urlunsplit
import logging

logger = logging.getLogger(__name__)

def normalize_url(url):
    url = url.strip()
    
    if not url:
        return None
    
    if not re.match(r'^https?://', url):
        if url.startswith('www.'):
            url = 'https://' + url
        else:
            url = 'https://' + url
    
    parsed = urlparse(url)
    
    if not parsed.netloc:
        return None
    
    return urlunsplit((parsed.scheme, parsed.netloc, parsed.path, parsed.query, parsed.fragment))

def is_valid_url(url):
    normalized = normalize_url(url)
    return normalized is not None
