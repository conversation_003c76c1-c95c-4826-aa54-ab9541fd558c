from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, viewsets
from celery.result import AsyncResult
from .serializers import URLAnalyzeSerializer, TaskStatusSerializer
from .tasks import analyze_url, redis_client    
import logging
import redis
from django.conf import settings
from django.http import HttpResponse, Http404
from django.core.cache import cache
import base64
import re
import json
from django.urls import reverse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.permissions import IsAuthenticated
from .models import AnalysisRecord, AnalyzedURL
from .utils import normalize_url
import stripe

logger = logging.getLogger(__name__)

class ShareAnalysisView(APIView):
    
    def get(self, request, task_id):
        try:
            analysis_record = AnalysisRecord.objects.filter(celery_task_id=task_id).first()
            
            if not analysis_record:
                return Response({
                    'status': 'error',
                    'message': 'Analysis not found'
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response(analysis_record.results)
            
        except Exception as e:
            logger.error(f"ShareAnalysisView: Error retrieving analysis for task_id {task_id}: {str(e)}")
            return Response({
                'status': 'error',
                'message': 'Error retrieving analysis results'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@method_decorator(csrf_exempt, name='dispatch')
class AnalyzeURLView(APIView):
    def post(self, request):
        serializer = URLAnalyzeSerializer(data=request.data)
        if serializer.is_valid():
            url = serializer.validated_data['url']
            
            task = analyze_url.apply_async(args=[url])
            logger.info(f"Started task with ID: {task.id} for URL: {url}")
            
            response_data = {
                "task_id": task.id,
                "status": "pending",
                "message": f"Analysis started for URL: {url}",
            }
            
            return Response(response_data, status=status.HTTP_202_ACCEPTED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
class TaskStatusView(APIView):
    def get(self, request, task_id):
        logger.info(f"TaskStatusView: Received request for task_id={task_id}")
        
        uuid_pattern = re.compile(r'^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$', re.IGNORECASE)
        if not uuid_pattern.match(task_id):
            logger.warning(f"TaskStatusView: Invalid task_id format: {task_id}")
            return Response({
                'status': 'error',
                'error': 'Invalid task ID format'
            }, status=400)
        
        try:
            task_result = AsyncResult(task_id)
            
            try:
                exists = redis_client.exists(f'celery-task-meta-{task_id}')
                logger.debug(f"Redis key check for 'celery-task-meta-{task_id}': exists={exists}")
            except Exception as ex:
                logger.warning(f"Error checking Redis for task {task_id}: {ex}")
                exists = True
            
            if not exists and task_result.state == 'PENDING':
                return Response({
                    'status': 'pending',
                    'message': 'Task is queued but not yet processed'
                })
            
            logger.info(f"TaskStatusView: Task state={task_result.state} for ID={task_id}")
            
            if task_result.ready():
                if task_result.successful():
                    result = task_result.result
                    logger.debug(f"TaskStatusView: Main task result: {result}")
                    
                    if isinstance(result, dict) and (
                        'onpage_task_id' in result or 'chord_id' in result):
                        logger.info(f"TaskStatusView: Found main analysis task, checking component results")
                        
                        analysis_info = redis_client.hgetall(f"analysis:{task_id}")
                        if not analysis_info:
                            analysis_info = cache.get(f"analysis:{task_id}")
                        
                        if isinstance(analysis_info, dict) and all(
                            not isinstance(v, str) for v in analysis_info.values()):
                            analysis_info = {k.decode(): v.decode() for k, v in analysis_info.items()}
                        
                        logger.info(f"TaskStatusView: Analysis info: {analysis_info}")
                        
                        response_data = {}
                        
                        url = analysis_info.get("url")
                        desktop_screenshot_key = analysis_info.get("desktop_screenshot") 
                        logger.info(f"TaskStatusView: Using URL '{url}' for usability result lookup")
                        logger.info(f"TaskStatusView: Desktop screenshot key: {desktop_screenshot_key}") 

                        if desktop_screenshot_key:
                            try:
                                screenshot_url = request.build_absolute_uri(
                                    reverse('tools:screenshot', kwargs={'screenshot_id': desktop_screenshot_key})
                                )
                                response_data['desktop_screenshot_url'] = screenshot_url
                                logger.info(f"TaskStatusView: Constructed screenshot URL: {screenshot_url}")
                            except Exception as e:
                                logger.error(f"TaskStatusView: Error constructing screenshot URL: {e}")
                                response_data['desktop_screenshot_url'] = None 
                        else:
                             response_data['desktop_screenshot_url'] = None 

                        onpage_results = None
                        try:
                            onpage_cache_key = f"onpage-result:{task_id}"
                            onpage_data = redis_client.get(onpage_cache_key)
                            if onpage_data:
                                onpage_results = json.loads(onpage_data)
                                logger.info(f"TaskStatusView: Found onpage results in Redis cache")
                                response_data['onpage_analysis'] = onpage_results
                            else:
                                response_data['onpage_analysis'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting onpage results: {e}")
                            response_data['onpage_analysis'] = 'in_progress'
                            
                        usability_results = None
                        try:
                            if url:
                                usability_cache_key = f"usability-result:{url}"
                                usability_data = redis_client.get(usability_cache_key)
                                if usability_data:
                                    usability_results = json.loads(usability_data)
                                    logger.info(f"TaskStatusView: Found usability results in Redis cache")
                                    response_data['usability_analysis'] = usability_results
                                else:
                                    response_data['usability_analysis'] = 'in_progress'
                            else:
                                response_data['usability_analysis'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting usability results: {e}")
                            response_data['usability_analysis'] = 'in_progress'
                            
                        localseo_results = None
                        try:
                            localseo_cache_key = f"localseo-result:{task_id}"
                            localseo_data = redis_client.get(localseo_cache_key)
                            if localseo_data:
                                localseo_results = json.loads(localseo_data)
                                logger.info(f"TaskStatusView: Found localseo results in Redis cache")
                                response_data['localseo_analysis'] = localseo_results
                            else:
                                response_data['localseo_analysis'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting localseo results: {e}")
                            response_data['localseo_analysis'] = 'in_progress'
                            
                        techseo_results = None
                        try:
                            techseo_cache_key = f"techseo-result:{task_id}"
                            techseo_data = redis_client.get(techseo_cache_key)
                            if techseo_data:
                                techseo_results = json.loads(techseo_data)
                                logger.info(f"TaskStatusView: Found techseo results in Redis cache")
                                response_data['technology_review_analysis'] = techseo_results
                            else:
                                response_data['technology_review_analysis'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting techseo results: {e}")
                            response_data['technology_review_analysis'] = 'in_progress'
                            
                        social_results = None
                        try:
                            social_cache_key = f"social-result:{task_id}"
                            social_data = redis_client.get(social_cache_key)
                            if social_data:
                                social_results = json.loads(social_data)
                                logger.info(f"TaskStatusView: Found social media results in Redis cache")
                                response_data['social_analysis'] = social_results
                            else:
                                response_data['social_analysis'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting social media results: {e}")
                            response_data['social_analysis'] = 'in_progress'
                        
                        performance_results = None
                        try:
                            performance_cache_key = f"performance-result:{task_id}"
                            performance_data = redis_client.get(performance_cache_key)
                            if performance_data:
                                performance_results = json.loads(performance_data)
                                logger.info(f"TaskStatusView: Found performance results in Redis cache")
                                response_data['performance_analysis'] = performance_results
                            else:
                                response_data['performance_analysis'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting performance results: {e}")
                            response_data['performance_analysis'] = 'in_progress'
                            
                        links_results = None
                        try:
                            links_cache_key = f"links-result:{task_id}"
                            links_data = redis_client.get(links_cache_key)
                            if links_data:
                                links_results = json.loads(links_data)
                                logger.info(f"TaskStatusView: Found links analyzer results in Redis cache")
                                response_data['links_analysis'] = links_results
                            else:
                                response_data['links_analysis'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting links analyzer results: {e}")
                            response_data['links_analysis'] = 'in_progress'
                            
                        pagespeed_results = None
                        try:
                            pagespeed_cache_key = f"pagespeed-desktop-result:{task_id}"
                            pagespeed_data = redis_client.get(pagespeed_cache_key)
                            if pagespeed_data:
                                pagespeed_results = json.loads(pagespeed_data)
                                logger.info(f"TaskStatusView: Found PageSpeed Desktop results in Redis cache")
                                response_data['pagespeed_analysis'] = pagespeed_results
                            else:
                                response_data['pagespeed_analysis'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting PageSpeed Desktop results: {e}")
                            response_data['pagespeed_analysis'] = 'in_progress'
                            
                        pagespeed_mobile_results = None
                        try:
                            pagespeed_mobile_cache_key = f"pagespeed-mobile-result:{task_id}"
                            pagespeed_mobile_data = redis_client.get(pagespeed_mobile_cache_key)
                            if pagespeed_mobile_data:
                                pagespeed_mobile_results = json.loads(pagespeed_mobile_data)
                                logger.info(f"TaskStatusView: Found PageSpeed Mobile results in Redis cache")
                                response_data['pagespeed_mobile_analysis'] = pagespeed_mobile_results
                            else:
                                response_data['pagespeed_mobile_analysis'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting PageSpeed Mobile results: {e}")
                            response_data['pagespeed_mobile_analysis'] = 'in_progress'

                        child_pages_results = None
                        try:
                            child_cache_key = f"child-pages-result:{task_id}"
                            child_data = redis_client.get(child_cache_key)
                            if child_data:
                                parsed_data = json.loads(child_data)
                                child_pages_results = {
                                    "pages": parsed_data.get('child_pages', []),
                                    "total_count": parsed_data.get('total_count', 0)
                                }
                                logger.info(f"TaskStatusView: Found child pages in Redis cache with count {child_pages_results['total_count']}")
                                response_data['child_pages'] = child_pages_results
                            else:
                                response_data['child_pages'] = 'in_progress'
                        except Exception as e:
                            logger.error(f"TaskStatusView: Error getting child pages results: {e}")
                            response_data['child_pages'] = 'in_progress'

                        if (onpage_results and usability_results and localseo_results and 
                            techseo_results and social_results and performance_results and links_results and pagespeed_results and pagespeed_mobile_results and child_pages_results):
                            status = 'success'
                        elif (onpage_results or usability_results or localseo_results or 
                              techseo_results or social_results or performance_results or links_results or pagespeed_results or pagespeed_mobile_results or child_pages_results):
                            status = 'partial'
                        else:
                            status = 'in_progress'
                            
                        logger.info(f"TaskStatusView: Returning status={status} with " + 
                                   f"onpage={onpage_results is not None}, " +
                                   f"usability={usability_results is not None}, " +
                                   f"localseo={localseo_results is not None}, " +
                                   f"techseo={techseo_results is not None}, " +
                                   f"social={social_results is not None}, " +
                                   f"performance={performance_results is not None}, " +
                                   f"links={links_results is not None}, " +
                                   f"pagespeed_desktop={pagespeed_results is not None}, " +
                                   f"pagespeed_mobile={pagespeed_mobile_results is not None}, " +
                                   f"desktop_screenshot_url={response_data.get('desktop_screenshot_url')}")
                            
                        return Response({
                            'status': status,
                            'result': response_data
                        })
                    
                    logger.info(f"TaskStatusView: Regular task, returning result directly")
                    return Response({
                        'status': 'success',
                        'result': result
                    })
                else:
                    error_info = str(task_result.result)
                    logger.error(f"TaskStatusView: Task failed with error: {error_info}")
                    return Response({
                        'status': 'failed',
                        'error': error_info
                    }, status=500)
            else:
                return Response({
                    'status': 'in_progress',
                    'message': 'Task not yet complete'
                })
        except Exception as e:
            logger.exception(f"TaskStatusView: Exception occurred: {str(e)}")
            return Response({
                'task_id': task_id,
                'status': 'error',
                'error': str(e)
            }, status=500)

class ScreenshotView(APIView):
    def get(self, request, screenshot_id):
        try:
            screenshot_data_base64 = redis_client.get(screenshot_id)
            
            if screenshot_data_base64 is None:
                raise Http404("Screenshot not found.")

            screenshot_data_binary = base64.b64decode(screenshot_data_base64)
            
            return HttpResponse(screenshot_data_binary, content_type="image/png")
        
        except redis.exceptions.RedisError as e:
            logger.error(f"ScreenshotView: Redis error for screenshot_id {screenshot_id}: {e}")
            raise Http404("Error accessing screenshot data.")
        except Exception as e:
            logger.error(f"ScreenshotView: General error for screenshot_id {screenshot_id}: {e}")
            raise Http404("Error retrieving screenshot.")