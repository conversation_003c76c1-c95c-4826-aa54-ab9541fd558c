from celery import shared_task, states, chord, group
from celery.utils.log import get_task_logger
from .modules.getcontent import PageContentFetcher
from .modules.onpage import OnPageSEOAnalyzer
from .modules.usability import analyze_website_usability
from .modules.localseo import LocalSEOAnalyzer
from .modules.tech import TechnologyReviewAnalyzer
from .modules.social import SocialMediaAnalyzer
from .modules.pagespeeddesktop import PageSpeedDesktopAnalyzer
from .modules.performance import PerformanceAnalyzer  
from .modules.links import LinksAnalyzer  
from .modules.pagespeedmobile import PageSpeedmobileAnalyzer
from .utils import normalize_url
from .models import AnalyzedURL, AnalysisRecord
import os
import traceback
import asyncio
import redis
from django.conf import settings
import time
import json
from celery.signals import task_postrun, task_success
from celery.result import GroupResult, AsyncResult
from django.core.cache import cache
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import re
from django.utils import timezone

logger = get_task_logger(__name__)

try:
    redis_client = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        db=settings.REDIS_DB,
        password=os.getenv('REDIS_PASSWORD', None),
        decode_responses=False
    )
    redis_client.ping()
    logger.info("Redis connection successful in tasks.py")
except Exception as e:
    logger.error(f"Redis connection failed in tasks.py: {str(e)}")
    class DummyRedisClient:
        def ping(self): return True
        def exists(self, key): return False
        def set(self, key, value, ex=None): return True
        def get(self, key): return None
        def hset(self, name, key, value): return True
    redis_client = DummyRedisClient()

@task_success.connect
def task_success_handler(sender=None, result=None, **kwargs):
    task_id = sender.request.id
    task_name = sender.name
    logger.info(f"[SIGNAL] Task {task_name}[{task_id}] completed successfully")

    try:
        if result is not None:
            result_key = f"task-result:{task_id}"
            redis_client.set(result_key, json.dumps(result, default=str), ex=3600)
            logger.info(f"[SIGNAL] Stored result for {task_id} in Redis")
    except Exception as e:
        logger.error(f"[SIGNAL] Failed to store result in Redis: {e}")

@shared_task
def analyze_onpage_task(url, html, task_id):
    
    start_time = time.time()
    logger.info(f"[ONPAGE ANALYSIS] STARTING for URL: {url} | Task ID: {task_id}")

    try:
        logger.info(f"[ONPAGE ANALYSIS] Creating analyzer instance for URL: {url}")
        analyzer = OnPageSEOAnalyzer(html, url, task_id)
        logger.info(f"[ONPAGE ANALYSIS] Analyzer instance created successfully")
    except Exception as e:
        logger.error(f"[ONPAGE ANALYSIS] ERROR creating analyzer instance: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Failed to create analyzer: {str(e)}"}

    try:
        logger.info(f"[ONPAGE ANALYSIS] Starting run_all() method")
        result = analyzer.run_all()
        logger.info(f"[ONPAGE ANALYSIS] Successfully completed run_all() method")
    except Exception as e:
        logger.error(f"[ONPAGE ANALYSIS] ERROR during run_all(): {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Onpage analysis failed: {str(e)}"}

    duration = time.time() - start_time
    logger.info(f"[ONPAGE ANALYSIS] COMPLETED for URL: {url} | Task ID: {task_id} | Duration: {duration:.2f} seconds")
    logger.debug(f"[ONPAGE ANALYSIS] Results: {json.dumps(result, default=str)[:500]}...")

    cache_key = f"onpage-result:{task_id}"
    try:
        redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
        logger.info(f"[ONPAGE ANALYSIS] Cached results in Redis with key {cache_key}")
    except Exception as e:
        logger.error(f"[ONPAGE ANALYSIS] Failed to cache results: {e}")

    return result

@shared_task
def analyze_usability_task(url, task_id=None, html=None, device_screenshots=None, technical_data=None):
    start_time = time.time()
    logger.info(f"[USABILITY ANALYSIS] STARTING for URL: {url}")

    try:
        logger.info(f"[USABILITY ANALYSIS] Calling analyze_website_usability")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(
                analyze_website_usability(
                    url=url,
                    task_id=task_id,
                    html=html,
                    device_screenshots=device_screenshots,
                    technical_data=technical_data
                )
            )
        finally:
            loop.close()

        duration = time.time() - start_time
        logger.info(f"[USABILITY ANALYSIS] COMPLETED for URL: {url} | Duration: {duration:.2f} seconds")
        logger.debug(f"[USABILITY ANALYSIS] Results: {json.dumps(result, default=str)[:500]}...")

        cache_key = f"usability-result:{url}"
        try:
            redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
            logger.info(f"[USABILITY ANALYSIS] Cached results in Redis with key {cache_key}")
        except Exception as e:
            logger.error(f"[USABILITY ANALYSIS] Failed to cache results: {e}")

        return result
    except Exception as e:
        logger.error(f"[USABILITY ANALYSIS] ERROR: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Usability analysis failed: {str(e)}"}

@shared_task
def analyze_localseo_task(url, html, task_id):
    
    start_time = time.time()
    logger.info(f"[LOCAL SEO ANALYSIS] STARTING for URL: {url} | Task ID: {task_id}")

    try:
        logger.info(f"[LOCAL SEO ANALYSIS] Creating analyzer instance for URL: {url}")
        analyzer = LocalSEOAnalyzer(html_content=html, url=url, task_id=task_id)
        logger.info(f"[LOCAL SEO ANALYSIS] Analyzer instance created successfully")
    except Exception as e:
        logger.error(f"[LOCAL SEO ANALYSIS] ERROR creating analyzer instance: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Failed to create analyzer: {str(e)}"}

    try:
        logger.info(f"[LOCAL SEO ANALYSIS] Starting run_all() method")
        result = analyzer.run_all()
        logger.info(f"[LOCAL SEO ANALYSIS] Successfully completed run_all() method")
    except Exception as e:
        logger.error(f"[LOCAL SEO ANALYSIS] ERROR during run_all(): {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Local SEO analysis failed: {str(e)}"}

    duration = time.time() - start_time
    logger.info(f"[LOCAL SEO ANALYSIS] COMPLETED for URL: {url} | Task ID: {task_id} | Duration: {duration:.2f} seconds")
    logger.debug(f"[LOCAL SEO ANALYSIS] Results: {json.dumps(result, default=str)[:500]}...")

    cache_key = f"localseo-result:{task_id}"
    try:
        redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
        logger.info(f"[LOCAL SEO ANALYSIS] Cached results in Redis with key {cache_key}")
    except Exception as e:
        logger.error(f"[LOCAL SEO ANALYSIS] Failed to cache results: {e}")

    return result

@shared_task
def analyze_techseo_task(url, html, headers, task_id):
    
    start_time = time.time()
    logger.info(f"[TECHNOLOGY REVIEW ANALYSIS] STARTING for URL: {url} | Task ID: {task_id}")

    try:
        logger.info(f"[TECHNOLOGY REVIEW ANALYSIS] Creating analyzer instance for URL: {url}")
        analyzer = TechnologyReviewAnalyzer(url, html, headers, task_id)
        logger.info(f"[TECHNOLOGY REVIEW ANALYSIS] Analyzer instance created successfully")
    except Exception as e:
        logger.error(f"[TECHNOLOGY REVIEW ANALYSIS] ERROR creating analyzer instance: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Failed to create analyzer: {str(e)}"}

    try:
        logger.info(f"[TECHNOLOGY REVIEW ANALYSIS] Starting run_all() method")
        result = analyzer.run_all()
        logger.info(f"[TECHNOLOGY REVIEW ANALYSIS] Successfully completed run_all() method")
    except Exception as e:
        logger.error(f"[TECHNOLOGY REVIEW ANALYSIS] ERROR during run_all(): {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Technology Review analysis failed: {str(e)}"}

    duration = time.time() - start_time
    logger.info(f"[TECHNOLOGY REVIEW ANALYSIS] COMPLETED for URL: {url} | Task ID: {task_id} | Duration: {duration:.2f} seconds")
    logger.debug(f"[TECHNOLOGY REVIEW ANALYSIS] Results: {json.dumps(result, default=str)[:500]}...")

    cache_key = f"techseo-result:{task_id}"
    try:
        redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
        logger.info(f"[TECHNOLOGY REVIEW ANALYSIS] Cached results in Redis with key {cache_key}")
    except Exception as e:
        logger.error(f"[TECHNOLOGY REVIEW ANALYSIS] Failed to cache results: {e}")

    return result

@shared_task
def analyze_social_task(url, html, task_id):
    
    start_time = time.time()
    logger.info(f"[SOCIAL MEDIA ANALYSIS] STARTING for URL: {url} | Task ID: {task_id}")

    try:
        logger.info(f"[SOCIAL MEDIA ANALYSIS] Creating analyzer instance for URL: {url}")
        analyzer = SocialMediaAnalyzer(html, url, task_id)
        logger.info(f"[SOCIAL MEDIA ANALYSIS] Analyzer instance created successfully")
    except Exception as e:
        logger.error(f"[SOCIAL MEDIA ANALYSIS] ERROR creating analyzer instance: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Failed to create analyzer: {str(e)}"}

    try:
        logger.info(f"[SOCIAL MEDIA ANALYSIS] Starting run_all() method")
        result = analyzer.run_all()
        logger.info(f"[SOCIAL MEDIA ANALYSIS] Successfully completed run_all() method")
    except Exception as e:
        logger.error(f"[SOCIAL MEDIA ANALYSIS] ERROR during run_all(): {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Social media analysis failed: {str(e)}"}

    duration = time.time() - start_time
    logger.info(f"[SOCIAL MEDIA ANALYSIS] COMPLETED for URL: {url} | Task ID: {task_id} | Duration: {duration:.2f} seconds")
    logger.debug(f"[SOCIAL MEDIA ANALYSIS] Results: {json.dumps(result, default=str)[:500]}...")

    cache_key = f"social-result:{task_id}"
    try:
        redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
        logger.info(f"[SOCIAL MEDIA ANALYSIS] Cached results in Redis with key {cache_key}")
    except Exception as e:
        logger.error(f"[SOCIAL MEDIA ANALYSIS] Failed to cache results: {e}")

    return result

@shared_task
def analyze_performance_task(url, html, headers, task_id, console_logs=None):
    
    start_time = time.time()
    logger.info(f"[PERFORMANCE ANALYSIS] STARTING for URL: {url} | Task ID: {task_id}")

    try:
        logger.info(f"[PERFORMANCE ANALYSIS] Creating analyzer instance for URL: {url}")
        analyzer = PerformanceAnalyzer(html, headers, url, task_id, console_logs)
        logger.info(f"[PERFORMANCE ANALYSIS] Analyzer instance created successfully")
    except Exception as e:
        logger.error(f"[PERFORMANCE ANALYSIS] ERROR creating analyzer instance: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Failed to create analyzer: {str(e)}"}

    try:
        logger.info(f"[PERFORMANCE ANALYSIS] Starting run_all() method")
        result = analyzer.run_all()
        logger.info(f"[PERFORMANCE ANALYSIS] Successfully completed run_all() method")
    except Exception as e:
        logger.error(f"[PERFORMANCE ANALYSIS] ERROR during run_all(): {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Performance analysis failed: {str(e)}"}

    duration = time.time() - start_time
    logger.info(f"[PERFORMANCE ANALYSIS] COMPLETED for URL: {url} | Task ID: {task_id} | Duration: {duration:.2f} seconds")
    logger.debug(f"[PERFORMANCE ANALYSIS] Results: {json.dumps(result, default=str)[:500]}...")

    cache_key = f"performance-result:{task_id}"
    try:
        redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
        logger.info(f"[PERFORMANCE ANALYSIS] Cached results in Redis with key {cache_key}")
    except Exception as e:
        logger.error(f"[PERFORMANCE ANALYSIS] Failed to cache results: {e}")

    return result

@shared_task
def analyze_links_task(url, html, task_id):
    
    start_time = time.time()
    logger.info(f"[LINKS ANALYSIS] STARTING for URL: {url} | Task ID: {task_id}")

    try:
        logger.info(f"[LINKS ANALYSIS] Creating analyzer instance for URL: {url}")
        analyzer = LinksAnalyzer(url=url, task_id=task_id, html=html)
        logger.info(f"[LINKS ANALYSIS] Analyzer instance created successfully")
    except Exception as e:
        logger.error(f"[LINKS ANALYSIS] ERROR creating analyzer instance: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Failed to create analyzer: {str(e)}"}

    try:
        logger.info(f"[LINKS ANALYSIS] Starting analyze_all() method")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(analyzer.analyze_all())
        finally:
            loop.close()
        logger.info(f"[LINKS ANALYSIS] Successfully completed analyze_all() method")
    except Exception as e:
        logger.error(f"[LINKS ANALYSIS] ERROR during analyze_all(): {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": f"Links analysis failed: {str(e)}"}

    duration = time.time() - start_time
    logger.info(f"[LINKS ANALYSIS] COMPLETED for URL: {url} | Task ID: {task_id} | Duration: {duration:.2f} seconds")
    logger.debug(f"[LINKS ANALYSIS] Results: {json.dumps(result, default=str)[:500]}...")

    cache_key = f"links-result:{task_id}"
    try:
        redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
        logger.info(f"[LINKS ANALYSIS] Cached results in Redis with key {cache_key}")
    except Exception as e:
        logger.error(f"[LINKS ANALYSIS] Failed to cache results: {e}")

    return result

@shared_task
def analyze_child_pages_task(url, html, task_id):
    
    soup = BeautifulSoup(html, 'lxml')
    parsed_base = urlparse(url)
    base_domain = parsed_base.netloc
    paths = set()

    for a in soup.find_all('a', href=True):
        href = a['href']
        href_lower = href.strip().lower()
        
        if href_lower.startswith(('javascript:', 'mailto:', 'tel:')) or 'void(0)' in href_lower:
            continue

        parsed = urlparse(href)
        
        if parsed.netloc and parsed.netloc != base_domain:
            continue
        
        if parsed.scheme and parsed.scheme not in ('http', 'https'):
            continue

        path = parsed.path or '/'
        if not path.startswith('/'):
            path = '/' + path
        
        if '@' in path or re.fullmatch(r'/\+?\d+/?', path):
            continue
        paths.add(path)

    sorted_paths = sorted(paths)
    result = {
        "child_pages": sorted_paths,
        "total_count": len(sorted_paths)
    }

    try:
        cache_key = f"child-pages-result:{task_id}"
        redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
        logger.info(f"[CHILD_PAGES TASK] Cached {len(sorted_paths)} child pages with key {cache_key}")
    except Exception as e:
        logger.error(f"[CHILD_PAGES TASK] Failed to cache child pages: {e}")

    return result

TASK_ORDER = [
    'pagespeed_analysis',
    'pagespeed_mobile_analysis',
    'onpage_analysis',
    'usability_analysis',
    'localseo_analysis',
    'technology_review_analysis',
    'social_analysis',
    'performance_analysis',
    'links_analysis',
    'child_pages_data'
]

@shared_task
def aggregate_onpage_results(results, url=None, main_task_id=None, desktop_screenshot_key=None):
    logger.info(f"[AGGREGATE] STARTING aggregation for URL: {url} | Task ID: {main_task_id}")
    final_status = "success"
    aggregated_result_dict = {}

    try:
        if len(results) == len(TASK_ORDER):
            for i, task_key in enumerate(TASK_ORDER):
                res = results[i]
                if isinstance(res, dict) and "error" in res:
                    logger.warning(f"[AGGREGATE] Sub-task '{task_key}' result contains error: {res['error']}")
                    aggregated_result_dict[task_key] = {"error": res['error']}
                elif res is None:
                     logger.warning(f"[AGGREGATE] Sub-task '{task_key}' result is None.")
                     aggregated_result_dict[task_key] = {"error": "Task returned no result."}
                else:
                    aggregated_result_dict[task_key] = res
        else:
            logger.error(f"[AGGREGATE] Mismatch between expected tasks ({len(TASK_ORDER)}) and received results ({len(results)}). Cannot reliably map results.")
            final_status = "error"
            aggregated_result_dict = {"error": "Result aggregation failed due to task count mismatch."} 

        final_data_structure = {
            "status": final_status,
            "result": {}
        }
        
        if final_status == "success":
            try:
                base_api_url = getattr(settings, 'API_BASE_URL', 'http://seoanalyser.com.au/api')
                if not base_api_url.endswith('/'):
                    base_api_url += '/'
                screenshot_url = f"{base_api_url}screenshots/{desktop_screenshot_key}/" if desktop_screenshot_key else None
            except Exception as url_err:
                 logger.error(f"[AGGREGATE] Error constructing screenshot URL: {url_err}")
                 screenshot_url = None

            child_pages_data = aggregated_result_dict.get('child_pages_data', {})
            child_pages = {
                "pages": child_pages_data.get('child_pages', []),
                "total_count": child_pages_data.get('total_count', 0)
            }

            final_data_structure["result"] = {
                "desktop_screenshot_url": screenshot_url,
                "onpage_analysis": aggregated_result_dict.get('onpage_analysis'),
                "usability_analysis": aggregated_result_dict.get('usability_analysis'),
                "localseo_analysis": aggregated_result_dict.get('localseo_analysis'),
                "technology_review_analysis": aggregated_result_dict.get('technology_review_analysis'),
                "social_analysis": aggregated_result_dict.get('social_analysis'),
                "performance_analysis": aggregated_result_dict.get('performance_analysis'),
                "links_analysis": aggregated_result_dict.get('links_analysis'),
                "pagespeed_analysis": aggregated_result_dict.get('pagespeed_analysis'),
                "pagespeed_mobile_analysis": aggregated_result_dict.get('pagespeed_mobile_analysis'),
                "child_pages": child_pages
            }
        else:
             final_data_structure["result"] = aggregated_result_dict

        if url and main_task_id:
            try:
                analyzed_url_obj, created = AnalyzedURL.objects.get_or_create(
                    url=url,
                    defaults={'last_analyzed': timezone.now(), 'created_at': timezone.now()}
                )
                
                if not created:
                    analyzed_url_obj.last_analyzed = timezone.now()
                    analyzed_url_obj.save(update_fields=['last_analyzed'])

                record = AnalysisRecord.objects.create(
                    analyzed_url=analyzed_url_obj,
                    results=final_data_structure,
                    celery_task_id=main_task_id
                )
                logger.info(f"[AGGREGATE] Saved AnalysisRecord ID: {record.id} for URL: {url}")

            except Exception as db_exc:
                logger.error(f"[AGGREGATE] DATABASE ERROR saving results for URL {url}: {str(db_exc)}")
                logger.error(traceback.format_exc())
                final_data_structure["status"] = "error"
                final_data_structure["result"] = {"error": f"Failed to save results to database: {str(db_exc)}"}

        logger.info(f"[AGGREGATE] COMPLETED with status '{final_status}' for URL: {url}")
        return final_data_structure

    except Exception as e:
        logger.error(f"[AGGREGATE] UNEXPECTED ERROR during aggregation for URL {url}: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "status": "error",
            "result": {"error": f"Aggregation failed unexpectedly: {str(e)}"}
        }

@shared_task
def analyze_pagespeed_desktop_task(url, task_id):
    
    logger.info(f"[PAGESPEED DESKTOP TASK] STARTING for URL: {url} | Task ID: {task_id}")

    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            analyzer = PageSpeedDesktopAnalyzer(url, task_id)
            result = loop.run_until_complete(analyzer.run_all())
        finally:
            loop.close()

        logger.info(f"[PAGESPEED DESKTOP TASK] COMPLETED for URL: {url} | Score: {result.get('score')}")

        cache_key = f"pagespeed-desktop-result:{task_id}"
        try:
            redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
            logger.info(f"[PAGESPEED DESKTOP TASK] Cached results in Redis with key {cache_key}")
        except Exception as e:
            logger.error(f"[PAGESPEED DESKTOP TASK] Failed to cache results: {e}")

        return result
    except Exception as e:
        logger.error(f"[PAGESPEED DESKTOP TASK] ERROR: {str(e)}")
        logger.error(traceback.format_exc())
        return {"status": "error", "message": "Analysis failed", "error_details": str(e)}

@shared_task
def analyze_pagespeed_mobile_task(url, task_id):
    
    logger.info(f"[PAGESPEED MOBILE TASK] STARTING for URL: {url} | Task ID: {task_id}")

    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            analyzer = PageSpeedmobileAnalyzer(url, task_id)
            result = loop.run_until_complete(analyzer.run_all())
        finally:
            loop.close()

        logger.info(f"[PAGESPEED MOBILE TASK] COMPLETED for URL: {url} | Score: {result.get('score')}")

        cache_key = f"pagespeed-mobile-result:{task_id}"
        try:
            redis_client.set(cache_key, json.dumps(result, default=str), ex=3600)
            logger.info(f"[PAGESPEED MOBILE TASK] Cached results in Redis with key {cache_key}")
        except Exception as e:
            logger.error(f"[PAGESPEED MOBILE TASK] Failed to cache results: {e}")

        return result
    except Exception as e:
        logger.error(f"[PAGESPEED MOBILE TASK] ERROR: {str(e)}")
        logger.error(traceback.format_exc())
        return {"status": "error", "message": "Analysis failed", "error_details": str(e)}

@shared_task(bind=True)
def analyze_url(self, url):
    task_start_time = time.time()
    main_task_id = self.request.id
    logger.info(f"[MAIN TASK] STARTING analysis for URL: {url} | Task ID: {main_task_id}")

    try:
        self.update_state(
            state=states.STARTED,
            meta={"status": "loading_page", "message": f"Fetching content with Playwright for {url}"},
        )

        normalized_url = normalize_url(url) or url
        logger.info(f"[MAIN TASK] Normalized URL: {normalized_url}")

        # Get proxy string from settings
        proxy_string = getattr(settings, 'PROXY_STRING', None)
        if proxy_string:
            logger.info(f"[MAIN TASK] Using proxy configuration for PageContentFetcher.")
        else:
            logger.info(f"[MAIN TASK] No proxy configured in settings.")

        logger.info(f"[MAIN TASK] Creating PageContentFetcher for URL: {url}")
        # Pass proxy_string to the constructor
        fetcher = PageContentFetcher(redis_client_instance=redis_client, proxy_string=proxy_string)

        logger.info(f"[MAIN TASK] Setting up asyncio event loop")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        logger.info(f"[MAIN TASK] Fetching page content")
        content_start = time.time()
        result = loop.run_until_complete(fetcher.get_page_content(normalized_url))
        content_duration = time.time() - content_start
        logger.info(f"[MAIN TASK] Content fetch completed in {content_duration:.2f} seconds")
        loop.close()

        if not result["success"]:
            error_msg = f"Failed to fetch page: {result['error']}"
            logger.error(f"[MAIN TASK] {error_msg}")
            raise Exception(error_msg)

        self.update_state(
            state=states.STARTED,
            meta={"status": "analyzing", "message": "Running analysis tasks..."},
        )

        html_content = result["html"]
        task_id = self.request.id
        logger.info(f"[MAIN TASK] Page fetched successfully, page title: '{result['title']}'")

        headers = result.get("headers", {})
        desktop_screenshot_key = result["screenshots"].get('desktop')

        chord_tasks = [
            analyze_pagespeed_desktop_task.s(normalized_url, task_id),
            analyze_pagespeed_mobile_task.s(normalized_url, task_id),
            analyze_onpage_task.s(normalized_url, html_content, task_id),
            analyze_usability_task.s(normalized_url, task_id, html_content , result["screenshots"]),
            analyze_localseo_task.s(normalized_url, html_content, task_id),
            analyze_techseo_task.s(normalized_url, html_content, headers, task_id),
            analyze_social_task.s(normalized_url, html_content, task_id),
            analyze_performance_task.s(normalized_url, html_content, headers, task_id, result["console"]), 
            analyze_links_task.s(normalized_url, html_content, task_id),
            analyze_child_pages_task.s(normalized_url, html_content, task_id)
        ]

        chord_result = chord(chord_tasks)(aggregate_onpage_results.s(
            url=normalized_url,
            main_task_id=task_id,
            desktop_screenshot_key=desktop_screenshot_key
        ))

        redis_client.hset(f"analysis:{task_id}", "chord_id", chord_result.id)
        redis_client.hset(f"analysis:{task_id}", "url", normalized_url)
        redis_client.hset(f"analysis:{task_id}", "title", result["title"])
        redis_client.hset(f"analysis:{task_id}", "onpage_task_id", task_id)
        redis_client.hset(f"analysis:{task_id}", "usability_task_id", task_id)
        redis_client.hset(f"analysis:{task_id}", "localseo_task_id", task_id)
        redis_client.hset(f"analysis:{task_id}", "techseo_task_id", task_id)
        redis_client.hset(f"analysis:{task_id}", "social_task_id", task_id)
        redis_client.hset(f"analysis:{task_id}", "performance_task_id", task_id)
        redis_client.hset(f"analysis:{task_id}", "links_task_id", task_id)
        redis_client.hset(f"analysis:{task_id}", "pagespeed_mobile_task_id", task_id)
        redis_client.hset(f"analysis:{task_id}", "desktop_screenshot", desktop_screenshot_key)

        cache.set(f"analysis:{task_id}", {
            "chord_id": chord_result.id,
            "url": normalized_url,
            "title": result["title"],
            "onpage_task_id": task_id,
            "usability_task_id": task_id,
            "localseo_task_id": task_id,
            "techseo_task_id": task_id,
            "social_task_id": task_id,
            "performance_task_id": task_id,
            "links_task_id": task_id,
            "pagespeed_mobile_task_id": task_id,
            "desktop_screenshot": desktop_screenshot_key
        }, timeout=3600)

        task_duration = time.time() - task_start_time
        logger.info(f"[MAIN TASK] Chord task launched successfully in {task_duration:.2f} seconds")
        logger.info(f"[MAIN TASK] Chord task ID: {chord_result.id}")

        return {
            "chord_id": chord_result.id,
            "message": "Analysis tasks launched in parallel with chord. Check task status endpoints for results."
        }

    except Exception as e:
        logger.error(f"[MAIN TASK] ERROR analyzing {url}: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "status": "failed",
            "message": str(e),
        }

@shared_task(bind=True)
def get_analysis_results(self, task_id):
    
    logger.info(f"[GET_RESULTS] Retrieving results for task ID: {task_id}")

    try:
        analysis_info = redis_client.hgetall(f"analysis:{task_id}")

        if not analysis_info:
            analysis_info = cache.get(f"analysis:{task_id}")

        if not analysis_info:
            return {
                "status": "error",
                "message": f"No analysis info found for task ID: {task_id}"
            }

        if isinstance(analysis_info, dict) and not isinstance(analysis_info.get("title", None), str):
            analysis_info = {k.decode(): v.decode() for k, v in analysis_info.items()}

        logger.info(f"[GET_RESULTS] Analysis info: {analysis_info}")

        onpage_task_id = analysis_info.get("onpage_task_id")
        usability_task_id = analysis_info.get("usability_task_id")
        localseo_task_id = analysis_info.get("localseo_task_id")
        techseo_task_id = analysis_info.get("techseo_task_id")
        social_task_id = analysis_info.get("social_task_id")
        performance_task_id = analysis_info.get("performance_task_id")
        links_task_id = analysis_info.get("links_task_id")
        pagespeed_mobile_task_id = analysis_info.get("pagespeed_mobile_task_id")
        desktop_screenshot = analysis_info.get("desktop_screenshot")
        url = analysis_info.get("url")

        onpage_results = None
        usability_results = None
        localseo_results = None
        techseo_results = None
        social_results = None
        performance_results = None
        links_results = None  
        pagespeed_results = None  

        try:
            onpage_cache_keys = [f"onpage-result:{task_id}", f"onpage-result:{onpage_task_id}"]

            for key in onpage_cache_keys:
                onpage_data = redis_client.get(key)
                if onpage_data:
                    onpage_results = json.loads(onpage_data)
                    logger.info(f"[GET_RESULTS] Found onpage results in Redis cache with key: {key}")
                    break

        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting onpage results from Redis: {e}")

        try:
            if url:
                usability_cache_key = f"usability-result:{url}"
                usability_data = redis_client.get(usability_cache_key)
                if usability_data:
                    usability_results = json.loads(usability_data)
                    logger.info(f"[GET_RESULTS] Found usability results in Redis cache with URL key")

            if not usability_results and usability_task_id:
                usability_cache_key = f"usability-result:{usability_task_id}"
                usability_data = redis_client.get(usability_cache_key)
                if usability_data:
                    usability_results = json.loads(usability_data)
                    logger.info(f"[GET_RESULTS] Found usability results in Redis cache with task ID key")
        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting usability results from Redis: {e}")

        try:
            localseo_cache_key = f"localseo-result:{localseo_task_id}"
            localseo_data = redis_client.get(localseo_cache_key)
            if localseo_data:
                localseo_results = json.loads(localseo_data)
                logger.info(f"[GET_RESULTS] Found local SEO results in Redis cache with key: {localseo_cache_key}")
        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting local SEO results from Redis: {e}")

        try:
            techseo_cache_key = f"techseo-result:{techseo_task_id}"
            techseo_data = redis_client.get(techseo_cache_key)
            if techseo_data:
                techseo_results = json.loads(techseo_data)
                logger.info(f"[GET_RESULTS] Found Technology Review results in Redis cache with key: {techseo_cache_key}")
        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting Technology Review results from Redis: {e}")

        try:
            social_cache_key = f"social-result:{social_task_id}"
            social_data = redis_client.get(social_cache_key)
            if social_data:
                social_results = json.loads(social_data)
                logger.info(f"[GET_RESULTS] Found social media results in Redis cache with key: {social_cache_key}")
        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting social media results from Redis: {e}")

        try:
            performance_cache_key = f"performance-result:{performance_task_id}"
            performance_data = redis_client.get(performance_cache_key)
            if performance_data:
                performance_results = json.loads(performance_data)
                logger.info(f"[GET_RESULTS] Found performance results in Redis cache with key {performance_cache_key}")
        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting performance results from Redis: {e}")

        try:
            links_cache_key = f"links-result:{links_task_id}"
            links_data = redis_client.get(links_cache_key)
            if links_data:
                links_results = json.loads(links_data)
                logger.info(f"[GET_RESULTS] Found links results in Redis cache with key {links_cache_key}")
        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting links results from Redis: {e}")

        try:
            pagespeed_cache_key = f"pagespeed-desktop-result:{task_id}"
            pagespeed_data = redis_client.get(pagespeed_cache_key)
            if pagespeed_data:
                pagespeed_results = json.loads(pagespeed_data)
                logger.info(f"[GET_RESULTS] Found PageSpeed Desktop results in Redis cache with key: {pagespeed_cache_key}")
        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting PageSpeed Desktop results from Redis: {e}")

        try:
            pagespeed_mobile_cache_key = f"pagespeed-mobile-result:{pagespeed_mobile_task_id}"
            pagespeed_mobile_data = redis_client.get(pagespeed_mobile_cache_key)
            if pagespeed_mobile_data:
                pagespeed_mobile_results = json.loads(pagespeed_mobile_data)
                logger.info(f"[GET_RESULTS] Found PageSpeed Mobile results in Redis cache with key: {pagespeed_mobile_cache_key}")
        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting PageSpeed Mobile results from Redis: {e}")

        if not onpage_results and onpage_task_id:
            try:
                onpage_task = AsyncResult(onpage_task_id)
                if onpage_task.ready() and onpage_task.successful():
                    onpage_results = onpage_task.result
                    logger.info(f"[GET_RESULTS] Retrieved onpage results from task")

                    try:
                        redis_client.set(f"onpage-result:{task_id}", json.dumps(onpage_results, default=str), ex=3600)
                        logger.info(f"[GET_RESULTS] Cached onpage results from task")
                    except Exception as e:
                        logger.error(f"[GET_RESULTS] Error caching onpage results: {e}")
            except Exception as e:
                logger.error(f"[GET_RESULTS] Error checking onpage task: {e}")

        if not usability_results and usability_task_id:
            try:
                usability_task = AsyncResult(usability_task_id)
                if usability_task.ready() and usability_task.successful():
                    usability_results = usability_task.result
                    logger.info(f"[GET_RESULTS] Retrieved usability results from task")

                    if url:
                        try:
                            redis_client.set(f"usability-result:{url}", json.dumps(usability_results, default=str), ex=3600)
                            logger.info(f"[GET_RESULTS] Cached usability results from task with URL key")
                        except Exception as e:
                            logger.error(f"[GET_RESULTS] Error caching usability results: {e}")
            except Exception as e:
                logger.error(f"[GET_RESULTS] Error checking usability task: {e}")

        if not localseo_results and localseo_task_id:
            try:
                localseo_task = AsyncResult(localseo_task_id)
                if localseo_task.ready() and localseo_task.successful():
                    localseo_results = localseo_task.result
                    logger.info(f"[GET_RESULTS] Retrieved local SEO results from task")

                    try:
                        redis_client.set(f"localseo-result:{localseo_task_id}", json.dumps(localseo_results, default=str), ex=3600)
                        logger.info(f"[GET_RESULTS] Cached local SEO results from task")
                    except Exception as e:
                        logger.error(f"[GET_RESULTS] Error caching local SEO results: {e}")
            except Exception as e:
                logger.error(f"[GET_RESULTS] Error checking local SEO task: {e}")

        if not techseo_results and techseo_task_id:
            try:
                techseo_task = AsyncResult(techseo_task_id)
                if techseo_task.ready() and techseo_task.successful():
                    techseo_results = techseo_task.result
                    logger.info(f"[GET_RESULTS] Retrieved Technology Review results from task")

                    try:
                        redis_client.set(f"techseo-result:{techseo_task_id}", json.dumps(techseo_results, default=str), ex=3600)
                        logger.info(f"[GET_RESULTS] Cached Technology Review results from task")
                    except Exception as e:
                        logger.error(f"[GET_RESULTS] Error caching Technology Review results: {e}")
            except Exception as e:
                logger.error(f"[GET_RESULTS] Error checking Technology Review task: {e}")

        if not social_results and social_task_id:
            try:
                social_task = AsyncResult(social_task_id)
                if social_task.ready() and social_task.successful():
                    social_results = social_task.result
                    logger.info(f"[GET_RESULTS] Retrieved social media results from task")

                    try:
                        redis_client.set(f"social-result:{social_task_id}", json.dumps(social_results, default=str), ex=3600)
                        logger.info(f"[GET_RESULTS] Cached social media results from task")
                    except Exception as e:
                        logger.error(f"[GET_RESULTS] Error caching social media results: {e}")
            except Exception as e:
                logger.error(f"[GET_RESULTS] Error checking social media task: {e}")

        if not performance_results and performance_task_id:
            try:
                performance_task = AsyncResult(performance_task_id)
                if performance_task.ready() and performance_task.successful():
                    performance_results = performance_task.result
                    logger.info(f"[GET_RESULTS] Retrieved performance results from task")

                    try:
                        redis_client.set(f"performance-result:{performance_task_id}", json.dumps(performance_results, default=str), ex=3600)
                        logger.info(f"[GET_RESULTS] Cached performance results from task")
                    except Exception as e:
                        logger.error(f"[GET_RESULTS] Error caching performance results: {e}")
            except Exception as e:
                logger.error(f"[GET_RESULTS] Error checking performance task: {e}")

        if not links_results and links_task_id:
            try:
                links_task = AsyncResult(links_task_id)
                if links_task.ready() and links_task.successful():
                    links_results = links_task.result
                    logger.info(f"[GET_RESULTS] Retrieved links results from task")

                    try:
                        redis_client.set(f"links-result:{links_task_id}", json.dumps(links_results, default=str), ex=3600)
                        logger.info(f"[GET_RESULTS] Cached links results from task")
                    except Exception as e:
                        logger.error(f"[GET_RESULTS] Error caching links results: {e}")
            except Exception as e:
                logger.error(f"[GET_RESULTS] Error checking links task: {e}")

        combined_result = {
        }

        if onpage_results:
            combined_result["onpage_analysis"] = onpage_results
        else:
            combined_result["onpage_analysis"] = "in_progress"

        if usability_results:
            combined_result["usability_analysis"] = usability_results
        else:
            combined_result["usability_analysis"] = "in_progress"

        if localseo_results:
            combined_result["localseo_analysis"] = localseo_results
        else:
            combined_result["localseo_analysis"] = "in_progress"

        if techseo_results:
            combined_result["technology_review_analysis"] = techseo_results
        else:
            combined_result["technology_review_analysis"] = "in_progress"

        if social_results:
            combined_result["social_analysis"] = social_results
        else:
            combined_result["social_analysis"] = "in_progress"

        if performance_results:
            combined_result["performance_analysis"] = performance_results
        else:
            combined_result["performance_analysis"] = "in_progress"

        if links_results:
            combined_result["links_analysis"] = links_results
        else:
            combined_result["links_analysis"] = "in_progress"

        if pagespeed_results:
            combined_result["pagespeed_analysis"] = pagespeed_results
        else:
            combined_result["pagespeed_analysis"] = "in_progress"

        combined_result["desktop_screenshot_key"] = desktop_screenshot or None

        try:
            child_cache_key = f"child-pages-result:{task_id}"
            child_data = redis_client.get(child_cache_key)
            if child_data:
                parsed_child = json.loads(child_data)
                child_pages = parsed_child.get("child_pages")
                total_count = parsed_child.get("total_count")
                combined_result["child_pages"] = child_pages
                combined_result["child_pages_count"] = total_count
                logger.info(f"[GET_RESULTS] Found child pages in Redis cache with count {total_count} and key: {child_cache_key}")
            else:
                combined_result["child_pages"] = "in_progress"
                combined_result["child_pages_count"] = "in_progress"
        except Exception as e:
            logger.error(f"[GET_RESULTS] Error getting child pages results from Redis: {e}")
            combined_result["child_pages"] = "in_progress"
            combined_result["child_pages_count"] = "in_progress"

        if onpage_results and usability_results and localseo_results and techseo_results and social_results and performance_results and links_results and pagespeed_results and pagespeed_mobile_results:
            try:
                redis_client.set(f"analysis-results:{task_id}", json.dumps(combined_result, default=str), ex=3600)
                logger.info(f"[GET_RESULTS] Cached combined results for faster future access")
            except Exception as e:
                logger.error(f"[GET_RESULTS] Error caching combined results: {e}")

        if onpage_results and usability_results and localseo_results and techseo_results and social_results and performance_results and links_results and pagespeed_results and pagespeed_mobile_results:
            logger.info(f"[GET_RESULTS] All analyses complete, returning combined results")

        elif onpage_results or usability_results or localseo_results or techseo_results or social_results or performance_results or links_results or pagespeed_results or pagespeed_mobile_results:
            logger.info(f"[GET_RESULTS] Partial results available. Onpage: {onpage_results is not None}, Usability: {usability_results is not None}, LocalSEO: {localseo_results is not None}, TechSEO: {techseo_results is not None}, Social: {social_results is not None}, Performance: {performance_results is not None}, Links: {links_results is not None}, PageSpeed: {pagespeed_results is not None}")

        else:
            logger.info(f"[GET_RESULTS] No results available yet")
            combined_result["onpage_task_id"] = onpage_task_id
            combined_result["usability_task_id"] = usability_task_id
            combined_result["localseo_task_id"] = localseo_task_id
            combined_result["techseo_task_id"] = techseo_task_id
            combined_result["social_task_id"] = social_task_id
            combined_result["performance_task_id"] = performance_task_id
            combined_result["links_task_id"] = links_task_id
            combined_result["pagespeed_mobile_task_id"] = pagespeed_mobile_task_id
            combined_result["desktop_screenshot_key"] = desktop_screenshot or None
            combined_result["message"] = "Analysis is still in progress"

        return combined_result

    except Exception as e:
        logger.error(f"[GET_RESULTS] Error retrieving results: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "status": "error",
            "message": f"Error retrieving results: {str(e)}"
        }