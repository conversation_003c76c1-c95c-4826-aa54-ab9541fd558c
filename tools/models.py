from django.db import models
from django.utils import timezone


class AnalyzedURL(models.Model):
    url = models.URLField(max_length=2000, unique=True)
    last_analyzed = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return self.url

    class Meta:
        verbose_name = "Analyzed URL"
        verbose_name_plural = "Analyzed URLs"


class AnalysisRecord(models.Model):
    analyzed_url = models.ForeignKey(AnalyzedURL, on_delete=models.CASCADE, related_name='analysis_records')
    analysis_timestamp = models.DateTimeField(auto_now_add=True)
    results = models.JSONField()
    celery_task_id = models.CharField(max_length=255, default='legacy')
    
    def __str__(self):
        return f"{self.analyzed_url.url} - {self.analysis_timestamp}"

    class Meta:
        ordering = ['-analysis_timestamp']
        verbose_name = "Analysis Record"
        verbose_name_plural = "Analysis Records"


class KeywordResearch(models.Model):
    # ... existing code ...
    pass


class CompetitorAnalysis(models.Model):
    # ... existing code ...
    pass


class BacklinkProfile(models.Model):
    # ... existing code ...
    pass


class AuditTemplate(models.Model):
    # ... existing code ...
    pass


class Report(models.Model):
    # ... existing code ...
    pass


class SecurityHeaders(models.Model):
    # ... existing code ...
    pass

