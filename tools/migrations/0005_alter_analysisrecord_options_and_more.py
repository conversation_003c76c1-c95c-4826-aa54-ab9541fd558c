# Generated by Django 5.2 on 2025-04-30 08:34

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tools', '0004_analysisrecord_analyzedurl_delete_analyzehistory_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='analysisrecord',
            options={'ordering': ['-analysis_timestamp'], 'verbose_name': 'Analysis Record', 'verbose_name_plural': 'Analysis Records'},
        ),
        migrations.AlterModelOptions(
            name='analyzedurl',
            options={'verbose_name': 'Analyzed URL', 'verbose_name_plural': 'Analyzed URLs'},
        ),
        migrations.AddField(
            model_name='analyzedurl',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='analysisrecord',
            name='analysis_timestamp',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.AlterField(
            model_name='analysisrecord',
            name='analyzed_url',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_records', to='tools.analyzedurl'),
        ),
        migrations.AlterField(
            model_name='analysisrecord',
            name='celery_task_id',
            field=models.CharField(default='legacy', max_length=255),
        ),
        migrations.AlterField(
            model_name='analyzedurl',
            name='last_analyzed',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name='analyzedurl',
            name='url',
            field=models.URLField(max_length=2000, unique=True),
        ),
    ]
