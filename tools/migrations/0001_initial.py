# Generated by Django 5.2 on 2025-04-29 11:20

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AnalysisHistory',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('url', models.URLField(db_index=True, help_text='The URL that was analyzed.', max_length=2048)),
                ('task_id', models.Char<PERSON>ield(db_index=True, help_text='The main Celery task ID for the analysis.', max_length=255, unique=True)),
                ('analysis_date', models.DateTimeField(auto_now_add=True, help_text='Timestamp when the analysis was completed and saved.')),
                ('results', models.J<PERSON><PERSON>ield(help_text='The full JSON results of the analysis.')),
            ],
            options={
                'verbose_name': 'Analysis History',
                'verbose_name_plural': 'Analysis Histories',
                'ordering': ['-analysis_date', 'url'],
            },
        ),
    ]
