# Generated by Django 5.2 on 2025-04-29 11:35

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tools', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnalysisResult',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('task_id', models.CharField(db_index=True, help_text='Celery task ID for the main analyze_url task', max_length=255, unique=True)),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('result_json', models.JSONField(help_text='Full JSON result of the analysis')),
            ],
            options={
                'verbose_name': 'Analysis Result',
                'verbose_name_plural': 'Analysis Results',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='AnalyzedURL',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(db_index=True, max_length=2048, unique=True)),
                ('first_analyzed', models.DateTimeField(auto_now_add=True)),
                ('last_analyzed', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Analyzed URL',
                'verbose_name_plural': 'Analyzed URLs',
                'ordering': ['-last_analyzed'],
            },
        ),
        migrations.DeleteModel(
            name='AnalysisHistory',
        ),
        migrations.AddField(
            model_name='analysisresult',
            name='analyzed_url',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_results', to='tools.analyzedurl'),
        ),
    ]
