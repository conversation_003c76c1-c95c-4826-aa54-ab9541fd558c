# Generated by Django 5.2 on 2025-04-29 12:49

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tools', '0002_analysisresult_analyzedurl_delete_analysishistory_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnalyzeHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.URLField(db_index=True, max_length=2048)),
                ('task_id', models.CharField(db_index=True, max_length=255, unique=True)),
                ('analysis_date', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('results', models.JSONField()),
            ],
            options={
                'verbose_name': 'Analyze History',
                'verbose_name_plural': 'Analyze Histories',
                'ordering': ['-analysis_date'],
                'indexes': [models.Index(fields=['url', '-analysis_date'], name='tools_analy_url_17bf4f_idx')],
            },
        ),
        migrations.DeleteModel(
            name='AnalysisResult',
        ),
        migrations.DeleteModel(
            name='AnalyzedURL',
        ),
    ]
