# Generated by Django 5.2 on 2025-04-30 08:18

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tools', '0003_analyzehistory_delete_analysisresult_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnalysisRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('analysis_timestamp', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('results', models.JSONField()),
                ('celery_task_id', models.CharField(blank=True, db_index=True, max_length=255, null=True)),
            ],
            options={
                'ordering': ['-analysis_timestamp'],
            },
        ),
        migrations.CreateModel(
            name='AnalyzedURL',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('url', models.TextField(db_index=True, unique=True)),
                ('last_analyzed', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.DeleteModel(
            name='AnalyzeHistory',
        ),
        migrations.AddField(
            model_name='analysisrecord',
            name='analyzed_url',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='records', to='tools.analyzedurl'),
        ),
    ]
