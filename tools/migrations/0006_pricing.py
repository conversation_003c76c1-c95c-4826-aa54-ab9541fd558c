# Generated by Django 5.2 on 2025-05-12 08:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tools', '0005_alter_analysisrecord_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Pricing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('period', models.CharField(choices=[('daily', 'Daily'), ('monthly', 'Monthly'), ('yearly', 'Yearly'), ('1_month', '1 Month'), ('2_months', '2 Months'), ('3_months', '3 Months'), ('4_months', '4 Months'), ('5_months', '5 Months'), ('6_months', '6 Months'), ('7_months', '7 Months'), ('8_months', '8 Months'), ('9_months', '9 Months'), ('10_months', '10 Months'), ('11_months', '11 Months'), ('12_months', '12 Months')], default='monthly', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Pricing Plan',
                'verbose_name_plural': 'Pricing Plans',
                'ordering': ['name'],
            },
        ),
    ]
