# Generated by Django 5.2 on 2025-05-12 09:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('tools', '0006_pricing'),
    ]

    operations = [
        migrations.AlterField(
            model_name='pricing',
            name='discount_percentage',
            field=models.SmallIntegerField(choices=[(0, 'No Discount'), (10, '10%'), (15, '15%'), (20, '20%'), (25, '25%'), (30, '30%'), (35, '35%'), (40, '40%'), (45, '45%'), (50, '50%'), (55, '55%'), (60, '60%'), (65, '65%'), (70, '70%'), (75, '75%'), (80, '80%'), (85, '85%'), (90, '90%'), (95, '95%')], default=0, help_text='Select discount percentage'),
        ),
    ]
