from django.contrib import admin
from .models import AnalyzedURL, AnalysisRecord, KeywordResearch, CompetitorAnalysis, BacklinkProfile, AuditTemplate, Report, SecurityHeaders


@admin.register(AnalyzedURL)
class AnalyzedURLAdmin(admin.ModelAdmin):
    list_display = ('url', 'last_analyzed', 'created_at')
    search_fields = ('url',)
    ordering = ('-last_analyzed',)


@admin.register(AnalysisRecord)
class AnalysisRecordAdmin(admin.ModelAdmin):
    list_display = ('analyzed_url', 'analysis_timestamp', 'celery_task_id')
    search_fields = ('analyzed_url__url', 'celery_task_id')
    ordering = ('-analysis_timestamp',)

# Commenting out problematic admin registrations for now
# @admin.register(KeywordResearch)
# class KeywordResearchAdmin(admin.ModelAdmin):
#     list_display = ('keyword', 'search_volume', 'competition')
#     search_fields = ('keyword',)
#     ordering = ('keyword',)
# 
# 
# @admin.register(CompetitorAnalysis)
# class CompetitorAnalysisAdmin(admin.ModelAdmin):
#     list_display = ('website', 'domain', 'traffic')
#     search_fields = ('website', 'domain')
#     ordering = ('website',)
# 
# 
# @admin.register(BacklinkProfile)
# class BacklinkProfileAdmin(admin.ModelAdmin):
#     list_display = ('website', 'total_backlinks', 'top_domains')
#     search_fields = ('website',)
#     ordering = ('website',)
# 
# 
# @admin.register(AuditTemplate)
# class AuditTemplateAdmin(admin.ModelAdmin):
#     list_display = ('name', 'description')
#     search_fields = ('name',)
#     ordering = ('name',)
# 
# 
# @admin.register(Report)
# class ReportAdmin(admin.ModelAdmin):
#     list_display = ('title', 'analysis_record', 'generated_at')
#     search_fields = ('title', 'analysis_record__analyzed_url__url')
#     ordering = ('-generated_at',)
# 
# 
# @admin.register(SecurityHeaders)
# class SecurityHeadersAdmin(admin.ModelAdmin):
#     list_display = ('website', 'https_status', 'certificate_expiration')
#     search_fields = ('website',)
#     ordering = ('website',)

