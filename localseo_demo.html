<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocalSEO Platform - Complete Feature Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .demo-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .demo-card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.4rem;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2d3748;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .response-area {
            margin-top: 20px;
            padding: 15px;
            background: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            max-height: 300px;
            overflow-y: auto;
        }

        .response-area pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 12px;
            color: #2d3748;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background: #48bb78; }
        .status-error { background: #f56565; }
        .status-pending { background: #ed8936; }

        .endpoint-info {
            background: #edf2f7;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #4a5568;
        }

        .feature-tabs {
            display: flex;
            margin-bottom: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .tab-button {
            flex: 1;
            padding: 15px;
            background: #f7fafc;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: #667eea;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .auth-status {
            background: #fed7d7;
            color: #c53030;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 600;
        }

        .auth-status.connected {
            background: #c6f6d5;
            color: #2f855a;
        }

        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .feature-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 LocalSEO Platform</h1>
            <p>Complete Feature Demo & API Testing Suite</p>
        </div>

        <!-- Feature Navigation Tabs -->
        <div class="feature-tabs">
            <button class="tab-button active" onclick="showTab('business-search')">Business Search</button>
            <button class="tab-button" onclick="showTab('gbp-integration')">Google Business Profile</button>
            <button class="tab-button" onclick="showTab('competitor-analysis')">Competitor Analysis</button>
            <button class="tab-button" onclick="showTab('ai-recommendations')">AI Recommendations</button>
            <button class="tab-button" onclick="showTab('data-collection')">Data Collection</button>
        </div>

        <!-- Business Search Tab -->
        <div id="business-search" class="tab-content active">
            <div class="demo-grid">
                <!-- Business Search Form -->
                <div class="demo-card">
                    <h3>🔍 Business Search</h3>
                    <div class="endpoint-info">GET /localseo/ | POST /localseo/collect/</div>

                    <form id="business-search-form">
                        <div class="form-group">
                            <label>Search Query</label>
                            <input type="text" id="search-query" placeholder="e.g., restaurants, dentists, plumbers" value="restaurants">
                        </div>

                        <div class="form-group">
                            <label>Location (Lat,Lng)</label>
                            <input type="text" id="search-location" placeholder="-33.8688,151.2093" value="-33.8688,151.2093">
                        </div>

                        <div class="form-group">
                            <label>Category Filter</label>
                            <select id="search-category">
                                <option value="">All Categories</option>
                                <option value="restaurant">Restaurant</option>
                                <option value="health">Health & Medical</option>
                                <option value="retail">Retail</option>
                                <option value="services">Services</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Domain Filter</label>
                            <select id="domain-filter">
                                <option value="">All Domains</option>
                                <option value="com.au">.com.au only</option>
                            </select>
                        </div>

                        <button type="submit" class="btn">Search Businesses</button>
                    </form>

                    <div id="search-response" class="response-area" style="display: none;">
                        <pre id="search-results"></pre>
                    </div>
                </div>

                <!-- Place Lookup -->
                <div class="demo-card">
                    <h3>📍 Google Place Lookup</h3>
                    <div class="endpoint-info">GET /localseo/lookup-place/</div>

                    <form id="place-lookup-form">
                        <div class="form-group">
                            <label>Google Place ID</label>
                            <input type="text" id="place-id" placeholder="ChIJ_wvuwJ-iEmsRAsIW9cBQ5KM" value="ChIJ_wvuwJ-iEmsRAsIW9cBQ5KM">
                        </div>

                        <div class="form-group">
                            <label>Google Places API Key</label>
                            <input type="text" id="places-api-key" placeholder="AIzaSy...">
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="update-db"> Update Database
                            </label>
                        </div>

                        <button type="submit" class="btn">Lookup Place</button>
                    </form>

                    <div id="lookup-response" class="response-area" style="display: none;">
                        <pre id="lookup-results"></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Google Business Profile Tab -->
        <div id="gbp-integration" class="tab-content">
            <div class="demo-grid">
                <!-- GBP Connection -->
                <div class="demo-card">
                    <h3>🔗 Google Business Profile Connection</h3>
                    <div class="endpoint-info">GET /localseo/gbp/connect/ | GET /localseo/gbp/callback/</div>

                    <div id="gbp-auth-status" class="auth-status">
                        <span class="status-indicator status-error"></span>
                        Not Connected
                    </div>

                    <button id="gbp-connect-btn" class="btn">Connect Google Business Profile</button>
                    <button id="gbp-debug-btn" class="btn btn-secondary">Debug OAuth Config</button>

                    <div id="gbp-connect-response" class="response-area" style="display: none;">
                        <pre id="gbp-connect-results"></pre>
                    </div>
                </div>

                <!-- GBP Dashboard -->
                <div class="demo-card">
                    <h3>📊 GBP Dashboard</h3>
                    <div class="endpoint-info">GET /localseo/gbp/dashboard/</div>

                    <form id="gbp-dashboard-form">
                        <div class="form-group">
                            <label>Location ID (optional)</label>
                            <input type="number" id="gbp-location-id" placeholder="Auto-select if empty">
                        </div>

                        <button type="submit" class="btn">Load Dashboard</button>
                    </form>

                    <div id="gbp-dashboard-response" class="response-area" style="display: none;">
                        <pre id="gbp-dashboard-results"></pre>
                    </div>
                </div>

                <!-- GBP Data Refresh -->
                <div class="demo-card">
                    <h3>🔄 Refresh GBP Data</h3>
                    <div class="endpoint-info">POST /localseo/gbp/refresh/{location_id}/</div>

                    <form id="gbp-refresh-form">
                        <div class="form-group">
                            <label>Location ID</label>
                            <input type="number" id="refresh-location-id" placeholder="Enter location ID" required>
                        </div>

                        <button type="submit" class="btn">Refresh Data</button>
                    </form>

                    <div id="gbp-refresh-response" class="response-area" style="display: none;">
                        <pre id="gbp-refresh-results"></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Competitor Analysis Tab -->
        <div id="competitor-analysis" class="tab-content">
            <div class="demo-grid">
                <!-- Competitor Discovery -->
                <div class="demo-card">
                    <h3>🏆 Competitor Analysis</h3>
                    <div class="endpoint-info">GET /localseo/gbp/competitors/{location_id}/</div>

                    <form id="competitor-analysis-form">
                        <div class="form-group">
                            <label>Location ID</label>
                            <input type="number" id="competitor-location-id" placeholder="Enter location ID" required>
                        </div>

                        <button type="submit" class="btn">Analyze Competitors</button>
                    </form>

                    <div id="competitor-response" class="response-area" style="display: none;">
                        <pre id="competitor-results"></pre>
                    </div>
                </div>

                <!-- Business Detail Analysis -->
                <div class="demo-card">
                    <h3>📈 Business Detail Analysis</h3>
                    <div class="endpoint-info">GET /localseo/business/{business_id}/</div>

                    <form id="business-detail-form">
                        <div class="form-group">
                            <label>Business ID</label>
                            <input type="number" id="business-detail-id" placeholder="Enter business ID" required>
                        </div>

                        <button type="submit" class="btn">Analyze Business</button>
                    </form>

                    <div id="business-detail-response" class="response-area" style="display: none;">
                        <pre id="business-detail-results"></pre>
                    </div>
                </div>

                <!-- API Key Testing -->
                <div class="demo-card">
                    <h3>🔑 API Key Testing</h3>
                    <div class="endpoint-info">GET /localseo/test-api-key/</div>

                    <form id="api-test-form">
                        <div class="form-group">
                            <label>Google Places API Key</label>
                            <input type="text" id="test-api-key" placeholder="AIzaSy..." required>
                        </div>

                        <button type="submit" class="btn">Test API Key</button>
                    </form>

                    <div id="api-test-response" class="response-area" style="display: none;">
                        <pre id="api-test-results"></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI Recommendations Tab -->
        <div id="ai-recommendations" class="tab-content">
            <div class="demo-grid">
                <!-- Generate Recommendations -->
                <div class="demo-card">
                    <h3>🤖 AI-Powered Recommendations</h3>
                    <div class="endpoint-info">POST /localseo/gbp/recommendations/{location_id}/</div>

                    <form id="ai-recommendations-form">
                        <div class="form-group">
                            <label>Location ID</label>
                            <input type="number" id="ai-location-id" placeholder="Enter location ID" required>
                        </div>

                        <div class="form-group">
                            <label>OpenAI API Key</label>
                            <input type="text" id="openai-api-key" placeholder="sk-proj-..." value="********************************************************************************************************************************************************************">
                        </div>

                        <button type="submit" class="btn">Generate Recommendations</button>
                    </form>

                    <div id="ai-recommendations-response" class="response-area" style="display: none;">
                        <pre id="ai-recommendations-results"></pre>
                    </div>
                </div>

                <!-- Custom Business Analysis -->
                <div class="demo-card">
                    <h3>📊 Custom Business Analysis</h3>
                    <div class="endpoint-info">Custom GPT Analysis</div>

                    <form id="custom-analysis-form">
                        <div class="form-group">
                            <label>Business Name</label>
                            <input type="text" id="custom-business-name" placeholder="Digital Apex" value="Digital Apex">
                        </div>

                        <div class="form-group">
                            <label>Business Type</label>
                            <input type="text" id="custom-business-type" placeholder="Website Design" value="Website Design">
                        </div>

                        <div class="form-group">
                            <label>Location</label>
                            <input type="text" id="custom-business-location" placeholder="Kellyville, NSW" value="Kellyville, NSW">
                        </div>

                        <div class="form-group">
                            <label>Current Rating</label>
                            <input type="number" id="custom-business-rating" placeholder="4.5" step="0.1" min="0" max="5" value="4.5">
                        </div>

                        <div class="form-group">
                            <label>Review Count</label>
                            <input type="number" id="custom-review-count" placeholder="25" value="25">
                        </div>

                        <button type="submit" class="btn">Analyze with AI</button>
                    </form>

                    <div id="custom-analysis-response" class="response-area" style="display: none;">
                        <pre id="custom-analysis-results"></pre>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Collection Tab -->
        <div id="data-collection" class="tab-content">
            <div class="demo-grid">
                <!-- Bulk Data Collection -->
                <div class="demo-card">
                    <h3>📥 Bulk Data Collection</h3>
                    <div class="endpoint-info">POST /localseo/collect/</div>

                    <form id="bulk-collection-form">
                        <div class="form-group">
                            <label>Search Query</label>
                            <input type="text" id="bulk-query" placeholder="restaurants in Sydney" value="restaurants in Sydney" required>
                        </div>

                        <div class="form-group">
                            <label>Location (Lat,Lng)</label>
                            <input type="text" id="bulk-location" placeholder="-33.8688,151.2093" value="-33.8688,151.2093">
                        </div>

                        <div class="form-group">
                            <label>Search Radius (meters)</label>
                            <input type="number" id="bulk-radius" placeholder="5000" value="5000" min="100" max="50000">
                        </div>

                        <div class="form-group">
                            <label>Maximum Results</label>
                            <input type="number" id="bulk-max-results" placeholder="20" value="20" min="1" max="60">
                        </div>

                        <div class="form-group">
                            <label>Google Places API Key</label>
                            <input type="text" id="bulk-api-key" placeholder="AIzaSy..." required>
                        </div>

                        <button type="submit" class="btn">Collect Business Data</button>
                    </form>

                    <div id="bulk-collection-response" class="response-area" style="display: none;">
                        <pre id="bulk-collection-results"></pre>
                    </div>
                </div>

                <!-- Authentication Testing -->
                <div class="demo-card">
                    <h3>🔐 Authentication Testing</h3>
                    <div class="endpoint-info">GET /localseo/login/ | POST /localseo/logout/</div>

                    <div class="form-group">
                        <label>Current Authentication Status</label>
                        <div id="auth-status" class="auth-status">
                            <span class="status-indicator status-error"></span>
                            Not Authenticated
                        </div>
                    </div>

                    <button id="check-auth-btn" class="btn">Check Authentication</button>
                    <button id="login-btn" class="btn btn-secondary">Go to Login</button>
                    <button id="logout-btn" class="btn btn-danger">Logout</button>

                    <div id="auth-response" class="response-area" style="display: none;">
                        <pre id="auth-results"></pre>
                    </div>
                </div>

                <!-- System Status -->
                <div class="demo-card">
                    <h3>⚡ System Status</h3>
                    <div class="endpoint-info">System Health Check</div>

                    <div class="form-group">
                        <label>Database Connection</label>
                        <div id="db-status" class="auth-status">
                            <span class="status-indicator status-pending"></span>
                            Checking...
                        </div>
                    </div>

                    <div class="form-group">
                        <label>API Endpoints</label>
                        <div id="api-status" class="auth-status">
                            <span class="status-indicator status-pending"></span>
                            Checking...
                        </div>
                    </div>

                    <button id="system-check-btn" class="btn">Run System Check</button>

                    <div id="system-response" class="response-area" style="display: none;">
                        <pre id="system-results"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const BASE_URL = 'http://127.0.0.1:8000';
        const LOCALSEO_BASE = `${BASE_URL}/localseo`;

        // Utility functions
        function showResponse(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            const pre = element.querySelector('pre');

            if (isError) {
                pre.style.color = '#e53e3e';
                pre.textContent = `Error: ${data}`;
            } else {
                pre.style.color = '#2d3748';
                pre.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
            }

            element.style.display = 'block';
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            const indicator = element.querySelector('.status-indicator');

            indicator.className = `status-indicator status-${status}`;
            element.className = `auth-status ${status === 'success' ? 'connected' : ''}`;
            element.innerHTML = `<span class="status-indicator status-${status}"></span>${message}`;
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    credentials: 'include',
                    ...options
                });

                const contentType = response.headers.get('content-type');
                let data;

                if (contentType && contentType.includes('application/json')) {
                    data = await response.json();
                } else {
                    data = await response.text();
                }

                return {
                    ok: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    ok: false,
                    status: 0,
                    data: error.message
                };
            }
        }

        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Business Search functionality
        document.getElementById('business-search-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const query = document.getElementById('search-query').value;
            const location = document.getElementById('search-location').value;
            const category = document.getElementById('search-category').value;
            const domainFilter = document.getElementById('domain-filter').value;

            let url = `${LOCALSEO_BASE}/?query=${encodeURIComponent(query)}`;
            if (location) url += `&location=${encodeURIComponent(location)}`;
            if (category) url += `&category=${encodeURIComponent(category)}`;
            if (domainFilter) url += `&domain_filter=${encodeURIComponent(domainFilter)}`;

            const result = await makeRequest(url);
            showResponse('search-response', result.data, !result.ok);
        });

        // Place Lookup functionality
        document.getElementById('place-lookup-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const placeId = document.getElementById('place-id').value;
            const apiKey = document.getElementById('places-api-key').value;
            const updateDb = document.getElementById('update-db').checked;

            let url = `${LOCALSEO_BASE}/lookup-place/?place_id=${encodeURIComponent(placeId)}`;
            if (apiKey) url += `&api_key=${encodeURIComponent(apiKey)}`;
            if (updateDb) url += `&update_db=true`;

            const result = await makeRequest(url);
            showResponse('lookup-response', result.data, !result.ok);
        });

        // GBP Connect functionality
        document.getElementById('gbp-connect-btn').addEventListener('click', async () => {
            window.open(`${LOCALSEO_BASE}/gbp/connect/`, '_blank');
        });

        document.getElementById('gbp-debug-btn').addEventListener('click', async () => {
            const result = await makeRequest(`${LOCALSEO_BASE}/gbp/debug/`);
            showResponse('gbp-connect-response', result.data, !result.ok);
        });

        // GBP Dashboard functionality
        document.getElementById('gbp-dashboard-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const locationId = document.getElementById('gbp-location-id').value;
            let url = `${LOCALSEO_BASE}/gbp/dashboard/`;
            if (locationId) url += `?location_id=${locationId}`;

            const result = await makeRequest(url);
            showResponse('gbp-dashboard-response', result.data, !result.ok);
        });

        // GBP Refresh functionality
        document.getElementById('gbp-refresh-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const locationId = document.getElementById('refresh-location-id').value;
            const url = `${LOCALSEO_BASE}/gbp/refresh/${locationId}/`;

            const result = await makeRequest(url, { method: 'POST' });
            showResponse('gbp-refresh-response', result.data, !result.ok);
        });

        // Competitor Analysis functionality
        document.getElementById('competitor-analysis-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const locationId = document.getElementById('competitor-location-id').value;
            const url = `${LOCALSEO_BASE}/gbp/competitors/${locationId}/`;

            const result = await makeRequest(url);
            showResponse('competitor-response', result.data, !result.ok);
        });

        // Business Detail functionality
        document.getElementById('business-detail-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const businessId = document.getElementById('business-detail-id').value;
            const url = `${LOCALSEO_BASE}/business/${businessId}/`;

            const result = await makeRequest(url);
            showResponse('business-detail-response', result.data, !result.ok);
        });

        // API Key Testing functionality
        document.getElementById('api-test-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const apiKey = document.getElementById('test-api-key').value;
            const url = `${LOCALSEO_BASE}/test-api-key/?api_key=${encodeURIComponent(apiKey)}`;

            const result = await makeRequest(url);
            showResponse('api-test-response', result.data, !result.ok);
        });

        // AI Recommendations functionality
        document.getElementById('ai-recommendations-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const locationId = document.getElementById('ai-location-id').value;
            const apiKey = document.getElementById('openai-api-key').value;

            const formData = new FormData();
            formData.append('openai_api_key', apiKey);

            const url = `${LOCALSEO_BASE}/gbp/recommendations/${locationId}/`;
            const result = await makeRequest(url, {
                method: 'POST',
                body: formData
            });

            showResponse('ai-recommendations-response', result.data, !result.ok);
        });

        // Custom Analysis functionality
        document.getElementById('custom-analysis-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const businessData = {
                name: document.getElementById('custom-business-name').value,
                business_type: document.getElementById('custom-business-type').value,
                location: document.getElementById('custom-business-location').value,
                rating: parseFloat(document.getElementById('custom-business-rating').value),
                reviews_count: parseInt(document.getElementById('custom-review-count').value)
            };

            // Simulate GPT API call for demo purposes
            const mockRecommendations = [
                {
                    title: "Optimize Google Business Profile",
                    description: `For ${businessData.name}, focus on completing all profile sections including business hours, services, and high-quality photos.`,
                    category: "PROFILE",
                    impact: "HIGH",
                    effort: "MEDIUM"
                },
                {
                    title: "Encourage Customer Reviews",
                    description: `With ${businessData.reviews_count} reviews, implement a systematic review collection strategy to reach 50+ reviews.`,
                    category: "REVIEWS",
                    impact: "HIGH",
                    effort: "LOW"
                },
                {
                    title: "Local SEO Content Strategy",
                    description: `Create location-specific content targeting "${businessData.location}" to improve local search visibility.`,
                    category: "WEBSITE",
                    impact: "MEDIUM",
                    effort: "HIGH"
                }
            ];

            showResponse('custom-analysis-response', {
                business: businessData,
                recommendations: mockRecommendations,
                analysis_date: new Date().toISOString(),
                status: "success"
            });
        });

        // Bulk Collection functionality
        document.getElementById('bulk-collection-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData();
            formData.append('query', document.getElementById('bulk-query').value);
            formData.append('location', document.getElementById('bulk-location').value);
            formData.append('radius', document.getElementById('bulk-radius').value);
            formData.append('max_results', document.getElementById('bulk-max-results').value);
            formData.append('api_key', document.getElementById('bulk-api-key').value);

            const url = `${LOCALSEO_BASE}/collect/`;
            const result = await makeRequest(url, {
                method: 'POST',
                body: formData
            });

            showResponse('bulk-collection-response', result.data, !result.ok);
        });

        // Authentication functionality
        document.getElementById('check-auth-btn').addEventListener('click', async () => {
            const result = await makeRequest(`${BASE_URL}/api/accounts/check-auth/`);

            if (result.ok && result.data.authenticated) {
                updateStatus('auth-status', 'success', `Authenticated as ${result.data.user.email}`);
            } else {
                updateStatus('auth-status', 'error', 'Not Authenticated');
            }

            showResponse('auth-response', result.data, !result.ok);
        });

        document.getElementById('login-btn').addEventListener('click', () => {
            window.open(`${LOCALSEO_BASE}/login/`, '_blank');
        });

        document.getElementById('logout-btn').addEventListener('click', async () => {
            const result = await makeRequest(`${LOCALSEO_BASE}/logout/`, { method: 'POST' });
            updateStatus('auth-status', 'error', 'Not Authenticated');
            showResponse('auth-response', result.data, !result.ok);
        });

        // System Check functionality
        document.getElementById('system-check-btn').addEventListener('click', async () => {
            updateStatus('db-status', 'pending', 'Checking...');
            updateStatus('api-status', 'pending', 'Checking...');

            // Check database connection
            const dbResult = await makeRequest(`${LOCALSEO_BASE}/`);
            if (dbResult.ok) {
                updateStatus('db-status', 'success', 'Database Connected');
            } else {
                updateStatus('db-status', 'error', 'Database Error');
            }

            // Check API endpoints
            const endpoints = [
                `${LOCALSEO_BASE}/`,
                `${LOCALSEO_BASE}/gbp/connect/`,
                `${LOCALSEO_BASE}/gbp/dashboard/`
            ];

            let apiStatus = 'success';
            const endpointResults = {};

            for (const endpoint of endpoints) {
                const result = await makeRequest(endpoint);
                endpointResults[endpoint] = {
                    status: result.status,
                    ok: result.ok
                };

                if (!result.ok && result.status !== 302) { // 302 is redirect, which is OK
                    apiStatus = 'error';
                }
            }

            updateStatus('api-status', apiStatus, apiStatus === 'success' ? 'All Endpoints OK' : 'Some Endpoints Failed');

            showResponse('system-response', {
                database: dbResult.ok ? 'Connected' : 'Error',
                endpoints: endpointResults,
                timestamp: new Date().toISOString()
            });
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            // Check authentication status on load
            document.getElementById('check-auth-btn').click();

            // Run system check on load
            setTimeout(() => {
                document.getElementById('system-check-btn').click();
            }, 1000);
        });
    </script>
</body>
</html>