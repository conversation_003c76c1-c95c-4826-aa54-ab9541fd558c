import os
import logging
import requests
import json
from django.conf import settings

logger = logging.getLogger(__name__)

class GPTClient:
    """Client for interacting with OpenAI's GPT API"""
    
    def __init__(self, api_key=None):
        """Initialize the GPT API client"""
        self.api_key = api_key or os.environ.get('GPT_API_KEY')
        if not self.api_key:
            raise ValueError("GPT API key is required")
        
        self.api_url = "https://api.openai.com/v1/chat/completions"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def generate_recommendations(self, business_data, competitor_data=None, max_tokens=1000):
        """
        Generate business improvement recommendations using GPT
        
        Args:
            business_data (dict): Business data including profile, metrics, etc.
            competitor_data (list): List of competitor data (optional)
            max_tokens (int): Maximum number of tokens in the response
            
        Returns:
            list: List of recommendation dictionaries
        """
        try:
            # Prepare the prompt
            prompt = self._prepare_recommendation_prompt(business_data, competitor_data)
            
            # Make the API request
            response = self._make_api_request(prompt, max_tokens)
            
            # Parse the recommendations
            recommendations = self._parse_recommendations(response)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return []
    
    def _prepare_recommendation_prompt(self, business_data, competitor_data):
        """
        Prepare the prompt for generating recommendations
        
        Args:
            business_data (dict): Business data
            competitor_data (list): List of competitor data
            
        Returns:
            list: List of message dictionaries for the API request
        """
        # Extract key business information
        business_name = business_data.get('name', 'Unknown Business')
        business_type = business_data.get('business_type', '')
        categories = business_data.get('categories', [])
        rating = business_data.get('rating', 'N/A')
        reviews_count = business_data.get('reviews_count', 0)
        website = business_data.get('website', '')
        
        # Build the system prompt
        system_prompt = """You are an expert Local SEO consultant specializing in Google Business Profile optimization. 
Your task is to analyze the provided business data and generate specific, actionable recommendations to improve the business's local SEO performance.
Focus on practical advice that can be implemented quickly. Format your response as a JSON array of recommendation objects."""
        
        # Build the user prompt
        user_prompt = f"""Please analyze the following business data and provide recommendations for improving their Google Business Profile and local SEO:

BUSINESS INFORMATION:
- Name: {business_name}
- Type: {business_type}
- Categories: {', '.join(categories) if categories else 'Not specified'}
- Rating: {rating}
- Number of Reviews: {reviews_count}
- Website: {website}
"""
        
        # Add profile completeness information if available
        if 'profile_completeness' in business_data:
            completeness = business_data['profile_completeness']
            user_prompt += f"""
PROFILE COMPLETENESS:
- Photos: {completeness.get('photos', 'N/A')}
- Business Hours: {completeness.get('hours', 'N/A')}
- Description: {completeness.get('description', 'N/A')}
- Attributes: {completeness.get('attributes', 'N/A')}
- Services: {completeness.get('services', 'N/A')}
"""
        
        # Add performance metrics if available
        if 'metrics' in business_data:
            metrics = business_data['metrics']
            user_prompt += f"""
PERFORMANCE METRICS (Last 30 days):
- Views on Search: {metrics.get('views_search', 'N/A')}
- Views on Maps: {metrics.get('views_maps', 'N/A')}
- Website Clicks: {metrics.get('actions_website', 'N/A')}
- Phone Calls: {metrics.get('actions_phone', 'N/A')}
- Direction Requests: {metrics.get('actions_directions', 'N/A')}
"""
        
        # Add competitor information if available
        if competitor_data:
            user_prompt += "\nCOMPETITOR ANALYSIS:\n"
            for i, competitor in enumerate(competitor_data[:5], 1):
                user_prompt += f"""
Competitor {i}: {competitor.get('name', 'Unknown')}
- Rating: {competitor.get('rating', 'N/A')}
- Reviews: {competitor.get('reviews_count', 0)}
- Categories: {', '.join(competitor.get('categories', [])) if competitor.get('categories') else 'Not specified'}
- Website: {competitor.get('website', 'None')}
"""
        
        # Add instructions for the response format
        user_prompt += """
Please provide recommendations in the following JSON format:
[
  {
    "title": "Short, specific recommendation title",
    "description": "Detailed explanation of the recommendation with specific steps to implement",
    "category": "One of: PROFILE, REVIEWS, POSTS, HOURS, PHOTOS, WEBSITE, CATEGORIES, ATTRIBUTES, GENERAL",
    "impact": "HIGH, MEDIUM, or LOW",
    "effort": "HIGH, MEDIUM, or LOW"
  },
  ...
]

Provide at least 5 recommendations, focusing on the most impactful changes first.
"""
        
        # Create the messages array
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        return messages
    
    def _make_api_request(self, messages, max_tokens=1000):
        """
        Make a request to the GPT API
        
        Args:
            messages (list): List of message dictionaries
            max_tokens (int): Maximum number of tokens in the response
            
        Returns:
            dict: API response
        """
        try:
            payload = {
                "model": "gpt-4-turbo-preview",
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": 0.7,
                "response_format": {"type": "json_object"}
            }
            
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload
            )
            
            if response.status_code != 200:
                logger.error(f"GPT API error: {response.status_code} - {response.text}")
                return None
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Error making GPT API request: {str(e)}")
            return None
    
    def _parse_recommendations(self, response):
        """
        Parse the GPT API response into recommendation objects
        
        Args:
            response (dict): API response
            
        Returns:
            list: List of recommendation dictionaries
        """
        if not response or 'choices' not in response:
            return []
        
        try:
            # Extract the content from the response
            content = response['choices'][0]['message']['content']
            
            # Parse the JSON content
            recommendations = json.loads(content)
            
            # Ensure it's a list
            if isinstance(recommendations, dict) and 'recommendations' in recommendations:
                recommendations = recommendations['recommendations']
            
            # Validate and clean up each recommendation
            valid_recommendations = []
            for rec in recommendations:
                if isinstance(rec, dict) and 'title' in rec and 'description' in rec:
                    # Ensure required fields are present
                    valid_rec = {
                        'title': rec.get('title', ''),
                        'description': rec.get('description', ''),
                        'category': rec.get('category', 'GENERAL'),
                        'impact': rec.get('impact', 'MEDIUM'),
                        'effort': rec.get('effort', 'MEDIUM')
                    }
                    
                    # Normalize category, impact, and effort
                    valid_rec['category'] = valid_rec['category'].upper()
                    valid_rec['impact'] = valid_rec['impact'].upper()
                    valid_rec['effort'] = valid_rec['effort'].upper()
                    
                    valid_recommendations.append(valid_rec)
            
            return valid_recommendations
            
        except Exception as e:
            logger.error(f"Error parsing GPT recommendations: {str(e)}")
            return []
