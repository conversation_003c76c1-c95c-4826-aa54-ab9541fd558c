import os
import logging
import googlemaps
from datetime import datetime
from django.conf import settings

logger = logging.getLogger(__name__)

class GooglePlacesClient:
    """Client for interacting with Google Places API"""

    def __init__(self, api_key=None):
        """Initialize the Google Places API client"""
        self.api_key = api_key or os.environ.get('GOOGLE_PLACES_API_KEY')
        if not self.api_key:
            raise ValueError("Google Places API key is required")

        self.client = googlemaps.Client(key=self.api_key)

    def search_places(self, query, location=None, radius=None, type=None, language='en-AU'):
        """
        Search for places using the Google Places API

        Args:
            query (str): The search query
            location (tuple): Latitude and longitude tuple (e.g., (-33.8688, 151.2093) for Sydney)
            radius (int): Search radius in meters (max 50000)
            type (str): Type of place (e.g., 'restaurant', 'cafe')
            language (str): Language code (default: 'en-AU')

        Returns:
            list: List of place results
        """
        try:
            params = {
                'language': language
            }

            if location and radius:
                params['location'] = location
                params['radius'] = min(radius, 50000)  # Max radius is 50km

            if type:
                params['type'] = type

            # Log the API request parameters
            logger.info(f"Google Places API request - query: {query}, params: {params}")

            results = self.client.places(query, **params)

            # Log the API response summary
            if 'results' in results:
                logger.info(f"Google Places API returned {len(results['results'])} results")
            else:
                logger.warning(f"Google Places API returned no 'results' key. Response keys: {results.keys()}")

            return results.get('results', [])

        except Exception as e:
            logger.error(f"Error searching Google Places: {str(e)}")
            # Log more detailed error information if available
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return []

    def get_place_details(self, place_id, language='en-AU'):
        """
        Get detailed information about a place

        Args:
            place_id (str): The Google Place ID
            language (str): Language code (default: 'en-AU')

        Returns:
            dict: Place details
        """
        try:
            logger.info(f"Fetching details for place_id: {place_id}")

            # Use only valid fields as per the API documentation
            # The error message showed the valid fields
            fields = [
                'name', 'place_id', 'formatted_address', 'formatted_phone_number',
                'international_phone_number', 'website', 'rating', 'user_ratings_total',
                'geometry', 'type', 'opening_hours', 'review', 'reviews',
                'address_component', 'business_status', 'url'
            ]

            logger.info(f"Requesting fields: {', '.join(fields)}")

            result = self.client.place(
                place_id=place_id,
                language=language,
                fields=fields
            )

            if 'result' in result:
                place_result = result['result']
                logger.info(f"Successfully retrieved details for: {place_result.get('name', 'Unknown')}")
                logger.info(f"Fields received: {', '.join(place_result.keys())}")

                # Log specific important fields
                if 'formatted_address' in place_result:
                    logger.info(f"Address: {place_result['formatted_address']}")
                if 'website' in place_result:
                    logger.info(f"Website: {place_result['website']}")
                if 'formatted_phone_number' in place_result:
                    logger.info(f"Phone: {place_result['formatted_phone_number']}")

                return place_result
            else:
                logger.warning(f"No 'result' key in response for place_id: {place_id}")
                logger.warning(f"Response keys: {', '.join(result.keys())}")
                return {}

        except Exception as e:
            logger.error(f"Error getting place details: {str(e)}")
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return {}

    def get_place_photos(self, photo_references, max_width=800):
        """
        Get photo URLs for place photos

        Args:
            photo_references (list): List of photo reference strings
            max_width (int): Maximum width of the photos

        Returns:
            list: List of photo URLs
        """
        photo_urls = []

        for ref in photo_references:
            try:
                photo_url = f"https://maps.googleapis.com/maps/api/place/photo?maxwidth={max_width}&photoreference={ref}&key={self.api_key}"
                photo_urls.append(photo_url)
            except Exception as e:
                logger.error(f"Error getting place photo: {str(e)}")

        return photo_urls

    def nearby_search(self, location, radius=5000, type=None, keyword=None, language='en-AU'):
        """
        Search for places near a location

        Args:
            location (tuple): Latitude and longitude tuple
            radius (int): Search radius in meters (max 50000)
            type (str): Type of place (e.g., 'restaurant', 'cafe')
            keyword (str): Keyword to search for
            language (str): Language code (default: 'en-AU')

        Returns:
            list: List of place results
        """
        try:
            params = {
                'location': location,
                'radius': min(radius, 50000),  # Max radius is 50km
                'language': language
            }

            if type:
                params['type'] = type

            if keyword:
                params['keyword'] = keyword

            # Log the API request parameters
            logger.info(f"Google Places Nearby Search API request - location: {location}, radius: {radius}, params: {params}")

            results = self.client.places_nearby(**params)

            # Log the API response summary
            if 'results' in results:
                logger.info(f"Google Places Nearby Search API returned {len(results['results'])} results")
                if len(results['results']) > 0:
                    # Log the first result as an example
                    first_result = results['results'][0]
                    logger.info(f"Example result: {first_result.get('name')} - {first_result.get('vicinity')}")
            else:
                logger.warning(f"Google Places Nearby Search API returned no 'results' key. Response keys: {results.keys()}")

            return results.get('results', [])

        except Exception as e:
            logger.error(f"Error in nearby search: {str(e)}")
            # Log more detailed error information if available
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return []
