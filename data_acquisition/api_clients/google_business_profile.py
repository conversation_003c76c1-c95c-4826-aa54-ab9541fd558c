import os
import logging
import requests
import json
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from django.conf import settings

logger = logging.getLogger(__name__)

class GoogleBusinessProfileClient:
    """Client for interacting with Google Business Profile API"""

    # Define the required scopes for Google Business Profile API
    SCOPES = [
        'openid',
        'https://www.googleapis.com/auth/business.manage',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/userinfo.profile'
    ]

    def __init__(self, credentials=None):
        """Initialize the Google Business Profile API client"""
        self.credentials = credentials
        self.client_id = os.environ.get('GOOGLE_OAUTH_CLIENT_ID')
        self.client_secret = os.environ.get('GOOGLE_OAUTH_CLIENT_SECRET')

        # Use a fixed redirect URI that matches what's configured in the Google Cloud Console
        self.redirect_uri = "http://127.0.0.1:8000/oauth2callback"
        logger.info(f"Using fixed redirect URI: {self.redirect_uri}")

        if not self.client_id or not self.client_secret:
            logger.error("Google OAuth client ID and secret are required")
            raise ValueError("Google OAuth client ID and secret are required")

    def get_auth_url(self):
        """
        Get the authorization URL for OAuth 2.0 flow

        Returns:
            tuple: (auth_url, state) - The URL to redirect the user to and the state parameter
        """
        try:
            # Log the current redirect URI
            logger.info(f"Using redirect URI for auth URL: {self.redirect_uri}")

            # Create the flow using the client secrets
            client_config = {
                "web": {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            }

            # Log the client config
            logger.info(f"Client config: {client_config}")

            flow = Flow.from_client_config(
                client_config,
                scopes=self.SCOPES
            )

            # Set the redirect URI
            flow.redirect_uri = self.redirect_uri

            # Generate the authorization URL
            auth_url, state = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true',
                prompt='consent'
            )

            # Log the generated auth URL
            logger.info(f"Generated auth URL: {auth_url}")
            logger.info(f"Redirect URI in auth URL: {flow.redirect_uri}")

            # Parse the auth URL to extract the redirect_uri parameter
            import urllib.parse
            parsed_url = urllib.parse.urlparse(auth_url)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            redirect_uri_param = query_params.get('redirect_uri', [''])[0]
            logger.info(f"Redirect URI parameter in auth URL: {redirect_uri_param}")

            return auth_url, state

        except Exception as e:
            logger.error(f"Error generating auth URL: {str(e)}")
            raise

    def get_credentials_from_code(self, code, state):
        """
        Exchange authorization code for credentials

        Args:
            code (str): The authorization code from the callback
            state (str): The state parameter from the callback

        Returns:
            Credentials: The OAuth 2.0 credentials
        """
        try:
            # Log the current scopes
            logger.info(f"Using scopes for token exchange: {self.SCOPES}")

            # Create the flow using the client secrets
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "redirect_uris": [self.redirect_uri]
                    }
                },
                scopes=self.SCOPES,
                state=state
            )

            # Set the redirect URI
            flow.redirect_uri = self.redirect_uri
            logger.info(f"Using redirect URI for token exchange: {self.redirect_uri}")

            # Exchange the authorization code for credentials
            try:
                flow.fetch_token(code=code)
            except Exception as token_error:
                # If there's a scope change error, try to handle it
                if "Scope has changed" in str(token_error):
                    logger.warning(f"Scope change error: {str(token_error)}")

                    # Extract the expected scopes from the error message
                    error_msg = str(token_error)
                    if "to" in error_msg:
                        expected_scopes_str = error_msg.split("to")[1].strip().strip('"')
                        expected_scopes = expected_scopes_str.split()

                        logger.info(f"Extracted expected scopes: {expected_scopes}")

                        # Create a new flow with the expected scopes
                        flow = Flow.from_client_config(
                            {
                                "web": {
                                    "client_id": self.client_id,
                                    "client_secret": self.client_secret,
                                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                                    "token_uri": "https://oauth2.googleapis.com/token",
                                    "redirect_uris": [self.redirect_uri]
                                }
                            },
                            scopes=expected_scopes,
                            state=state
                        )

                        # Set the redirect URI
                        flow.redirect_uri = self.redirect_uri

                        # Try again with the expected scopes
                        flow.fetch_token(code=code)
                    else:
                        raise
                else:
                    raise

            # Return the credentials
            return flow.credentials

        except Exception as e:
            logger.error(f"Error exchanging code for credentials: {str(e)}")
            raise

    def get_user_info(self):
        """
        Get information about the authenticated user

        Returns:
            dict: User information
        """
        if not self.credentials:
            logger.error("No credentials available")
            raise ValueError("No credentials available")

        try:
            # Build the service
            service = build('oauth2', 'v2', credentials=self.credentials)

            # Get user info
            user_info = service.userinfo().get().execute()

            return user_info

        except HttpError as e:
            logger.error(f"Error getting user info: {str(e)}")
            raise

    def get_accounts(self):
        """
        Get the list of accounts the user has access to

        Returns:
            list: List of account resources

        Raises:
            ValueError: If no credentials are available
            HttpError: If there's an error with the API request
            Exception: For other errors

        Returns a tuple of (accounts, error_type, error_message) where:
            - accounts is a list of account resources
            - error_type is None if no error, or one of 'rate_limit', 'permission', 'not_found', 'api_disabled', 'unknown'
            - error_message is None if no error, or a string with the error message
        """
        if not self.credentials:
            logger.error("No credentials available")
            raise ValueError("No credentials available")

        try:
            # Build the service
            service = build('mybusinessaccountmanagement', 'v1', credentials=self.credentials)

            # Get accounts
            accounts = service.accounts().list().execute()

            # Get the accounts list
            account_list = accounts.get('accounts', [])

            # If the list is empty, it could be because the user doesn't have any accounts
            # or because there's an issue with the API
            if not account_list:
                logger.warning("No accounts found. This could be because the user doesn't have any Google Business Profile accounts.")

            return account_list, None, None

        except HttpError as e:
            error_message = str(e)
            error_code = getattr(e, 'status_code', None)
            logger.error(f"HTTP Error getting accounts: {error_message}, code: {error_code}")

            # Check if it's a rate limit error
            if "Quota exceeded" in error_message or "RATE_LIMIT_EXCEEDED" in error_message:
                logger.warning("Rate limit exceeded. You need to enable the API and set up proper quota limits.")
                error_type = 'rate_limit'
                error_msg = "API rate limit exceeded. Please try again later or request a quota increase."
            # Check if it's a permission error
            elif error_code == 403 or "permission" in error_message.lower() or "access" in error_message.lower():
                logger.warning("Permission error. The user may not have access to the Google Business Profile API.")
                error_type = 'permission'
                error_msg = "You don't have permission to access the Google Business Profile API. Please check your account permissions."
            # Check if it's a not found error
            elif error_code == 404 or "not found" in error_message.lower():
                logger.warning("Resource not found. The user may not have any Google Business Profile accounts.")
                error_type = 'not_found'
                error_msg = "No Google Business Profile accounts found for this user."
            # Check if the API is disabled
            elif "API has not been used" in error_message or "API not enabled" in error_message:
                logger.warning("API not enabled. The Google Business Profile API may not be enabled for this project.")
                error_type = 'api_disabled'
                error_msg = "The Google Business Profile API is not enabled for this project. Please enable it in the Google Cloud Console."
            # Other errors
            else:
                logger.error(f"Unknown API error: {error_message}")
                error_type = 'unknown'
                error_msg = f"An error occurred while accessing the Google Business Profile API: {error_message}"

            # Return an empty list and the error information
            return [], error_type, error_msg

        except Exception as e:
            logger.error(f"Unexpected error getting accounts: {str(e)}")
            return [], 'unknown', f"An unexpected error occurred: {str(e)}"

    def get_locations(self, account_name):
        """
        Get the list of locations for an account

        Args:
            account_name (str): The resource name of the account

        Returns:
            tuple: (locations, error_type, error_message) where:
                - locations is a list of location resources
                - error_type is None if no error, or one of 'rate_limit', 'permission', 'not_found', 'api_disabled', 'unknown'
                - error_message is None if no error, or a string with the error message
        """
        if not self.credentials:
            logger.error("No credentials available")
            raise ValueError("No credentials available")

        try:
            # Build the service
            service = build('mybusinessbusinessinformation', 'v1', credentials=self.credentials)

            # Get locations
            locations = service.accounts().locations().list(
                parent=account_name,
                readMask='name,title,storefrontAddress,websiteUri,regularHours,specialHours,serviceArea,labels,profile,relationshipData,moreHours'
            ).execute()

            # Get the locations list
            location_list = locations.get('locations', [])

            # If the list is empty, it could be because the account doesn't have any locations
            if not location_list:
                logger.warning(f"No locations found for account {account_name}. This could be because the account doesn't have any locations.")

            return location_list, None, None

        except HttpError as e:
            error_message = str(e)
            error_code = getattr(e, 'status_code', None)
            logger.error(f"HTTP Error getting locations: {error_message}, code: {error_code}")

            # Check if it's a rate limit error
            if "Quota exceeded" in error_message or "RATE_LIMIT_EXCEEDED" in error_message:
                logger.warning("Rate limit exceeded. You need to enable the API and set up proper quota limits.")
                error_type = 'rate_limit'
                error_msg = "API rate limit exceeded. Please try again later or request a quota increase."
            # Check if it's a permission error
            elif error_code == 403 or "permission" in error_message.lower() or "access" in error_message.lower():
                logger.warning("Permission error. The user may not have access to the locations for this account.")
                error_type = 'permission'
                error_msg = "You don't have permission to access the locations for this account. Please check your account permissions."
            # Check if it's a not found error
            elif error_code == 404 or "not found" in error_message.lower():
                logger.warning(f"Account {account_name} not found.")
                error_type = 'not_found'
                error_msg = f"Account {account_name} not found."
            # Check if the API is disabled
            elif "API has not been used" in error_message or "API not enabled" in error_message:
                logger.warning("API not enabled. The Google Business Profile API may not be enabled for this project.")
                error_type = 'api_disabled'
                error_msg = "The Google Business Profile API is not enabled for this project. Please enable it in the Google Cloud Console."
            # Other errors
            else:
                logger.error(f"Unknown API error: {error_message}")
                error_type = 'unknown'
                error_msg = f"An error occurred while accessing the Google Business Profile API: {error_message}"

            # Return an empty list and the error information
            return [], error_type, error_msg

        except Exception as e:
            logger.error(f"Unexpected error getting locations: {str(e)}")
            return [], 'unknown', f"An unexpected error occurred: {str(e)}"

    def get_location_metrics(self, location_name):
        """
        Get metrics for a location

        Args:
            location_name (str): The resource name of the location

        Returns:
            dict: Location metrics
        """
        if not self.credentials:
            logger.error("No credentials available")
            raise ValueError("No credentials available")

        try:
            # Build the service
            service = build('mybusinessbusinessinformation', 'v1', credentials=self.credentials)

            # Get location
            location = service.locations().get(name=location_name).execute()

            try:
                # Build the performance service
                performance_service = build('mybusinessperformance', 'v1', credentials=self.credentials)

                # Get metrics
                metrics = performance_service.locations().getDailyMetricsTimeSeries(
                    name=location_name,
                    dailyMetrics=['QUERIES_DIRECT', 'QUERIES_INDIRECT', 'VIEWS_MAPS', 'VIEWS_SEARCH', 'ACTIONS_WEBSITE', 'ACTIONS_PHONE', 'ACTIONS_DRIVING_DIRECTIONS']
                ).execute()

                return {
                    'location': location,
                    'metrics': metrics
                }
            except HttpError as metrics_error:
                error_message = str(metrics_error)
                logger.error(f"Error getting metrics: {error_message}")

                # Check if it's a rate limit error
                if "Quota exceeded" in error_message or "RATE_LIMIT_EXCEEDED" in error_message:
                    logger.warning("Rate limit exceeded for metrics. Returning location data only.")
                    # Return location data only
                    return {
                        'location': location,
                        'metrics': {}
                    }

                # For other errors, still return the location data
                return {
                    'location': location,
                    'metrics': {}
                }

        except HttpError as e:
            error_message = str(e)
            logger.error(f"Error getting location metrics: {error_message}")

            # Check if it's a rate limit error
            if "Quota exceeded" in error_message or "RATE_LIMIT_EXCEEDED" in error_message:
                logger.warning("Rate limit exceeded. You need to enable the API and set up proper quota limits.")
                # Return empty data instead of raising an error
                return {
                    'location': {},
                    'metrics': {}
                }

            raise

    def get_reviews(self, location_name):
        """
        Get reviews for a location

        Args:
            location_name (str): The resource name of the location

        Returns:
            list: List of reviews
        """
        if not self.credentials:
            logger.error("No credentials available")
            raise ValueError("No credentials available")

        try:
            # Build the service
            service = build('mybusinessplaceactions', 'v1', credentials=self.credentials)

            # Get reviews
            reviews = service.locations().reviews().list(
                parent=location_name,
                pageSize=50
            ).execute()

            return reviews.get('reviews', [])

        except HttpError as e:
            error_message = str(e)
            logger.error(f"Error getting reviews: {error_message}")

            # Check if it's a rate limit error
            if "Quota exceeded" in error_message or "RATE_LIMIT_EXCEEDED" in error_message:
                logger.warning("Rate limit exceeded. You need to enable the API and set up proper quota limits.")
                # Return an empty list instead of raising an error
                return []

            raise
