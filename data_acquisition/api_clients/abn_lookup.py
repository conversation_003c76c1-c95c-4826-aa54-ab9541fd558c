import os
import logging
import requests
import xml.etree.ElementTree as ET
from django.conf import settings

logger = logging.getLogger(__name__)

class ABNLookupClient:
    """Client for interacting with the Australian Business Register ABN Lookup API"""
    
    # ABN Lookup SOAP API endpoint
    BASE_URL = "https://abr.business.gov.au/abrxmlsearch/ABRXMLSearch.asmx"
    
    def __init__(self, guid=None):
        """Initialize the ABN Lookup API client"""
        self.guid = guid or os.environ.get('ABN_LOOKUP_API_KEY')
        if not self.guid:
            raise ValueError("ABN Lookup GUID is required")
    
    def search_by_abn(self, abn, history=False):
        """
        Search for a business by ABN
        
        Args:
            abn (str): The ABN to search for
            history (bool): Whether to include historical details
            
        Returns:
            dict: Business details
        """
        try:
            # Remove spaces and validate ABN format
            abn = abn.replace(' ', '')
            if not self._validate_abn_format(abn):
                logger.error(f"Invalid ABN format: {abn}")
                return {}
            
            # Build the request URL
            url = f"{self.BASE_URL}/SearchByABNv202001"
            
            # Build the SOAP request
            headers = {'Content-Type': 'text/xml; charset=utf-8'}
            
            # Create SOAP envelope
            soap_request = f"""<?xml version="1.0" encoding="utf-8"?>
            <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
              <soap12:Body>
                <SearchByABNv202001 xmlns="http://abr.business.gov.au/ABRXMLSearch/">
                  <searchString>{abn}</searchString>
                  <includeHistoricalDetails>{'Y' if history else 'N'}</includeHistoricalDetails>
                  <authenticationGuid>{self.guid}</authenticationGuid>
                </SearchByABNv202001>
              </soap12:Body>
            </soap12:Envelope>"""
            
            # Make the request
            response = requests.post(url, headers=headers, data=soap_request)
            
            if response.status_code != 200:
                logger.error(f"ABN Lookup API error: {response.status_code} - {response.text}")
                return {}
            
            # Parse the XML response
            return self._parse_abn_response(response.text)
        
        except Exception as e:
            logger.error(f"Error searching ABN: {str(e)}")
            return {}
    
    def search_by_name(self, name, state=None, postcode=None, legal_name=True, trading_name=True, max_results=10):
        """
        Search for businesses by name
        
        Args:
            name (str): The business name to search for
            state (str): State code (e.g., 'NSW', 'VIC')
            postcode (str): Postcode
            legal_name (bool): Whether to search legal names
            trading_name (bool): Whether to search trading names
            max_results (int): Maximum number of results to return
            
        Returns:
            list: List of matching businesses
        """
        try:
            # Build the request URL
            url = f"{self.BASE_URL}/ABRSearchByNameAdvancedv202001"
            
            # Build the SOAP request
            headers = {'Content-Type': 'text/xml; charset=utf-8'}
            
            # Create name search options
            name_type = ""
            if legal_name and trading_name:
                name_type = "ALL"
            elif legal_name:
                name_type = "LEGAL"
            elif trading_name:
                name_type = "TRADING"
            else:
                name_type = "ALL"  # Default to all if none specified
            
            # Create SOAP envelope
            soap_request = f"""<?xml version="1.0" encoding="utf-8"?>
            <soap12:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
              <soap12:Body>
                <ABRSearchByNameAdvancedv202001 xmlns="http://abr.business.gov.au/ABRXMLSearch/">
                  <name>{name}</name>
                  <legalName>{legal_name}</legalName>
                  <tradingName>{trading_name}</tradingName>
                  <NSW>{'Y' if state == 'NSW' else 'N'}</NSW>
                  <SA>{'Y' if state == 'SA' else 'N'}</SA>
                  <ACT>{'Y' if state == 'ACT' else 'N'}</ACT>
                  <VIC>{'Y' if state == 'VIC' else 'N'}</VIC>
                  <WA>{'Y' if state == 'WA' else 'N'}</WA>
                  <NT>{'Y' if state == 'NT' else 'N'}</NT>
                  <QLD>{'Y' if state == 'QLD' else 'N'}</QLD>
                  <TAS>{'Y' if state == 'TAS' else 'N'}</TAS>
                  <postcode>{postcode if postcode else ''}</postcode>
                  <authenticationGuid>{self.guid}</authenticationGuid>
                  <maxSearchResults>{max_results}</maxSearchResults>
                </ABRSearchByNameAdvancedv202001>
              </soap12:Body>
            </soap12:Envelope>"""
            
            # Make the request
            response = requests.post(url, headers=headers, data=soap_request)
            
            if response.status_code != 200:
                logger.error(f"ABN Lookup API error: {response.status_code} - {response.text}")
                return []
            
            # Parse the XML response
            return self._parse_name_search_response(response.text)
        
        except Exception as e:
            logger.error(f"Error searching by name: {str(e)}")
            return []
    
    def _validate_abn_format(self, abn):
        """
        Validate ABN format (11 digits)
        
        Args:
            abn (str): The ABN to validate
            
        Returns:
            bool: Whether the ABN format is valid
        """
        return len(abn) == 11 and abn.isdigit()
    
    def _parse_abn_response(self, xml_response):
        """
        Parse the XML response from the ABN Lookup API
        
        Args:
            xml_response (str): The XML response
            
        Returns:
            dict: Parsed business details
        """
        try:
            # Parse the XML
            root = ET.fromstring(xml_response)
            
            # Find the business entity element
            ns = {'ns': 'http://abr.business.gov.au/ABRXMLSearch/'}
            business_entity = root.find('.//ns:businessEntity', ns)
            
            if business_entity is None:
                return {}
            
            # Extract business details
            abn = business_entity.find('.//ns:ABN', ns)
            entity_name = business_entity.find('.//ns:entityName', ns)
            trading_name = business_entity.find('.//ns:mainTradingName', ns)
            business_location = business_entity.find('.//ns:mainBusinessPhysicalAddress', ns)
            
            # Build the result dictionary
            result = {
                'abn': abn.find('.//ns:identifierValue', ns).text if abn is not None else None,
                'entity_type': business_entity.find('.//ns:entityType', ns).find('.//ns:entityDescription', ns).text if business_entity.find('.//ns:entityType', ns) is not None else None,
                'status': business_entity.find('.//ns:entityStatus', ns).find('.//ns:entityStatusCode', ns).text if business_entity.find('.//ns:entityStatus', ns) is not None else None,
                'name': entity_name.text if entity_name is not None else None,
                'trading_name': trading_name.text if trading_name is not None else None,
            }
            
            # Add address if available
            if business_location is not None:
                result['address'] = {
                    'state': business_location.find('.//ns:stateCode', ns).text if business_location.find('.//ns:stateCode', ns) is not None else None,
                    'postcode': business_location.find('.//ns:postcode', ns).text if business_location.find('.//ns:postcode', ns) is not None else None,
                }
            
            return result
        
        except Exception as e:
            logger.error(f"Error parsing ABN response: {str(e)}")
            return {}
    
    def _parse_name_search_response(self, xml_response):
        """
        Parse the XML response from the ABN name search
        
        Args:
            xml_response (str): The XML response
            
        Returns:
            list: List of matching businesses
        """
        try:
            # Parse the XML
            root = ET.fromstring(xml_response)
            
            # Find all business entities
            ns = {'ns': 'http://abr.business.gov.au/ABRXMLSearch/'}
            search_results = root.findall('.//ns:searchResultsRecord', ns)
            
            results = []
            for result in search_results:
                abn = result.find('.//ns:ABN', ns)
                entity_name = result.find('.//ns:organisationName', ns) or result.find('.//ns:legalName', ns)
                
                business = {
                    'abn': abn.find('.//ns:identifierValue', ns).text if abn is not None else None,
                    'name': entity_name.text if entity_name is not None else None,
                    'state': result.find('.//ns:mainState', ns).text if result.find('.//ns:mainState', ns) is not None else None,
                    'postcode': result.find('.//ns:mainPostcode', ns).text if result.find('.//ns:mainPostcode', ns) is not None else None,
                }
                
                results.append(business)
            
            return results
        
        except Exception as e:
            logger.error(f"Error parsing name search response: {str(e)}")
            return []
