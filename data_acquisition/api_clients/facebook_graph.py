import os
import logging
import requests
import facebook
from django.conf import settings

logger = logging.getLogger(__name__)

class FacebookGraphClient:
    """Client for interacting with Facebook Graph API"""
    
    def __init__(self, access_token=None):
        """Initialize the Facebook Graph API client"""
        self.access_token = access_token or os.environ.get('FACEBOOK_GRAPH_API_KEY')
        if not self.access_token:
            raise ValueError("Facebook Graph API access token is required")
        
        self.graph = facebook.GraphAPI(access_token=self.access_token, version="13.0")
    
    def get_business_page(self, page_id):
        """
        Get information about a business page
        
        Args:
            page_id (str): The Facebook Page ID or username
            
        Returns:
            dict: Page information
        """
        try:
            fields = [
                'id', 'name', 'about', 'category', 'category_list', 'contact_address',
                'current_location', 'description', 'emails', 'engagement', 'fan_count',
                'link', 'location', 'phone', 'rating_count', 'website', 'whatsapp_number',
                'overall_star_rating'
            ]
            
            page_info = self.graph.get_object(
                id=page_id,
                fields=','.join(fields)
            )
            return page_info
        
        except Exception as e:
            logger.error(f"Error getting Facebook page info: {str(e)}")
            return {}
    
    def get_page_posts(self, page_id, limit=10):
        """
        Get recent posts from a business page
        
        Args:
            page_id (str): The Facebook Page ID or username
            limit (int): Maximum number of posts to retrieve
            
        Returns:
            list: List of posts
        """
        try:
            posts = self.graph.get_connections(
                id=page_id,
                connection_name='posts',
                fields='id,message,created_time,permalink_url,full_picture,reactions.summary(true),comments.summary(true),shares',
                limit=limit
            )
            return posts.get('data', [])
        
        except Exception as e:
            logger.error(f"Error getting Facebook page posts: {str(e)}")
            return []
    
    def search_pages(self, query, location=None, categories=None, limit=10):
        """
        Search for business pages
        
        Args:
            query (str): Search query
            location (str): Location to search in
            categories (list): List of categories to filter by
            limit (int): Maximum number of results
            
        Returns:
            list: List of page results
        """
        try:
            # Build the search query
            search_query = query
            if location:
                search_query += f" {location}"
                
            # Perform the search
            search_results = self.graph.request(
                'search',
                {
                    'q': search_query,
                    'type': 'page',
                    'fields': 'id,name,category,location,link',
                    'limit': limit
                }
            )
            
            results = search_results.get('data', [])
            
            # Filter by categories if specified
            if categories and results:
                filtered_results = []
                for page in results:
                    if 'category' in page and page['category'] in categories:
                        filtered_results.append(page)
                return filtered_results
            
            return results
        
        except Exception as e:
            logger.error(f"Error searching Facebook pages: {str(e)}")
            return []
    
    def get_page_reviews(self, page_id, limit=25):
        """
        Get reviews for a business page
        
        Args:
            page_id (str): The Facebook Page ID
            limit (int): Maximum number of reviews to retrieve
            
        Returns:
            list: List of reviews
        """
        try:
            # Note: This requires a Page Access Token with manage_pages permission
            reviews = self.graph.get_connections(
                id=page_id,
                connection_name='ratings',
                fields='reviewer{id,name},created_time,rating,review_text,recommendation_type',
                limit=limit
            )
            return reviews.get('data', [])
        
        except Exception as e:
            logger.error(f"Error getting Facebook page reviews: {str(e)}")
            return []
