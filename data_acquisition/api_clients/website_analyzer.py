import logging
import requests
import json
import os
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class WebsiteAnalyzer:
    """Client for analyzing website performance using Google PageSpeed Insights API"""
    
    PAGESPEED_API_URL = "https://www.googleapis.com/pagespeedonline/v5/runPagespeed"
    
    def __init__(self, api_key=None):
        """Initialize the Website Analyzer"""
        self.api_key = api_key or os.environ.get('GOOGLE_PAGESPEED_API_KEY')
        if not self.api_key:
            logger.warning("Google PageSpeed API key not provided. Some features may be limited.")
    
    def analyze_website(self, url, strategy='mobile'):
        """
        Analyze a website using Google PageSpeed Insights
        
        Args:
            url (str): The website URL to analyze
            strategy (str): The analysis strategy ('mobile' or 'desktop')
            
        Returns:
            dict: Website performance metrics
        """
        try:
            # Validate URL format
            if not self._validate_url(url):
                logger.error(f"Invalid URL format: {url}")
                return {}
            
            # Build the request parameters
            params = {
                'url': url,
                'strategy': strategy,
                'category': 'performance',
                'locale': 'en_AU'
            }
            
            # Add API key if available
            if self.api_key:
                params['key'] = self.api_key
            
            # Make the request
            response = requests.get(self.PAGESPEED_API_URL, params=params)
            
            if response.status_code != 200:
                logger.error(f"PageSpeed API error: {response.status_code} - {response.text}")
                return {}
            
            # Parse the response
            data = response.json()
            
            # Extract the metrics
            metrics = {}
            
            if 'lighthouseResult' in data:
                lighthouse = data['lighthouseResult']
                
                # Get overall score
                if 'categories' in lighthouse and 'performance' in lighthouse['categories']:
                    metrics['performance_score'] = int(lighthouse['categories']['performance']['score'] * 100)
                
                # Get specific metrics
                if 'audits' in lighthouse:
                    audits = lighthouse['audits']
                    
                    # First Contentful Paint
                    if 'first-contentful-paint' in audits:
                        metrics['first_contentful_paint'] = audits['first-contentful-paint']['numericValue']
                    
                    # Speed Index
                    if 'speed-index' in audits:
                        metrics['speed_index'] = audits['speed-index']['numericValue']
                    
                    # Largest Contentful Paint
                    if 'largest-contentful-paint' in audits:
                        metrics['largest_contentful_paint'] = audits['largest-contentful-paint']['numericValue']
                    
                    # Time to Interactive
                    if 'interactive' in audits:
                        metrics['time_to_interactive'] = audits['interactive']['numericValue']
                    
                    # Total Blocking Time
                    if 'total-blocking-time' in audits:
                        metrics['total_blocking_time'] = audits['total-blocking-time']['numericValue']
                    
                    # Cumulative Layout Shift
                    if 'cumulative-layout-shift' in audits:
                        metrics['cumulative_layout_shift'] = audits['cumulative-layout-shift']['numericValue']
                    
                    # Mobile Friendliness
                    if 'viewport' in audits:
                        metrics['mobile_friendly'] = audits['viewport']['score'] == 1
            
            return metrics
        
        except Exception as e:
            logger.error(f"Error analyzing website: {str(e)}")
            return {}
    
    def check_mobile_friendliness(self, url):
        """
        Check if a website is mobile-friendly
        
        Args:
            url (str): The website URL to check
            
        Returns:
            dict: Mobile-friendliness metrics
        """
        try:
            # Use the mobile strategy for analysis
            results = self.analyze_website(url, strategy='mobile')
            
            # Extract mobile-specific metrics
            mobile_metrics = {
                'mobile_friendly_score': results.get('performance_score', 0),
                'mobile_friendly': results.get('mobile_friendly', False),
                'cumulative_layout_shift': results.get('cumulative_layout_shift', 0),
            }
            
            return mobile_metrics
        
        except Exception as e:
            logger.error(f"Error checking mobile-friendliness: {str(e)}")
            return {}
    
    def _validate_url(self, url):
        """
        Validate URL format
        
        Args:
            url (str): The URL to validate
            
        Returns:
            bool: Whether the URL format is valid
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
