import logging
import requests
from bs4 import BeautifulSoup
import time
import random

logger = logging.getLogger(__name__)

class YellowPagesClient:
    """Client for scraping data from Yellow Pages Australia"""
    
    BASE_URL = "https://www.yellowpages.com.au"
    
    def __init__(self):
        """Initialize the Yellow Pages scraper"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
        })
    
    def search_businesses(self, what, where, page=1):
        """
        Search for businesses on Yellow Pages
        
        Args:
            what (str): What to search for (business type, category)
            where (str): Where to search (location, suburb, postcode)
            page (int): Page number for pagination
            
        Returns:
            list: List of business results
        """
        try:
            # Build the search URL
            url = f"{self.BASE_URL}/search/listings"
            params = {
                'what': what,
                'where': where,
                'page': page
            }
            
            # Make the request
            response = self.session.get(url, params=params)
            
            if response.status_code != 200:
                logger.error(f"Yellow Pages search error: {response.status_code}")
                return []
            
            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find all business listings
            listings = soup.select('.listing-data')
            
            results = []
            for listing in listings:
                try:
                    # Extract business name
                    name_elem = listing.select_one('.listing-name a')
                    name = name_elem.text.strip() if name_elem else None
                    
                    # Extract business URL
                    url = self.BASE_URL + name_elem['href'] if name_elem and 'href' in name_elem.attrs else None
                    
                    # Extract phone number
                    phone_elem = listing.select_one('.contact-phone')
                    phone = phone_elem.text.strip() if phone_elem else None
                    
                    # Extract address
                    address_elem = listing.select_one('.listing-address')
                    address = address_elem.text.strip() if address_elem else None
                    
                    # Extract website
                    website_elem = listing.select_one('.contact-url')
                    website = website_elem.text.strip() if website_elem else None
                    
                    # Create business object
                    business = {
                        'name': name,
                        'url': url,
                        'phone': phone,
                        'address': address,
                        'website': website,
                        'source': 'Yellow Pages'
                    }
                    
                    results.append(business)
                
                except Exception as e:
                    logger.error(f"Error parsing Yellow Pages listing: {str(e)}")
                    continue
            
            return results
        
        except Exception as e:
            logger.error(f"Error searching Yellow Pages: {str(e)}")
            return []
    
    def get_business_details(self, url):
        """
        Get detailed information about a business from its Yellow Pages listing
        
        Args:
            url (str): The Yellow Pages listing URL
            
        Returns:
            dict: Business details
        """
        try:
            # Add a small delay to avoid rate limiting
            time.sleep(random.uniform(1, 3))
            
            # Make the request
            response = self.session.get(url)
            
            if response.status_code != 200:
                logger.error(f"Yellow Pages details error: {response.status_code}")
                return {}
            
            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract business details
            name_elem = soup.select_one('h1.listing-name')
            name = name_elem.text.strip() if name_elem else None
            
            # Extract categories
            categories = []
            category_elems = soup.select('.categories-list a')
            for cat in category_elems:
                categories.append(cat.text.strip())
            
            # Extract address
            address_elem = soup.select_one('.listing-address')
            address = address_elem.text.strip() if address_elem else None
            
            # Extract phone
            phone_elem = soup.select_one('.contact-phone')
            phone = phone_elem.text.strip() if phone_elem else None
            
            # Extract website
            website_elem = soup.select_one('.contact-url')
            website = website_elem.text.strip() if website_elem else None
            
            # Extract opening hours
            hours = {}
            hours_elems = soup.select('.opening-hours-list li')
            for hour in hours_elems:
                day = hour.select_one('.day').text.strip() if hour.select_one('.day') else None
                time_range = hour.select_one('.times').text.strip() if hour.select_one('.times') else None
                if day and time_range:
                    hours[day] = time_range
            
            # Extract description
            description_elem = soup.select_one('.listing-description')
            description = description_elem.text.strip() if description_elem else None
            
            # Create business details object
            business_details = {
                'name': name,
                'categories': categories,
                'address': address,
                'phone': phone,
                'website': website,
                'opening_hours': hours,
                'description': description,
                'source': 'Yellow Pages'
            }
            
            return business_details
        
        except Exception as e:
            logger.error(f"Error getting Yellow Pages business details: {str(e)}")
            return {}
