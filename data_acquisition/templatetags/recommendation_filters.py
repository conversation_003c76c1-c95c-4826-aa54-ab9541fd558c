from django import template

register = template.Library()

@register.filter
def filter_by_tag(recommendations, tag):
    """
    Filter recommendations by priority tag
    
    Args:
        recommendations (list): List of recommendation objects
        tag (str): Priority tag to filter by
        
    Returns:
        list: Filtered recommendations
    """
    if not recommendations:
        return []
    
    return [rec for rec in recommendations if rec.get('priority_tag') == tag]

@register.filter
def replace(value, arg):
    """
    Replace all occurrences of the first argument with the second argument
    
    Args:
        value (str): String to modify
        arg (str): String in format "old,new"
        
    Returns:
        str: Modified string
    """
    if not value or not arg:
        return value
    
    old, new = arg.split(',')
    return value.replace(old, new)
