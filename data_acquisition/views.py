from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from .models import Business, BusinessCategory
from .utils.data_collector import DataCollector
from .utils.competitive_analysis import CompetitiveAnalyzer
from .utils.recommendation_engine import RecommendationEngine
from .api_clients.google_places import GooglePlacesClient
import logging

logger = logging.getLogger(__name__)

@login_required
def business_search(request):
    """View for searching businesses"""
    query = request.GET.get('query', '')
    category = request.GET.get('category', '')
    location = request.GET.get('location', '')
    domain_filter = request.GET.get('domain_filter', '')

    businesses = Business.objects.all()

    if query:
        businesses = businesses.filter(name__icontains=query)

    if category:
        businesses = businesses.filter(businesscategorymapping__category__name__icontains=category)

    if location:
        businesses = businesses.filter(
            city__icontains=location
        ) | businesses.filter(
            state__icontains=location
        ) | businesses.filter(
            postcode__icontains=location
        )

    # Filter by domain type
    if domain_filter == 'com_au':
        businesses = businesses.filter(website__icontains='.com.au')
    elif domain_filter == 'au':
        businesses = businesses.filter(website__icontains='.au')
    elif domain_filter == 'has_website':
        businesses = businesses.exclude(website__isnull=True).exclude(website='')

    # Count businesses with .com.au domains
    com_au_count = Business.objects.filter(website__icontains='.com.au').count()

    categories = BusinessCategory.objects.all()

    context = {
        'businesses': businesses[:50],  # Limit to 50 results
        'categories': categories,
        'query': query,
        'category': category,
        'location': location,
        'domain_filter': domain_filter,
        'com_au_count': com_au_count,
        'total_count': Business.objects.count(),
        'with_website_count': Business.objects.exclude(website__isnull=True).exclude(website='').count()
    }

    return render(request, 'data_acquisition/business_search.html', context)

@login_required
def business_detail(request, business_id):
    """View for business details and analysis"""
    business = get_object_or_404(Business, id=business_id)

    # Get competitors
    analyzer = CompetitiveAnalyzer()
    competitors = analyzer.identify_competitors(business)

    # Calculate competitive metrics
    competitive_metrics = analyzer.calculate_competitive_metrics(business, competitors)

    # Generate recommendations
    recommendation_engine = RecommendationEngine()
    recommendations = recommendation_engine.generate_recommendations(business, competitive_metrics)

    context = {
        'business': business,
        'competitors': competitors,
        'competitive_metrics': competitive_metrics,
        'recommendations': recommendations
    }

    return render(request, 'data_acquisition/business_detail.html', context)

@login_required
def test_api_key(request):
    """View for testing the Google Places API key"""
    api_key = request.GET.get('api_key', '')

    if not api_key:
        return JsonResponse({'error': 'API key is required'}, status=400)

    try:
        # Initialize the client with the provided key
        client = GooglePlacesClient(api_key=api_key)

        # Try a simple search
        results = client.search_places('cafe', location=(-33.8688, 151.2093), radius=5000)

        # Check if we got results
        if results:
            return JsonResponse({
                'success': True,
                'message': 'API key is valid',
                'results_count': len(results),
                'sample_results': [r.get('name', 'Unknown') for r in results[:5]]
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'API key may be valid but no results were returned',
                'results_count': 0
            })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"API key test error: {str(e)}")
        logger.error(f"Detailed error: {error_details}")

        return JsonResponse({
            'success': False,
            'error': str(e),
            'error_details': error_details
        }, status=500)

@login_required
def lookup_place_details(request):
    """View for looking up a place by its Google Place ID"""
    place_id = request.GET.get('place_id', '')
    api_key = request.GET.get('api_key', '')
    update_db = request.GET.get('update_db', 'false').lower() == 'true'

    if not place_id:
        return JsonResponse({'error': 'Place ID is required'}, status=400)

    if not api_key:
        return JsonResponse({'error': 'API key is required'}, status=400)

    try:
        # Initialize the client with the provided key
        client = GooglePlacesClient(api_key=api_key)

        # Get place details
        place_details = client.get_place_details(place_id)

        # Check if we got results
        if place_details:
            # Extract key information
            result = {
                'success': True,
                'place_id': place_id,
                'name': place_details.get('name', 'Unknown'),
                'formatted_address': place_details.get('formatted_address', ''),
                'phone': place_details.get('formatted_phone_number', ''),
                'website': place_details.get('website', ''),
                'rating': place_details.get('rating', ''),
                'reviews_count': place_details.get('user_ratings_total', ''),
                'types': place_details.get('types', []),
                'available_fields': list(place_details.keys())
            }

            # Add location if available
            if 'geometry' in place_details and 'location' in place_details['geometry']:
                result['latitude'] = place_details['geometry']['location'].get('lat', '')
                result['longitude'] = place_details['geometry']['location'].get('lng', '')

            # Update the database if requested
            if update_db:
                from django.db import transaction
                from .models import Business, DataSource, BusinessDataSource, BusinessCategory, BusinessCategoryMapping

                with transaction.atomic():
                    # Get or create the business
                    business, created = Business.objects.get_or_create(
                        google_place_id=place_id,
                        defaults={'name': place_details.get('name', f'Business {place_id}')}
                    )

                    # Update business details
                    business.name = place_details.get('name', business.name)
                    business.phone = place_details.get('formatted_phone_number', business.phone)
                    business.website = place_details.get('website', business.website)
                    business.google_rating = place_details.get('rating', business.google_rating)
                    business.google_reviews_count = place_details.get('user_ratings_total', business.google_reviews_count)

                    # Update address
                    if 'formatted_address' in place_details:
                        address_parts = place_details['formatted_address'].split(',')

                        # Australian addresses typically have format: "Street, Suburb STATE Postcode, Australia"
                        if len(address_parts) >= 2:
                            # First part is street address
                            business.street_address = address_parts[0].strip()

                            # Second part usually contains suburb, state and postcode
                            location_part = address_parts[1].strip()
                            location_parts = location_part.split()

                            if len(location_parts) >= 2:
                                # Last part is usually the postcode
                                postcode_candidate = location_parts[-1]
                                if postcode_candidate.isdigit() and len(postcode_candidate) == 4:  # Australian postcodes are 4 digits
                                    business.postcode = postcode_candidate
                                    # Second last part is usually the state
                                    if len(location_parts) >= 3:
                                        business.state = location_parts[-2]
                                        # Everything before state and postcode is the suburb/city
                                        business.city = ' '.join(location_parts[:-2])
                                else:
                                    # If no postcode, assume last part is state and rest is suburb
                                    business.state = location_parts[-1]
                                    business.city = ' '.join(location_parts[:-1])

                    # Update coordinates
                    if 'geometry' in place_details and 'location' in place_details['geometry']:
                        location = place_details['geometry']['location']
                        business.latitude = location.get('lat')
                        business.longitude = location.get('lng')

                    # Save the business
                    business.save()

                    # Process business categories
                    if 'types' in place_details:
                        for type_name in place_details['types']:
                            # Skip generic types
                            if type_name in ['point_of_interest', 'establishment']:
                                continue

                            # Create or get category
                            category, _ = BusinessCategory.objects.get_or_create(
                                name=type_name.replace('_', ' ').title()
                            )

                            # Link category to business
                            BusinessCategoryMapping.objects.get_or_create(
                                business=business,
                                category=category,
                                defaults={'primary': False}
                            )

                    # Add business ID to result
                    result['business_id'] = business.id
                    result['database_updated'] = True
                    result['created'] = created

            return JsonResponse(result)
        else:
            return JsonResponse({
                'success': False,
                'message': 'No details found for the provided Place ID',
                'place_id': place_id
            })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Place details lookup error: {str(e)}")
        logger.error(f"Detailed error: {error_details}")

        return JsonResponse({
            'success': False,
            'error': str(e),
            'error_details': error_details,
            'place_id': place_id
        }, status=500)

@login_required
@require_http_methods(["POST"])
def collect_business_data(request):
    """View for triggering business data collection"""
    query = request.POST.get('query', '')
    location = request.POST.get('location', '')
    radius = int(request.POST.get('radius', 5000))
    max_results = int(request.POST.get('max_results', 20))
    api_key = request.POST.get('api_key', '')

    logger.info(f"Collecting business data - query: {query}, location: {location}, radius: {radius}, max_results: {max_results}")

    if not query:
        logger.warning("Business data collection failed: Query is required")
        return JsonResponse({'error': 'Query is required'}, status=400)

    # Parse location if provided
    location_tuple = None
    if location:
        try:
            lat, lng = location.split(',')
            location_tuple = (float(lat.strip()), float(lng.strip()))
            logger.info(f"Parsed location: {location_tuple}")
        except Exception as e:
            logger.error(f"Location parsing error: {str(e)}")
            return JsonResponse({'error': 'Invalid location format. Use "latitude,longitude"'}, status=400)

    try:
        # Collect business data
        collector = DataCollector()

        # If an API key was provided, use it
        if api_key:
            collector.google_places = GooglePlacesClient(api_key=api_key)
            logger.info("Using API key provided in the request")

        businesses = collector.collect_business_data(query, location_tuple, radius, max_results)

        logger.info(f"Business data collection completed. Collected {len(businesses)} businesses.")

        # Include more detailed information in the response
        business_details = []
        for b in businesses:
            business_details.append({
                'id': b.id,
                'name': b.name,
                'address': f"{b.street_address}, {b.city}, {b.state} {b.postcode}",
                'google_place_id': b.google_place_id,
                'website': b.website
            })

        return JsonResponse({
            'success': True,
            'businesses_collected': len(businesses),
            'business_ids': [b.id for b in businesses],
            'business_details': business_details
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        logger.error(f"Business data collection error: {str(e)}")
        logger.error(f"Detailed error: {error_details}")

        return JsonResponse({
            'success': False,
            'error': str(e),
            'error_details': error_details
        }, status=500)
