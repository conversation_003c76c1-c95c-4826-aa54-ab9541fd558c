"""
URL configuration for LocalSEO data_acquisition app.
"""
from django.urls import path
from django.contrib.auth import views as auth_views
from data_acquisition.views import business as business_views
from data_acquisition.views import google_business_profile as gbp_views
from data_acquisition.views.oauth_redirect import oauth_redirect

app_name = 'data_acquisition'

urlpatterns = [
    # Authentication URLs
    path('login/', auth_views.LoginView.as_view(template_name='data_acquisition/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(next_page='data_acquisition:login'), name='logout'),

    # Business data URLs
    path('', business_views.business_search, name='business_search'),
    path('testing-dashboard/', business_views.localseo_testing_dashboard, name='testing_dashboard'),
    path('business/<int:business_id>/', business_views.business_detail, name='business_detail'),
    path('collect/', business_views.collect_business_data, name='collect_business_data'),
    path('test-api-key/', business_views.test_api_key, name='test_api_key'),
    path('lookup-place/', business_views.lookup_place_details, name='lookup_place_details'),

    # Google Business Profile URLs
    path('gbp/connect/', gbp_views.gbp_connect, name='gbp_connect'),
    path('gbp/debug/', gbp_views.gbp_debug, name='gbp_debug'),  # Debug page for OAuth configuration
    path('gbp/callback/', gbp_views.gbp_oauth_callback, name='gbp_oauth_callback'),

    # OAuth redirect handlers - these will redirect to the gbp_oauth_callback view
    path('callback/', oauth_redirect, name='oauth_redirect1'),  # Alternative callback URL
    path('callback', oauth_redirect, name='oauth_redirect2'),  # Alternative callback URL without trailing slash
    path('oauth2callback/', oauth_redirect, name='oauth_redirect3'),  # Alternative callback URL for Google Cloud Console
    path('oauth2callback', oauth_redirect, name='oauth_redirect4'),  # Alternative callback URL without trailing slash

    # Dashboard and other views
    path('gbp/dashboard/', gbp_views.gbp_dashboard, name='gbp_dashboard'),
    path('gbp/refresh/<int:location_id>/', gbp_views.gbp_refresh_data, name='gbp_refresh_data'),
    path('gbp/competitors/<int:location_id>/', gbp_views.gbp_competitors, name='gbp_competitors'),
    path('gbp/recommendations/<int:location_id>/', gbp_views.gbp_generate_recommendations, name='gbp_generate_recommendations'),
]
