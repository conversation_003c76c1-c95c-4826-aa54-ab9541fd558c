from django.db import migrations, models

class Migration(migrations.Migration):

    dependencies = [
        ('data_acquisition', '0011_update_all_gbp_count_fields'),
    ]

    operations = [
        # Add new fields that don't exist yet
        migrations.AddField(
            model_name='business',
            name='gbp_answers_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_bookings_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_categories_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_locations_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_menus_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_questions_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_regular_hours_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_special_hours_count',
            field=models.IntegerField(default=0),
        ),
    ]
