# Generated by Django 5.2 on 2025-05-09 04:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('data_acquisition', '0002_alter_business_last_fetched_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AIRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('category', models.CharField(choices=[('PROFILE', 'Profile Optimization'), ('REVIEWS', 'Review Strategy'), ('POSTS', 'Post Content'), ('HOURS', 'Business Hours'), ('PHOTOS', 'Photos'), ('WEBSITE', 'Website'), ('CATEGORIES', 'Categories'), ('ATTRIBUTES', 'Attributes'), ('GENERAL', 'General')], max_length=20)),
                ('impact', models.CharField(choices=[('HIGH', 'High'), ('MEDIUM', 'Medium'), ('LOW', 'Low')], max_length=10)),
                ('effort', models.CharField(choices=[('HIGH', 'High'), ('MEDIUM', 'Medium'), ('LOW', 'Low')], max_length=10)),
                ('action_url', models.URLField(blank=True, null=True)),
                ('is_implemented', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ai_recommendations', to='data_acquisition.business')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('name', models.CharField(max_length=255)),
                ('picture_url', models.URLField(blank=True, null=True)),
                ('access_token', models.TextField()),
                ('refresh_token', models.TextField(blank=True, null=True)),
                ('token_expiry', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='google_account', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='GoogleBusinessLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location_name', models.CharField(max_length=255)),
                ('location_id', models.CharField(max_length=255)),
                ('title', models.CharField(max_length=255)),
                ('stored_location_type', models.CharField(blank=True, max_length=50, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('website_url', models.URLField(blank=True, null=True)),
                ('profile_photo_url', models.URLField(blank=True, null=True)),
                ('cover_photo_url', models.URLField(blank=True, null=True)),
                ('attributes', models.TextField(blank=True, null=True)),
                ('labels', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_fetched', models.DateTimeField(auto_now=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gbp_locations', to='data_acquisition.business')),
                ('google_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='data_acquisition.googleaccount')),
            ],
        ),
        migrations.CreateModel(
            name='GoogleBusinessInsights',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('views_search', models.IntegerField(default=0)),
                ('views_maps', models.IntegerField(default=0)),
                ('queries_direct', models.IntegerField(default=0)),
                ('queries_indirect', models.IntegerField(default=0)),
                ('actions_website', models.IntegerField(default=0)),
                ('actions_phone', models.IntegerField(default=0)),
                ('actions_directions', models.IntegerField(default=0)),
                ('photos_views', models.IntegerField(default=0)),
                ('photos_count', models.IntegerField(default=0)),
                ('local_post_views', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='insights', to='data_acquisition.googlebusinesslocation')),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('location', 'date')},
            },
        ),
    ]
