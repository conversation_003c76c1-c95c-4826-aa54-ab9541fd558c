from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('data_acquisition', '0010_update_gbp_services_count'),
    ]

    operations = [
        # Update all fields that start with gbp_ and end with _count
        migrations.RunSQL(
            """
            DO $$
            DECLARE
                col_name text;
            BEGIN
                FOR col_name IN 
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'data_acquisition_business' 
                    AND column_name LIKE 'gbp_%' 
                    AND column_name LIKE '%_count'
                LOOP
                    EXECUTE format('UPDATE data_acquisition_business SET %I = 0 WHERE %I IS NULL', col_name, col_name);
                END LOOP;
            END $$;
            """,
            reverse_sql=migrations.RunSQL.noop
        ),
    ]
