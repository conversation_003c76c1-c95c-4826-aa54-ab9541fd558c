# Generated by Django 5.2 on 2025-05-08 01:01

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Business',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('abn', models.CharField(blank=True, max_length=20, null=True)),
                ('business_type', models.CharField(blank=True, max_length=100, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('street_address', models.CharField(blank=True, max_length=255, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('state', models.CharField(blank=True, max_length=50, null=True)),
                ('postcode', models.CharField(blank=True, max_length=10, null=True)),
                ('country', models.CharField(default='Australia', max_length=50)),
                ('latitude', models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=7, max_digits=10, null=True)),
                ('google_place_id', models.CharField(blank=True, max_length=255, null=True)),
                ('google_rating', models.DecimalField(blank=True, decimal_places=1, max_digits=3, null=True)),
                ('google_reviews_count', models.IntegerField(default=0)),
                ('google_verified', models.BooleanField(default=False)),
                ('facebook_url', models.URLField(blank=True, null=True)),
                ('instagram_url', models.URLField(blank=True, null=True)),
                ('linkedin_url', models.URLField(blank=True, null=True)),
                ('page_speed_score', models.IntegerField(blank=True, null=True)),
                ('mobile_friendly_score', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_fetched', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
        migrations.CreateModel(
            name='DataSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('base_url', models.URLField(blank=True, null=True)),
                ('api_key_required', models.BooleanField(default=False)),
                ('active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='BusinessCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='children', to='data_acquisition.businesscategory')),
            ],
        ),
        migrations.CreateModel(
            name='BusinessDataSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_business_id', models.CharField(blank=True, max_length=255, null=True)),
                ('last_fetched', models.DateTimeField(default=django.utils.timezone.now)),
                ('data_quality_score', models.IntegerField(default=0)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_acquisition.business')),
                ('data_source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_acquisition.datasource')),
            ],
            options={
                'unique_together': {('business', 'data_source')},
            },
        ),
        migrations.AddField(
            model_name='business',
            name='data_sources',
            field=models.ManyToManyField(through='data_acquisition.BusinessDataSource', to='data_acquisition.datasource'),
        ),
        migrations.CreateModel(
            name='APIQuota',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('daily_limit', models.IntegerField(default=0)),
                ('daily_usage', models.IntegerField(default=0)),
                ('monthly_limit', models.IntegerField(default=0)),
                ('monthly_usage', models.IntegerField(default=0)),
                ('reset_date', models.DateTimeField()),
                ('data_source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_acquisition.datasource')),
            ],
        ),
        migrations.CreateModel(
            name='BusinessCategoryMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('primary', models.BooleanField(default=False)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_acquisition.business')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_acquisition.businesscategory')),
            ],
            options={
                'unique_together': {('business', 'category')},
            },
        ),
        migrations.CreateModel(
            name='BusinessHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.IntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')])),
                ('open_time', models.TimeField()),
                ('close_time', models.TimeField()),
                ('is_closed', models.BooleanField(default=False)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hours', to='data_acquisition.business')),
            ],
            options={
                'unique_together': {('business', 'day_of_week')},
            },
        ),
        migrations.CreateModel(
            name='BusinessReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_review_id', models.CharField(blank=True, max_length=255, null=True)),
                ('reviewer_name', models.CharField(blank=True, max_length=255, null=True)),
                ('rating', models.DecimalField(decimal_places=1, max_digits=3)),
                ('review_text', models.TextField(blank=True, null=True)),
                ('review_date', models.DateTimeField()),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='data_acquisition.business')),
                ('data_source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_acquisition.datasource')),
            ],
            options={
                'unique_together': {('business', 'data_source', 'source_review_id')},
            },
        ),
        migrations.CreateModel(
            name='BusinessKeyword',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('keyword', models.CharField(max_length=100)),
                ('relevance_score', models.IntegerField(default=0)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='keywords', to='data_acquisition.business')),
                ('source', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='data_acquisition.datasource')),
            ],
            options={
                'unique_together': {('business', 'keyword')},
            },
        ),
    ]
