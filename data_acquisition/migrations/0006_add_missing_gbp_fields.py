# Generated by Django 5.2 on 2025-05-30 04:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('data_acquisition', '0005_add_remaining_gbp_count_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='business',
            name='gbp_attributes_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_photos_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_posts_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_products_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_profile_completeness',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='business',
            name='gbp_services_count',
            field=models.IntegerField(default=0),
        ),
    ]
