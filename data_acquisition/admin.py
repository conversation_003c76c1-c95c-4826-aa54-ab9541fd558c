from django.contrib import admin
from .models import (
    DataSource, Business, BusinessDataSource, BusinessCategory,
    BusinessCategoryMapping, BusinessHours, BusinessReview,
    BusinessKeyword, APIQuota
)


@admin.register(DataSource)
class DataSourceAdmin(admin.ModelAdmin):
    list_display = ('name', 'active', 'api_key_required', 'created_at', 'updated_at')
    list_filter = ('active', 'api_key_required')
    search_fields = ('name', 'description')


@admin.register(Business)
class BusinessAdmin(admin.ModelAdmin):
    list_display = ('name', 'city', 'state', 'google_rating', 'last_fetched')
    list_filter = ('state', 'city')
    search_fields = ('name', 'abn', 'phone', 'email', 'website', 'street_address', 'city', 'postcode')
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'abn', 'business_type')
        }),
        ('Contact Information', {
            'fields': ('phone', 'email', 'website')
        }),
        ('Address Information', {
            'fields': ('street_address', 'city', 'state', 'postcode', 'country', 'latitude', 'longitude')
        }),
        ('Google My Business', {
            'fields': ('google_place_id', 'google_rating', 'google_reviews_count', 'google_verified')
        }),
        ('Social Media', {
            'fields': ('facebook_url', 'instagram_url', 'linkedin_url')
        }),
        ('Website Metrics', {
            'fields': ('page_speed_score', 'mobile_friendly_score')
        }),
    )


@admin.register(BusinessDataSource)
class BusinessDataSourceAdmin(admin.ModelAdmin):
    list_display = ('business', 'data_source', 'last_fetched', 'data_quality_score')
    list_filter = ('data_source', 'last_fetched')
    search_fields = ('business__name', 'source_business_id')


@admin.register(BusinessCategory)
class BusinessCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'parent')
    search_fields = ('name', 'description')


@admin.register(BusinessCategoryMapping)
class BusinessCategoryMappingAdmin(admin.ModelAdmin):
    list_display = ('business', 'category', 'primary')
    list_filter = ('primary', 'category')
    search_fields = ('business__name', 'category__name')


@admin.register(BusinessHours)
class BusinessHoursAdmin(admin.ModelAdmin):
    list_display = ('business', 'day_of_week', 'open_time', 'close_time', 'is_closed')
    list_filter = ('day_of_week', 'is_closed')
    search_fields = ('business__name',)


@admin.register(BusinessReview)
class BusinessReviewAdmin(admin.ModelAdmin):
    list_display = ('business', 'data_source', 'reviewer_name', 'rating', 'review_date')
    list_filter = ('data_source', 'rating', 'review_date')
    search_fields = ('business__name', 'reviewer_name', 'review_text')


@admin.register(BusinessKeyword)
class BusinessKeywordAdmin(admin.ModelAdmin):
    list_display = ('business', 'keyword', 'source', 'relevance_score')
    list_filter = ('source', 'relevance_score')
    search_fields = ('business__name', 'keyword')


@admin.register(APIQuota)
class APIQuotaAdmin(admin.ModelAdmin):
    list_display = ('data_source', 'daily_limit', 'daily_usage', 'monthly_limit', 'monthly_usage', 'reset_date')
    list_filter = ('data_source', 'reset_date')

