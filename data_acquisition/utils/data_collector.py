import logging
from django.db import transaction
from django.utils import timezone
from ..models import (
    Business, DataSource, BusinessDataSource, BusinessCategory,
    BusinessCategoryMapping, BusinessHours, BusinessReview
)
from ..api_clients.google_places import GooglePlacesClient
# Commented out non-Google APIs for this phase
# from ..api_clients.facebook_graph import FacebookGraphClient
# from ..api_clients.abn_lookup import ABNLookupClient
# from ..api_clients.yellow_pages import YellowPagesClient
from ..api_clients.website_analyzer import WebsiteAnalyzer

logger = logging.getLogger(__name__)

class DataCollector:
    """Utility for collecting and storing business data from various sources"""

    def __init__(self):
        """Initialize the data collector with API clients"""
        try:
            # IMPORTANT: You need to replace this with a valid API key that has the Places API enabled
            # The previous key was not authorized for the Places API
            # Get a new key from https://console.cloud.google.com/

            # For now, we'll use a placeholder - you need to replace this with your actual key
            places_api_key = "YOUR_VALID_PLACES_API_KEY"

            # Check if the key is still a placeholder
            if places_api_key == "YOUR_VALID_PLACES_API_KEY":
                logger.warning("You are using a placeholder API key. Please replace it with a valid Google Places API key.")

            self.google_places = GooglePlacesClient(api_key=places_api_key)
            logger.info("Google Places client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Google Places client: {str(e)}")
            self.google_places = None

        # Initialize Website Analyzer with the provided API key
        try:
            # PageSpeed API key
            pagespeed_api_key = "AIzaSyBJJWeCJLlfhTJ7cQVv2kfdGYxumlu-6hw"
            self.website_analyzer = WebsiteAnalyzer(api_key=pagespeed_api_key)
            logger.info("Website Analyzer initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Website Analyzer: {str(e)}")
            self.website_analyzer = None

        # Get or create data sources
        self.data_sources = {}
        self._initialize_data_sources()

    def _initialize_data_sources(self):
        """Initialize data sources in the database"""
        sources = [
            {
                'name': 'Google Places',
                'description': 'Google Places API',
                'base_url': 'https://maps.googleapis.com/maps/api/place',
                'api_key_required': True
            },
            {
                'name': 'Google PageSpeed',
                'description': 'Google PageSpeed Insights API',
                'base_url': 'https://www.googleapis.com/pagespeedonline/v5/runPagespeed',
                'api_key_required': True
            }
            # Other data sources commented out for this phase
            # {
            #     'name': 'Facebook',
            #     'description': 'Facebook Graph API',
            #     'base_url': 'https://graph.facebook.com',
            #     'api_key_required': True
            # },
            # {
            #     'name': 'ABN Lookup',
            #     'description': 'Australian Business Register ABN Lookup',
            #     'base_url': 'https://abr.business.gov.au/abrxmlsearch/ABRXMLSearch.asmx',
            #     'api_key_required': True
            # },
            # {
            #     'name': 'Yellow Pages',
            #     'description': 'Yellow Pages Australia',
            #     'base_url': 'https://www.yellowpages.com.au',
            #     'api_key_required': False
            # }
        ]

        for source_data in sources:
            source, _ = DataSource.objects.get_or_create(
                name=source_data['name'],
                defaults={
                    'description': source_data['description'],
                    'base_url': source_data['base_url'],
                    'api_key_required': source_data['api_key_required']
                }
            )
            self.data_sources[source_data['name']] = source

    def collect_business_data(self, query, location=None, radius=5000, max_results=20):
        """
        Collect business data from Google Places API

        Args:
            query (str): Search query (business type, category)
            location (tuple): Latitude and longitude tuple
            radius (int): Search radius in meters
            max_results (int): Maximum number of results to collect

        Returns:
            list: List of collected business objects
        """
        collected_businesses = []

        # Collect from Google Places
        if self.google_places:
            try:
                google_businesses = self._collect_from_google_places(query, location, radius, max_results)
                collected_businesses.extend(google_businesses)
                logger.info(f"Collected {len(google_businesses)} businesses from Google Places")
            except Exception as e:
                logger.error(f"Error collecting from Google Places: {str(e)}")

        # Yellow Pages collection commented out for this phase
        # try:
        #     # Convert location to suburb/city name for Yellow Pages
        #     location_str = self._location_to_string(location)
        #     yellow_businesses = self._collect_from_yellow_pages(query, location_str, max_results)
        #     collected_businesses.extend(yellow_businesses)
        #     logger.info(f"Collected {len(yellow_businesses)} businesses from Yellow Pages")
        # except Exception as e:
        #     logger.error(f"Error collecting from Yellow Pages: {str(e)}")

        return collected_businesses

    def _collect_from_google_places(self, query, location, radius, max_results):
        """
        Collect business data from Google Places API

        Args:
            query (str): Search query
            location (tuple): Latitude and longitude tuple
            radius (int): Search radius in meters
            max_results (int): Maximum number of results

        Returns:
            list: List of Business objects
        """
        businesses = []

        # Get the Google Places data source
        google_source = self.data_sources.get('Google Places')
        if not google_source:
            logger.error("Google Places data source not found")
            return businesses

        # Search for places
        if location:
            places = self.google_places.nearby_search(location, radius, keyword=query)
        else:
            places = self.google_places.search_places(query)

        # Limit results
        places = places[:max_results]

        # Process each place
        for place in places:
            try:
                # Get detailed information
                place_id = place.get('place_id')
                if not place_id:
                    continue

                place_details = self.google_places.get_place_details(place_id)

                # Create or update the business
                with transaction.atomic():
                    # Check if business already exists by Google Place ID
                    business = Business.objects.filter(google_place_id=place_id).first()

                    # Log the place details for debugging
                    logger.info(f"Place details for {place_id}: {place_details}")

                    # Make sure we have a name
                    business_name = place_details.get('name', '')
                    if not business_name:
                        logger.warning(f"No name found for place ID: {place_id}")
                        business_name = f"Business {place_id}"

                    if not business:
                        # Create new business
                        business = Business(
                            name=business_name,
                            google_place_id=place_id
                        )
                        logger.info(f"Creating new business: {business_name}")
                    else:
                        # Update name if it's empty or different
                        if not business.name or (business_name and business.name != business_name):
                            logger.info(f"Updating business name from '{business.name}' to '{business_name}'")
                            business.name = business_name

                    # Log the business details we're working with
                    logger.info(f"Processing business: {business_name} (ID: {place_id})")
                    logger.info(f"Details available: {', '.join(place_details.keys())}")

                    # Update business details
                    business.phone = place_details.get('formatted_phone_number', business.phone)
                    business.website = place_details.get('website', business.website)

                    # Update address
                    if 'formatted_address' in place_details:
                        logger.info(f"Address: {place_details['formatted_address']}")
                        address_parts = place_details['formatted_address'].split(',')

                        # Australian addresses typically have format: "Street, Suburb STATE Postcode, Australia"
                        if len(address_parts) >= 2:
                            # First part is street address
                            business.street_address = address_parts[0].strip()

                            # Second part usually contains suburb, state and postcode
                            location_part = address_parts[1].strip()
                            location_parts = location_part.split()

                            if len(location_parts) >= 2:
                                # Last part is usually the postcode
                                postcode_candidate = location_parts[-1]
                                if postcode_candidate.isdigit() and len(postcode_candidate) == 4:  # Australian postcodes are 4 digits
                                    business.postcode = postcode_candidate
                                    # Second last part is usually the state
                                    if len(location_parts) >= 3:
                                        business.state = location_parts[-2]
                                        # Everything before state and postcode is the suburb/city
                                        business.city = ' '.join(location_parts[:-2])
                                else:
                                    # If no postcode, assume last part is state and rest is suburb
                                    business.state = location_parts[-1]
                                    business.city = ' '.join(location_parts[:-1])

                    # Update coordinates
                    if 'geometry' in place_details and 'location' in place_details['geometry']:
                        location = place_details['geometry']['location']
                        business.latitude = location.get('lat')
                        business.longitude = location.get('lng')

                    # Update Google ratings
                    business.google_rating = place_details.get('rating', business.google_rating)
                    business.google_reviews_count = place_details.get('user_ratings_total', business.google_reviews_count)

                    # Save the business
                    business.save()
                    logger.info(f"Saved business: {business.name} (ID: {business.id})")

                    # Link to data source
                    business_source, created = BusinessDataSource.objects.get_or_create(
                        business=business,
                        data_source=google_source,
                        defaults={
                            'source_business_id': place_id
                        }
                    )

                    if not created:
                        business_source.save()
                        logger.info(f"Updated business source link for {business.name}")

                    # Process business categories
                    if 'types' in place_details:
                        for type_name in place_details['types']:
                            # Skip generic types
                            if type_name in ['point_of_interest', 'establishment']:
                                continue

                            # Create or get category
                            category, created = BusinessCategory.objects.get_or_create(
                                name=type_name.replace('_', ' ').title()
                            )

                            # Link category to business
                            BusinessCategoryMapping.objects.get_or_create(
                                business=business,
                                category=category,
                                defaults={'primary': False}
                            )

                    # Process opening hours
                    if 'opening_hours' in place_details and 'periods' in place_details['opening_hours']:
                        for period in place_details['opening_hours']['periods']:
                            # Skip if no open/close info
                            if 'open' not in period or 'day' not in period['open']:
                                continue

                            day = period['open']['day']

                            # Handle closed days
                            is_closed = 'close' not in period

                            # Parse open time
                            open_time = None
                            if 'time' in period['open']:
                                hours = period['open']['time'][:2]
                                minutes = period['open']['time'][2:]
                                open_time = f"{hours}:{minutes}"

                            # Parse close time
                            close_time = None
                            if not is_closed and 'close' in period and 'time' in period['close']:
                                hours = period['close']['time'][:2]
                                minutes = period['close']['time'][2:]
                                close_time = f"{hours}:{minutes}"

                            # Create or update hours
                            BusinessHours.objects.update_or_create(
                                business=business,
                                day_of_week=day,
                                defaults={
                                    'open_time': open_time or '00:00',
                                    'close_time': close_time or '00:00',
                                    'is_closed': is_closed
                                }
                            )

                    # Process reviews
                    if 'reviews' in place_details:
                        for review in place_details['reviews']:
                            # Create or update review
                            BusinessReview.objects.update_or_create(
                                business=business,
                                data_source=google_source,
                                source_review_id=review.get('time', ''),
                                defaults={
                                    'reviewer_name': review.get('author_name', ''),
                                    'rating': review.get('rating', 0),
                                    'review_text': review.get('text', ''),
                                    'review_date': timezone.now() if not review.get('time') else timezone.datetime.fromtimestamp(
                                        review.get('time'),
                                        tz=timezone.get_current_timezone()
                                    )
                                }
                            )

                    # Analyze website if available
                    if business.website:
                        try:
                            website_metrics = self.website_analyzer.analyze_website(business.website)
                            business.page_speed_score = website_metrics.get('performance_score')

                            mobile_metrics = self.website_analyzer.check_mobile_friendliness(business.website)
                            business.mobile_friendly_score = mobile_metrics.get('mobile_friendly_score')

                            business.save()
                        except Exception as e:
                            logger.error(f"Error analyzing website for {business.name}: {str(e)}")

                businesses.append(business)

            except Exception as e:
                logger.error(f"Error processing Google place: {str(e)}")
                continue

        return businesses

    def _collect_from_yellow_pages(self, query, location, max_results):
        """
        Collect business data from Yellow Pages

        Args:
            query (str): Search query
            location (str): Location string (suburb, city)
            max_results (int): Maximum number of results

        Returns:
            list: List of Business objects
        """
        businesses = []

        # Get the Yellow Pages data source
        yellow_source = self.data_sources.get('Yellow Pages')
        if not yellow_source:
            logger.error("Yellow Pages data source not found")
            return businesses

        # Search for businesses
        yellow_listings = self.yellow_pages.search_businesses(query, location)

        # Limit results
        yellow_listings = yellow_listings[:max_results]

        # Process each listing
        for listing in yellow_listings:
            try:
                # Get detailed information if URL is available
                listing_details = {}
                if listing.get('url'):
                    listing_details = self.yellow_pages.get_business_details(listing['url'])

                # Merge listing and details
                business_data = {**listing, **listing_details}

                # Create or update the business
                with transaction.atomic():
                    # Try to find existing business by name and phone
                    business = None
                    if business_data.get('phone'):
                        business = Business.objects.filter(
                            name=business_data.get('name', ''),
                            phone=business_data.get('phone')
                        ).first()

                    if not business:
                        # Create new business
                        business = Business(
                            name=business_data.get('name', '')
                        )

                    # Update business details
                    business.phone = business_data.get('phone', business.phone)
                    business.website = business_data.get('website', business.website)

                    # Update address
                    if business_data.get('address'):
                        address = business_data['address']

                        # Try to parse address parts
                        address_parts = address.split(',')
                        if len(address_parts) >= 2:
                            business.street_address = address_parts[0].strip()

                            # Parse city, state, postcode
                            location_parts = address_parts[1].strip().split()
                            if len(location_parts) >= 2:
                                business.city = ' '.join(location_parts[:-1])

                                # Check if last part is state or postcode
                                last_part = location_parts[-1]
                                if last_part.isdigit() and len(last_part) == 4:
                                    business.postcode = last_part
                                else:
                                    business.state = last_part

                    # Save the business
                    business.save()

                    # Link to data source
                    business_source, created = BusinessDataSource.objects.get_or_create(
                        business=business,
                        data_source=yellow_source,
                        defaults={
                            'source_business_id': business_data.get('url', '')
                        }
                    )

                    if not created:
                        business_source.save()

                    # Process business categories
                    if 'categories' in business_data:
                        for category_name in business_data['categories']:
                            # Create or get category
                            category, created = BusinessCategory.objects.get_or_create(
                                name=category_name
                            )

                            # Link category to business
                            BusinessCategoryMapping.objects.get_or_create(
                                business=business,
                                category=category,
                                defaults={'primary': False}
                            )

                    # Process opening hours
                    if 'opening_hours' in business_data:
                        for day, hours_str in business_data['opening_hours'].items():
                            # Map day string to day number
                            day_map = {
                                'Monday': 0, 'Tuesday': 1, 'Wednesday': 2, 'Thursday': 3,
                                'Friday': 4, 'Saturday': 5, 'Sunday': 6
                            }

                            day_num = day_map.get(day)
                            if day_num is None:
                                continue

                            # Check if closed
                            is_closed = hours_str.lower() == 'closed'

                            # Parse hours
                            open_time = '00:00'
                            close_time = '00:00'

                            if not is_closed:
                                # Try to parse time range (e.g., "9:00 am - 5:00 pm")
                                time_parts = hours_str.split('-')
                                if len(time_parts) == 2:
                                    # Parse open time
                                    open_str = time_parts[0].strip()
                                    open_time = self._parse_time_string(open_str)

                                    # Parse close time
                                    close_str = time_parts[1].strip()
                                    close_time = self._parse_time_string(close_str)

                            # Create or update hours
                            BusinessHours.objects.update_or_create(
                                business=business,
                                day_of_week=day_num,
                                defaults={
                                    'open_time': open_time,
                                    'close_time': close_time,
                                    'is_closed': is_closed
                                }
                            )

                    # Analyze website if available
                    if business.website:
                        try:
                            website_metrics = self.website_analyzer.analyze_website(business.website)
                            business.page_speed_score = website_metrics.get('performance_score')

                            mobile_metrics = self.website_analyzer.check_mobile_friendliness(business.website)
                            business.mobile_friendly_score = mobile_metrics.get('mobile_friendly_score')

                            business.save()
                        except Exception as e:
                            logger.error(f"Error analyzing website for {business.name}: {str(e)}")

                businesses.append(business)

            except Exception as e:
                logger.error(f"Error processing Yellow Pages listing: {str(e)}")
                continue

        return businesses

    def _location_to_string(self, location):
        """
        Convert location coordinates to a string representation

        Args:
            location (tuple): Latitude and longitude tuple

        Returns:
            str: Location string (defaults to 'Sydney, NSW')
        """
        # Default to Sydney if no location provided
        if not location:
            return 'Sydney, NSW'

        # TODO: Implement reverse geocoding to get location name
        # For now, return default
        return 'Sydney, NSW'

    def _parse_time_string(self, time_str):
        """
        Parse a time string (e.g., "9:00 am") to 24-hour format

        Args:
            time_str (str): Time string

        Returns:
            str: Time in 24-hour format (HH:MM)
        """
        try:
            # Remove any non-alphanumeric characters except : and space
            time_str = ''.join(c for c in time_str if c.isalnum() or c in [':', ' '])

            # Split into time and AM/PM
            parts = time_str.lower().split()
            if len(parts) != 2:
                return '00:00'

            time_part = parts[0]
            am_pm = parts[1]

            # Split hours and minutes
            if ':' in time_part:
                hours, minutes = time_part.split(':')
            else:
                hours = time_part
                minutes = '00'

            # Convert to integers
            hours = int(hours)
            minutes = int(minutes)

            # Adjust for PM
            if am_pm == 'pm' and hours < 12:
                hours += 12

            # Adjust for 12 AM
            if am_pm == 'am' and hours == 12:
                hours = 0

            # Format as HH:MM
            return f"{hours:02d}:{minutes:02d}"

        except Exception:
            return '00:00'
