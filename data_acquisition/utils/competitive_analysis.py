import logging
import math
from django.db.models import Avg, Count, F, Q
from ..models import Business, BusinessCategory, BusinessCategoryMapping

logger = logging.getLogger(__name__)

class CompetitiveAnalyzer:
    """Utility for analyzing competitive landscape of businesses"""
    
    def __init__(self):
        """Initialize the competitive analyzer"""
        pass
    
    def identify_competitors(self, business, radius_km=5, max_competitors=10):
        """
        Identify competitors for a business based on category and location
        
        Args:
            business (Business): The business to find competitors for
            radius_km (float): Radius in kilometers to search for competitors
            max_competitors (int): Maximum number of competitors to return
            
        Returns:
            list: List of competitor businesses
        """
        try:
            # Get the business categories
            category_mappings = BusinessCategoryMapping.objects.filter(business=business)
            category_ids = [mapping.category_id for mapping in category_mappings]
            
            if not category_ids:
                logger.warning(f"No categories found for business {business.name}")
                return []
            
            # Find businesses in the same categories
            competitors_by_category = Business.objects.filter(
                businesscategorymapping__category_id__in=category_ids
            ).exclude(
                id=business.id
            ).distinct()
            
            # If location data is available, filter by distance
            if business.latitude and business.longitude:
                # Filter competitors by distance
                nearby_competitors = []
                
                for competitor in competitors_by_category:
                    if competitor.latitude and competitor.longitude:
                        distance = self._calculate_distance(
                            float(business.latitude), float(business.longitude),
                            float(competitor.latitude), float(competitor.longitude)
                        )
                        
                        if distance <= radius_km:
                            competitor.distance = distance
                            nearby_competitors.append(competitor)
                
                # Sort by distance
                nearby_competitors.sort(key=lambda x: x.distance)
                
                # Limit to max_competitors
                return nearby_competitors[:max_competitors]
            else:
                # If no location data, just return by category
                return competitors_by_category[:max_competitors]
        
        except Exception as e:
            logger.error(f"Error identifying competitors: {str(e)}")
            return []
    
    def calculate_competitive_metrics(self, business, competitors):
        """
        Calculate competitive metrics for a business compared to its competitors
        
        Args:
            business (Business): The business to analyze
            competitors (list): List of competitor businesses
            
        Returns:
            dict: Competitive metrics
        """
        try:
            # Initialize metrics
            metrics = {
                'rating': {
                    'business': business.google_rating or 0,
                    'competitors_avg': 0,
                    'percentile': 0,
                    'difference': 0
                },
                'reviews_count': {
                    'business': business.google_reviews_count or 0,
                    'competitors_avg': 0,
                    'percentile': 0,
                    'difference': 0
                },
                'page_speed': {
                    'business': business.page_speed_score or 0,
                    'competitors_avg': 0,
                    'percentile': 0,
                    'difference': 0
                },
                'mobile_friendly': {
                    'business': business.mobile_friendly_score or 0,
                    'competitors_avg': 0,
                    'percentile': 0,
                    'difference': 0
                }
            }
            
            # If no competitors, return basic metrics
            if not competitors:
                return metrics
            
            # Calculate average metrics for competitors
            competitor_ratings = [c.google_rating for c in competitors if c.google_rating]
            competitor_reviews = [c.google_reviews_count for c in competitors if c.google_reviews_count is not None]
            competitor_page_speeds = [c.page_speed_score for c in competitors if c.page_speed_score]
            competitor_mobile_scores = [c.mobile_friendly_score for c in competitors if c.mobile_friendly_score]
            
            # Calculate averages
            if competitor_ratings:
                metrics['rating']['competitors_avg'] = sum(competitor_ratings) / len(competitor_ratings)
                metrics['rating']['difference'] = (business.google_rating or 0) - metrics['rating']['competitors_avg']
                metrics['rating']['percentile'] = self._calculate_percentile(business.google_rating or 0, competitor_ratings)
            
            if competitor_reviews:
                metrics['reviews_count']['competitors_avg'] = sum(competitor_reviews) / len(competitor_reviews)
                metrics['reviews_count']['difference'] = (business.google_reviews_count or 0) - metrics['reviews_count']['competitors_avg']
                metrics['reviews_count']['percentile'] = self._calculate_percentile(business.google_reviews_count or 0, competitor_reviews)
            
            if competitor_page_speeds:
                metrics['page_speed']['competitors_avg'] = sum(competitor_page_speeds) / len(competitor_page_speeds)
                metrics['page_speed']['difference'] = (business.page_speed_score or 0) - metrics['page_speed']['competitors_avg']
                metrics['page_speed']['percentile'] = self._calculate_percentile(business.page_speed_score or 0, competitor_page_speeds)
            
            if competitor_mobile_scores:
                metrics['mobile_friendly']['competitors_avg'] = sum(competitor_mobile_scores) / len(competitor_mobile_scores)
                metrics['mobile_friendly']['difference'] = (business.mobile_friendly_score or 0) - metrics['mobile_friendly']['competitors_avg']
                metrics['mobile_friendly']['percentile'] = self._calculate_percentile(business.mobile_friendly_score or 0, competitor_mobile_scores)
            
            return metrics
        
        except Exception as e:
            logger.error(f"Error calculating competitive metrics: {str(e)}")
            return {}
    
    def _calculate_distance(self, lat1, lon1, lat2, lon2):
        """
        Calculate distance between two points in kilometers using the Haversine formula
        
        Args:
            lat1 (float): Latitude of point 1
            lon1 (float): Longitude of point 1
            lat2 (float): Latitude of point 2
            lon2 (float): Longitude of point 2
            
        Returns:
            float: Distance in kilometers
        """
        # Earth radius in kilometers
        R = 6371.0
        
        # Convert latitude and longitude from degrees to radians
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)
        
        # Differences
        dlon = lon2_rad - lon1_rad
        dlat = lat2_rad - lat1_rad
        
        # Haversine formula
        a = math.sin(dlat / 2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        distance = R * c
        
        return distance
    
    def _calculate_percentile(self, value, values_list):
        """
        Calculate the percentile of a value in a list
        
        Args:
            value (float): The value to calculate percentile for
            values_list (list): List of values
            
        Returns:
            float: Percentile (0-100)
        """
        if not values_list or value is None:
            return 0
        
        # Count values less than the given value
        count_less = sum(1 for v in values_list if v < value)
        
        # Calculate percentile
        percentile = (count_less / len(values_list)) * 100
        
        return percentile
