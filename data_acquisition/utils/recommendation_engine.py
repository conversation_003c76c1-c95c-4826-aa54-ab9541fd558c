import logging
from django.utils import timezone

logger = logging.getLogger(__name__)

class RecommendationEngine:
    """Engine for generating SEO recommendations based on business data"""
    
    def __init__(self):
        """Initialize the recommendation engine"""
        pass
    
    def generate_recommendations(self, business, competitive_metrics=None):
        """
        Generate SEO recommendations for a business
        
        Args:
            business (Business): The business to generate recommendations for
            competitive_metrics (dict): Competitive metrics from CompetitiveAnalyzer
            
        Returns:
            list: List of recommendation objects
        """
        recommendations = []
        
        # Add Google Business Profile recommendations
        gbp_recommendations = self._generate_gbp_recommendations(business)
        recommendations.extend(gbp_recommendations)
        
        # Add website recommendations
        website_recommendations = self._generate_website_recommendations(business)
        recommendations.extend(website_recommendations)
        
        # Add competitive recommendations if metrics are provided
        if competitive_metrics:
            competitive_recommendations = self._generate_competitive_recommendations(business, competitive_metrics)
            recommendations.extend(competitive_recommendations)
        
        # Sort recommendations by priority
        recommendations.sort(key=lambda x: x['priority'], reverse=True)
        
        return recommendations
    
    def _generate_gbp_recommendations(self, business):
        """
        Generate recommendations for Google Business Profile
        
        Args:
            business (Business): The business to generate recommendations for
            
        Returns:
            list: List of recommendation objects
        """
        recommendations = []
        
        # Check if Google Place ID exists
        if not business.google_place_id:
            recommendations.append({
                'category': 'Google Business Profile',
                'title': 'Claim your Google Business Profile',
                'description': 'Your business does not appear to have a Google Business Profile. Create and verify your business on Google to improve local visibility.',
                'impact': 'High',
                'effort': 'Medium',
                'priority': 90,
                'action_url': 'https://business.google.com/create'
            })
            return recommendations  # Return early as other checks depend on having a GBP
        
        # Check for missing basic information
        if not business.phone:
            recommendations.append({
                'category': 'Google Business Profile',
                'title': 'Add phone number to your Google Business Profile',
                'description': 'Your business phone number is missing. Adding a phone number makes it easier for customers to contact you.',
                'impact': 'Medium',
                'effort': 'Low',
                'priority': 80
            })
        
        if not business.website:
            recommendations.append({
                'category': 'Google Business Profile',
                'title': 'Add website to your Google Business Profile',
                'description': 'Your business website is missing. Adding a website helps customers learn more about your business.',
                'impact': 'Medium',
                'effort': 'Low',
                'priority': 75
            })
        
        # Check for address information
        if not business.street_address or not business.city or not business.postcode:
            recommendations.append({
                'category': 'Google Business Profile',
                'title': 'Complete your business address',
                'description': 'Your business address is incomplete. A complete address helps customers find your location.',
                'impact': 'Medium',
                'effort': 'Low',
                'priority': 70
            })
        
        # Check for Google reviews
        if business.google_reviews_count is None or business.google_reviews_count < 5:
            recommendations.append({
                'category': 'Google Business Profile',
                'title': 'Encourage more Google reviews',
                'description': f'Your business has {business.google_reviews_count or 0} Google reviews. Encourage satisfied customers to leave reviews to improve your visibility.',
                'impact': 'High',
                'effort': 'Medium',
                'priority': 85
            })
        
        # Check for Google rating
        if business.google_rating is None or business.google_rating < 4.0:
            recommendations.append({
                'category': 'Google Business Profile',
                'title': 'Improve your Google rating',
                'description': f'Your business has a {business.google_rating or 0}/5 rating on Google. Focus on improving customer satisfaction and responding to negative reviews.',
                'impact': 'High',
                'effort': 'High',
                'priority': 85
            })
        
        return recommendations
    
    def _generate_website_recommendations(self, business):
        """
        Generate recommendations for website optimization
        
        Args:
            business (Business): The business to generate recommendations for
            
        Returns:
            list: List of recommendation objects
        """
        recommendations = []
        
        # Skip if no website
        if not business.website:
            return recommendations
        
        # Check page speed score
        if business.page_speed_score is None:
            recommendations.append({
                'category': 'Website',
                'title': 'Analyze your website performance',
                'description': 'Your website performance has not been analyzed. Use Google PageSpeed Insights to identify performance issues.',
                'impact': 'Medium',
                'effort': 'Low',
                'priority': 60,
                'action_url': f'https://pagespeed.web.dev/report?url={business.website}'
            })
        elif business.page_speed_score < 50:
            recommendations.append({
                'category': 'Website',
                'title': 'Improve website performance',
                'description': f'Your website performance score is {business.page_speed_score}/100. Improving page speed can enhance user experience and SEO.',
                'impact': 'High',
                'effort': 'High',
                'priority': 75,
                'action_url': f'https://pagespeed.web.dev/report?url={business.website}'
            })
        elif business.page_speed_score < 80:
            recommendations.append({
                'category': 'Website',
                'title': 'Optimize website performance',
                'description': f'Your website performance score is {business.page_speed_score}/100. Further optimization can improve user experience and SEO.',
                'impact': 'Medium',
                'effort': 'Medium',
                'priority': 65,
                'action_url': f'https://pagespeed.web.dev/report?url={business.website}'
            })
        
        # Check mobile-friendliness
        if business.mobile_friendly_score is None:
            recommendations.append({
                'category': 'Website',
                'title': 'Test mobile-friendliness',
                'description': 'Your website has not been tested for mobile-friendliness. Ensure your site works well on mobile devices.',
                'impact': 'High',
                'effort': 'Low',
                'priority': 70,
                'action_url': f'https://search.google.com/test/mobile-friendly?url={business.website}'
            })
        elif business.mobile_friendly_score < 50:
            recommendations.append({
                'category': 'Website',
                'title': 'Improve mobile-friendliness',
                'description': f'Your mobile-friendliness score is {business.mobile_friendly_score}/100. A mobile-friendly website is crucial for SEO and user experience.',
                'impact': 'High',
                'effort': 'High',
                'priority': 80,
                'action_url': f'https://search.google.com/test/mobile-friendly?url={business.website}'
            })
        
        return recommendations
    
    def _generate_competitive_recommendations(self, business, competitive_metrics):
        """
        Generate recommendations based on competitive analysis
        
        Args:
            business (Business): The business to generate recommendations for
            competitive_metrics (dict): Competitive metrics from CompetitiveAnalyzer
            
        Returns:
            list: List of recommendation objects
        """
        recommendations = []
        
        # Check Google rating compared to competitors
        if 'rating' in competitive_metrics:
            rating_diff = competitive_metrics['rating']['difference']
            if rating_diff < -0.5:
                recommendations.append({
                    'category': 'Competitive',
                    'title': 'Improve Google rating compared to competitors',
                    'description': f'Your Google rating is {abs(rating_diff):.1f} points below the average of your competitors. Focus on improving customer satisfaction and responding to reviews.',
                    'impact': 'High',
                    'effort': 'High',
                    'priority': 85
                })
        
        # Check review count compared to competitors
        if 'reviews_count' in competitive_metrics:
            reviews_diff = competitive_metrics['reviews_count']['difference']
            if reviews_diff < -10:
                recommendations.append({
                    'category': 'Competitive',
                    'title': 'Increase number of Google reviews',
                    'description': f'You have {abs(reviews_diff):.0f} fewer reviews than your average competitor. Encourage satisfied customers to leave reviews.',
                    'impact': 'High',
                    'effort': 'Medium',
                    'priority': 80
                })
        
        # Check page speed compared to competitors
        if 'page_speed' in competitive_metrics and business.website:
            speed_diff = competitive_metrics['page_speed']['difference']
            if speed_diff < -10:
                recommendations.append({
                    'category': 'Competitive',
                    'title': 'Improve website performance compared to competitors',
                    'description': f'Your website performance score is {abs(speed_diff):.0f} points below your competitors. Optimizing your website can give you a competitive advantage.',
                    'impact': 'Medium',
                    'effort': 'High',
                    'priority': 70,
                    'action_url': f'https://pagespeed.web.dev/report?url={business.website}'
                })
        
        return recommendations
