import os
import logging
import json
from datetime import datetime, timed<PERSON>ta
from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.urls import reverse
from django.utils import timezone
from django.db import transaction
from django.contrib import messages
from google.oauth2.credentials import Credentials
from google.auth.exceptions import RefreshError

from ..models import (
    Business, GoogleAccount, GoogleBusinessLocation,
    GoogleBusinessInsights, AIRecommendation
)
from ..api_clients.google_business_profile import GoogleBusinessProfileClient
from ..api_clients.google_places import GooglePlacesClient
from ..api_clients.gpt_api import GPTClient
from ..utils.competitive_analysis import CompetitiveAnalyzer

logger = logging.getLogger(__name__)

def get_business_defaults(**kwargs):
    """
    Get default values for all fields in the Business model.
    This function ensures that all NOT NULL fields have default values.

    Args:
        **kwargs: Keyword arguments to override default values

    Returns:
        dict: Default values for all fields in the Business model
    """
    # Start with basic defaults for common fields
    defaults = {
        'phone': '',
        'website': '',
        'google_reviews_count': 0,
        'google_verified': False
    }

    # Add default values for all fields that have NOT NULL constraints
    for field in Business._meta.fields:
        # Skip fields that already have defaults
        if field.name in defaults:
            continue

        # Skip fields that allow NULL values
        if field.null:
            continue

        # Skip the primary key field
        if field.primary_key:
            continue

        # Skip the name field (it's required for get_or_create)
        if field.name == 'name':
            continue

        # Add default values based on field type
        if field.get_internal_type() in ['IntegerField', 'PositiveIntegerField', 'PositiveSmallIntegerField', 'SmallIntegerField', 'BigIntegerField']:
            defaults[field.name] = 0
        elif field.get_internal_type() in ['DecimalField', 'FloatField']:
            defaults[field.name] = 0.0
        elif field.get_internal_type() in ['BooleanField']:
            defaults[field.name] = False
        elif field.get_internal_type() in ['CharField', 'TextField']:
            defaults[field.name] = ''
        elif field.get_internal_type() in ['DateField', 'DateTimeField']:
            # Use the default value if provided, otherwise skip (auto_now and auto_now_add fields)
            if field.has_default():
                defaults[field.name] = field.get_default()

    # Override with provided values
    defaults.update(kwargs)

    # Log the defaults for debugging
    logger.info(f"Business defaults: {defaults}")

    return defaults

@login_required
def gbp_debug(request):
    """View for debugging OAuth configuration"""
    # Initialize the client
    client = GoogleBusinessProfileClient()

    # Get server information
    import socket
    hostname = socket.gethostname()

    # Get base URL
    base_url = request.build_absolute_uri('/').rstrip('/')

    # Generate possible callback URLs
    callback_urls = [
        f"{base_url}/oauth2callback",
        f"{base_url}/oauth2callback/",
        f"{base_url}/callback",
        f"{base_url}/callback/",
        f"{base_url}/gbp/callback/",
        "https://digitalapex.com.au/oauth2callback",
        "https://www.digitalapex.com.au/oauth2callback",
        "https://digitalapex.com.au/callback",
        "https://www.digitalapex.com.au/callback"
    ]

    # Generate an auth URL for testing
    try:
        auth_url, state = client.get_auth_url()
        auth_url_generated = True
    except Exception as e:
        auth_url = f"Error generating auth URL: {str(e)}"
        state = None
        auth_url_generated = False

    # Parse the auth URL to extract the redirect_uri parameter
    redirect_uri_param = None
    if auth_url_generated:
        try:
            import urllib.parse
            parsed_url = urllib.parse.urlparse(auth_url)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            redirect_uri_param = query_params.get('redirect_uri', [''])[0]
        except Exception as e:
            redirect_uri_param = f"Error parsing auth URL: {str(e)}"

    context = {
        'client_id': client.client_id,
        'redirect_uri': client.redirect_uri,
        'scopes': client.SCOPES,
        'hostname': hostname,
        'base_url': base_url,
        'callback_urls': callback_urls,
        'auth_url': auth_url,
        'state': state,
        'redirect_uri_param': redirect_uri_param,
        'env_redirect_uri': os.environ.get('GOOGLE_OAUTH_REDIRECT_URI', 'Not set')
    }

    return render(request, 'data_acquisition/gbp_debug.html', context)

def gbp_connect(request):
    """View for connecting to Google Business Profile"""
    # Check if user already has a connected account
    try:
        google_account = GoogleAccount.objects.get(user=request.user)
        # Check if token is still valid
        if google_account.token_expiry > timezone.now():
            messages.info(request, "You already have a connected Google account.")
            return redirect('gbp_dashboard')
    except GoogleAccount.DoesNotExist:
        google_account = None

    # Initialize the client
    client = GoogleBusinessProfileClient()

    # Get the authorization URL
    try:
        auth_url, state = client.get_auth_url()

        # Log the authorization URL and state
        logger.info(f"Generated auth URL: {auth_url}")
        logger.info(f"Generated state: {state}")
        logger.info(f"Redirect URI: {client.redirect_uri}")

        # Store the state in the session
        request.session['gbp_oauth_state'] = state

        # Instead of rendering a template, directly redirect to Google OAuth
        logger.info(f"Redirecting to Google OAuth: {auth_url}")
        return redirect(auth_url)

    except Exception as e:
        logger.error(f"Error getting auth URL: {str(e)}")
        messages.error(request, f"Error connecting to Google: {str(e)}")
        return redirect('data_acquisition:business_search')

def gbp_oauth_callback(request):
    """Callback view for Google OAuth"""
    # Log the full request details
    logger.info(f"OAuth callback received at path: {request.path}")
    logger.info(f"Full callback URL: {request.build_absolute_uri()}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request GET parameters: {request.GET}")

    # Get the authorization code and state from the request
    code = request.GET.get('code')
    state = request.GET.get('state')

    # Log the callback parameters
    if code:
        logger.info(f"OAuth callback received - code: {code[:10]}..., state: {state}")
    else:
        logger.info(f"OAuth callback received - no code provided, state: {state}")

    # Log the error parameter if present
    error = request.GET.get('error')
    if error:
        logger.error(f"OAuth error received: {error}")
        error_description = request.GET.get('error_description', 'No description provided')
        logger.error(f"OAuth error description: {error_description}")
        messages.error(request, f"Google authentication error: {error_description}")
        return redirect('data_acquisition:gbp_connect')

    # Get the state from the session
    session_state = request.session.get('gbp_oauth_state')
    logger.info(f"Session state: {session_state}")

    # Validate the state
    if not state or state != session_state:
        logger.error(f"Invalid OAuth state. Received: {state}, Expected: {session_state}")
        messages.error(request, "Invalid OAuth state. Please try again.")
        return redirect('data_acquisition:gbp_connect')

    # Clear the state from the session
    if 'gbp_oauth_state' in request.session:
        del request.session['gbp_oauth_state']

    # Exchange the code for credentials
    try:
        # Initialize the client
        client = GoogleBusinessProfileClient()

        # Log the scopes
        logger.info(f"Client scopes: {client.SCOPES}")

        try:
            # Exchange the code for credentials
            credentials = client.get_credentials_from_code(code, state)

            # Get user info
            client.credentials = credentials
            user_info = client.get_user_info()
        except Exception as scope_error:
            # Check if it's a scope change error
            if "Scope has changed" in str(scope_error):
                logger.warning(f"Scope change detected: {str(scope_error)}")

                # Try to handle the scope change by extracting the scopes from the callback URL
                scope_param = request.GET.get('scope', '')
                if scope_param:
                    logger.info(f"Scope parameter from callback: {scope_param}")

                    # Update the client's scopes
                    client.SCOPES = scope_param.split()
                    logger.info(f"Updated client scopes: {client.SCOPES}")

                    # Try again with the updated scopes
                    credentials = client.get_credentials_from_code(code, state)
                    client.credentials = credentials
                    user_info = client.get_user_info()
                else:
                    raise
            else:
                raise

        # Save the credentials and user info
        with transaction.atomic():
            # For testing, create a default user if none exists
            from django.contrib.auth.models import User
            if not request.user.is_authenticated:
                # Create or get a test user
                test_user, _ = User.objects.get_or_create(
                    username='test_user',
                    defaults={
                        'email': user_info.get('email', '<EMAIL>'),
                        'first_name': 'Test',
                        'last_name': 'User'
                    }
                )
                user = test_user
            else:
                user = request.user

            # Check if user already has a connected account
            google_account, created = GoogleAccount.objects.update_or_create(
                user=user,
                defaults={
                    'email': user_info.get('email', ''),
                    'name': user_info.get('name', ''),
                    'picture_url': user_info.get('picture', ''),
                    'access_token': credentials.token,
                    'refresh_token': credentials.refresh_token,
                    'token_expiry': credentials.expiry
                }
            )

            # Get accounts and locations
            accounts, accounts_error_type, accounts_error_msg = client.get_accounts()

            if accounts_error_type:
                # Handle the error based on its type
                if accounts_error_type == 'rate_limit':
                    logger.warning("Rate limit error when getting accounts.")
                    messages.warning(request, "API rate limit exceeded. Please try again later.")
                elif accounts_error_type == 'permission':
                    logger.warning("Permission error when getting accounts.")
                    messages.warning(request, "You don't have permission to access your Google Business Profile. Please check your account permissions.")
                elif accounts_error_type == 'not_found':
                    logger.warning("No accounts found.")
                    messages.info(request, "No Google Business Profile accounts found for this user.")
                elif accounts_error_type == 'api_disabled':
                    logger.warning("API not enabled.")
                    messages.warning(request, "The Google Business Profile API is not enabled for this project. Please enable it in the Google Cloud Console.")
                else:
                    logger.error(f"Unknown error when getting accounts: {accounts_error_msg}")
                    messages.error(request, f"An error occurred while accessing your Google Business Profile: {accounts_error_msg}")

                # Create a placeholder business and location
                create_placeholder_business_and_location(google_account)
            elif accounts:
                # Process the accounts and locations
                locations_found = False

                for account in accounts:
                    account_name = account.get('name')
                    if account_name:
                        locations, locations_error_type, locations_error_msg = client.get_locations(account_name)

                        if locations_error_type:
                            # Handle the error based on its type
                            if locations_error_type == 'rate_limit':
                                logger.warning(f"Rate limit error when getting locations for account {account_name}.")
                                messages.warning(request, "API rate limit exceeded when fetching locations. Please try again later.")
                            elif locations_error_type == 'permission':
                                logger.warning(f"Permission error when getting locations for account {account_name}.")
                                messages.warning(request, "You don't have permission to access locations for this account. Please check your account permissions.")
                            elif locations_error_type == 'not_found':
                                logger.warning(f"Account {account_name} not found.")
                                messages.info(request, f"Account {account_name} not found.")
                            elif locations_error_type == 'api_disabled':
                                logger.warning("API not enabled.")
                                messages.warning(request, "The Google Business Profile API is not enabled for this project. Please enable it in the Google Cloud Console.")
                            else:
                                logger.error(f"Unknown error when getting locations: {locations_error_msg}")
                                messages.error(request, f"An error occurred while accessing locations: {locations_error_msg}")

                            # Skip this account and try the next one
                            continue

                        if locations:
                            locations_found = True

                            for location in locations:
                                # Create or get a business for this location
                                business_name = location.get('title', 'Unknown Business')

                                # Create a defaults dictionary with all fields set to appropriate default values
                                defaults = get_business_defaults(
                                    phone=location.get('phoneNumber', ''),
                                    website=location.get('websiteUri', '')
                                )

                                business, _ = Business.objects.get_or_create(
                                    name=business_name,
                                    defaults=defaults
                                )

                                # Create or update the location
                                gbp_location, _ = GoogleBusinessLocation.objects.update_or_create(
                                    location_name=location.get('name'),
                                    google_account=google_account,
                                    defaults={
                                        'business': business,
                                        'location_id': location.get('name', '').split('/')[-1],
                                        'title': business_name,
                                        'phone_number': location.get('phoneNumber', ''),
                                        'website_url': location.get('websiteUri', ''),
                                        'address': json.dumps(location.get('storefrontAddress', {})),
                                        'attributes': json.dumps(location.get('attributes', {})),
                                        'labels': json.dumps(location.get('labels', []))
                                    }
                                )

                # If no locations were found for any account, create a placeholder
                if not locations_found:
                    logger.warning("No locations found for any account.")
                    messages.info(request, "No business locations found for your Google Business Profile accounts.")
                    create_placeholder_business_and_location(google_account)
            else:
                # No accounts found, but no error either
                logger.warning("No accounts found, but no error reported.")
                messages.info(request, "No Google Business Profile accounts found for this user.")
                create_placeholder_business_and_location(google_account)

        messages.success(request, f"Successfully connected to Google Business Profile as {user_info.get('email')}")
        return redirect('data_acquisition:gbp_dashboard')

    except Exception as e:
        logger.error(f"Error in OAuth callback: {str(e)}")

        # Provide a more user-friendly error message
        if "Scope has changed" in str(e):
            messages.error(request, "There was an issue with the Google authentication. Please try again.")
            logger.error(f"Scope change error details: {str(e)}")
        else:
            messages.error(request, f"Error connecting to Google: {str(e)}")

        return redirect('data_acquisition:gbp_connect')

def create_placeholder_business_and_location(google_account):
    """
    Create a placeholder business and location for a Google account.

    Args:
        google_account: The GoogleAccount object
    """
    # Create a defaults dictionary with all fields set to appropriate default values
    defaults = get_business_defaults(
        phone='',
        website=''
    )

    # Log the defaults
    logger.info(f"Business defaults for placeholder: {defaults}")

    business, _ = Business.objects.get_or_create(
        name="My Business",
        defaults=defaults
    )

    # Create a placeholder location
    GoogleBusinessLocation.objects.update_or_create(
        location_name="placeholder",
        google_account=google_account,
        defaults={
            'business': business,
            'location_id': "placeholder",
            'title': "My Business",
            'phone_number': '',
            'website_url': '',
            'address': json.dumps({}),
            'attributes': json.dumps({}),
            'labels': json.dumps([])
        }
    )

def gbp_dashboard(request):
    """Dashboard view for Google Business Profile"""
    # For testing, use test user if not authenticated
    from django.contrib.auth.models import User
    if not request.user.is_authenticated:
        try:
            test_user = User.objects.get(username='test_user')
            user = test_user
        except User.DoesNotExist:
            messages.warning(request, "You need to connect your Google Business Profile first.")
            return redirect('data_acquisition:gbp_connect')
    else:
        user = request.user

    # Check if user has a connected account
    try:
        google_account = GoogleAccount.objects.get(user=user)
    except GoogleAccount.DoesNotExist:
        messages.warning(request, "You need to connect your Google Business Profile first.")
        return redirect('data_acquisition:gbp_connect')

    # Get the user's locations
    locations = GoogleBusinessLocation.objects.filter(google_account=google_account)

    # If no locations, redirect to connect page
    if not locations.exists():
        messages.warning(request, "No business locations found. Please connect your Google Business Profile.")
        return redirect('data_acquisition:gbp_connect')

    # Get the selected location or use the first one
    location_id = request.GET.get('location_id')
    if location_id:
        location = get_object_or_404(GoogleBusinessLocation, id=location_id, google_account=google_account)
    else:
        location = locations.first()

    # Get insights for the location
    insights = GoogleBusinessInsights.objects.filter(location=location).order_by('-date')[:30]

    # Get recommendations for the business
    recommendations = AIRecommendation.objects.filter(business=location.business).order_by('-created_at')

    # Check if this is a placeholder location due to rate limiting
    is_placeholder = location.location_name == "placeholder"

    context = {
        'google_account': google_account,
        'locations': locations,
        'selected_location': location,
        'insights': insights,
        'recommendations': recommendations,
        'is_placeholder': is_placeholder,
        'rate_limited': is_placeholder
    }

    # Add a message if this is a placeholder location
    if is_placeholder:
        # Check if there's already a message about API rate limits
        has_rate_limit_message = any('rate limit' in str(message).lower() for message in messages.get_messages(request))

        if not has_rate_limit_message:
            messages.warning(request, "Your Google Business Profile was connected, but we couldn't fetch your business data. This could be because you don't have any business locations, or because you don't have permission to access them. Try refreshing the data or check your Google Business Profile account.")

    return render(request, 'data_acquisition/gbp_dashboard.html', context)

def gbp_refresh_data(request, location_id):
    """View for refreshing data from Google Business Profile"""
    # Get the location
    location = get_object_or_404(GoogleBusinessLocation, id=location_id, google_account__user=request.user)

    try:
        # Get the credentials
        google_account = location.google_account
        credentials_dict = google_account.to_credentials_dict()
        credentials = Credentials.from_authorized_user_info(credentials_dict)

        # Initialize the client
        client = GoogleBusinessProfileClient(credentials=credentials)

        # Get location metrics
        location_data = client.get_location_metrics(location.location_name)

        # Update the location
        location_info = location_data.get('location', {})
        location.title = location_info.get('title', location.title)
        location.phone_number = location_info.get('phoneNumber', location.phone_number)
        location.website_url = location_info.get('websiteUri', location.website_url)

        if 'storefrontAddress' in location_info:
            location.address = json.dumps(location_info['storefrontAddress'])

        if 'attributes' in location_info:
            location.attributes = json.dumps(location_info['attributes'])

        if 'labels' in location_info:
            location.labels = json.dumps(location_info['labels'])

        location.save()

        # Update the business
        business = location.business
        business.name = location.title
        business.phone = location.phone_number
        business.website = location.website_url
        business.save()

        # Process metrics
        metrics_data = location_data.get('metrics', {})
        daily_metrics = metrics_data.get('dailyMetricsTimeSeries', [])

        for daily_metric in daily_metrics:
            date_str = daily_metric.get('date', {}).get('year', '2023') + '-' + \
                      daily_metric.get('date', {}).get('month', '01') + '-' + \
                      daily_metric.get('date', {}).get('day', '01')

            try:
                date = datetime.strptime(date_str, '%Y-%m-%d').date()

                # Create or update insights
                GoogleBusinessInsights.objects.update_or_create(
                    location=location,
                    date=date,
                    defaults={
                        'views_search': daily_metric.get('metricValues', {}).get('VIEWS_SEARCH', 0),
                        'views_maps': daily_metric.get('metricValues', {}).get('VIEWS_MAPS', 0),
                        'queries_direct': daily_metric.get('metricValues', {}).get('QUERIES_DIRECT', 0),
                        'queries_indirect': daily_metric.get('metricValues', {}).get('QUERIES_INDIRECT', 0),
                        'actions_website': daily_metric.get('metricValues', {}).get('ACTIONS_WEBSITE', 0),
                        'actions_phone': daily_metric.get('metricValues', {}).get('ACTIONS_PHONE', 0),
                        'actions_directions': daily_metric.get('metricValues', {}).get('ACTIONS_DRIVING_DIRECTIONS', 0)
                    }
                )
            except Exception as e:
                logger.error(f"Error processing metric for date {date_str}: {str(e)}")

        messages.success(request, "Successfully refreshed data from Google Business Profile.")

    except RefreshError:
        # Token expired and couldn't be refreshed
        messages.error(request, "Your Google account token has expired. Please reconnect your account.")
        return redirect('data_acquisition:gbp_connect')

    except Exception as e:
        logger.error(f"Error refreshing data: {str(e)}")
        messages.error(request, f"Error refreshing data: {str(e)}")

    return redirect(reverse('data_acquisition:gbp_dashboard') + f'?location_id={location_id}')

def gbp_competitors(request, location_id):
    """View for finding and analyzing competitors"""
    # Get the location
    location = get_object_or_404(GoogleBusinessLocation, id=location_id, google_account__user=request.user)
    business = location.business

    # Get the API key from request or environment
    api_key = request.GET.get('api_key', os.environ.get('GOOGLE_PLACES_API_KEY'))

    if not api_key:
        messages.error(request, "Google Places API key is required to find competitors.")
        return redirect(reverse('data_acquisition:gbp_dashboard') + f'?location_id={location_id}')

    try:
        # Initialize the Google Places client
        places_client = GooglePlacesClient(api_key=api_key)

        # Get the business coordinates
        lat = float(business.latitude) if business.latitude else None
        lng = float(business.longitude) if business.longitude else None

        if not lat or not lng:
            messages.error(request, "Business location coordinates are required to find competitors.")
            return redirect(reverse('data_acquisition:gbp_dashboard') + f'?location_id={location_id}')

        # Get the business categories
        categories = business.businesscategorymapping_set.all()
        category_names = [cm.category.name for cm in categories]

        # Find nearby competitors
        competitors = []

        for category in category_names:
            # Search for places in this category
            nearby_places = places_client.nearby_search(
                location=(lat, lng),
                radius=5000,  # 5km radius
                keyword=category
            )

            for place in nearby_places:
                # Skip if this is the same business
                if place.get('place_id') == business.google_place_id:
                    continue

                # Get place details
                place_details = places_client.get_place_details(place.get('place_id'))

                if place_details:
                    competitors.append({
                        'name': place_details.get('name', 'Unknown'),
                        'place_id': place_details.get('place_id'),
                        'address': place_details.get('formatted_address', ''),
                        'rating': place_details.get('rating'),
                        'reviews_count': place_details.get('user_ratings_total', 0),
                        'website': place_details.get('website', ''),
                        'phone': place_details.get('formatted_phone_number', ''),
                        'types': place_details.get('types', []),
                        'distance': 0  # TODO: Calculate distance
                    })

        # Sort competitors by rating (descending)
        competitors.sort(key=lambda x: x.get('rating', 0) or 0, reverse=True)

        context = {
            'business': business,
            'location': location,
            'competitors': competitors[:10]  # Limit to top 10
        }

        return render(request, 'data_acquisition/gbp_competitors.html', context)

    except Exception as e:
        logger.error(f"Error finding competitors: {str(e)}")
        messages.error(request, f"Error finding competitors: {str(e)}")
        return redirect(reverse('data_acquisition:gbp_dashboard') + f'?location_id={location_id}')

def gbp_generate_recommendations(request, location_id):
    """View for generating AI recommendations"""
    # Get the location
    location = get_object_or_404(GoogleBusinessLocation, id=location_id, google_account__user=request.user)
    business = location.business

    # Get the GPT API key from request or environment
    api_key = request.POST.get('api_key', os.environ.get('GPT_API_KEY'))

    if not api_key:
        messages.error(request, "GPT API key is required to generate recommendations.")
        return redirect(reverse('data_acquisition:gbp_dashboard') + f'?location_id={location_id}')

    try:
        # Initialize the GPT client
        gpt_client = GPTClient(api_key=api_key)

        # Prepare business data
        business_data = {
            'name': business.name,
            'business_type': business.business_type,
            'categories': [cm.category.name for cm in business.businesscategorymapping_set.all()],
            'rating': float(business.google_rating) if business.google_rating else None,
            'reviews_count': business.google_reviews_count,
            'website': business.website,
            'profile_completeness': {
                'photos': 'Complete' if business.google_verified else 'Incomplete',
                'hours': 'Complete' if business.hours.exists() else 'Incomplete',
                'description': 'Complete' if business.business_type else 'Incomplete',
                'attributes': 'Complete' if location.attributes else 'Incomplete',
                'services': 'Unknown'
            }
        }

        # Add metrics if available
        insights = GoogleBusinessInsights.objects.filter(location=location).order_by('-date')[:30]
        if insights.exists():
            metrics = {
                'views_search': sum(insight.views_search for insight in insights),
                'views_maps': sum(insight.views_maps for insight in insights),
                'actions_website': sum(insight.actions_website for insight in insights),
                'actions_phone': sum(insight.actions_phone for insight in insights),
                'actions_directions': sum(insight.actions_directions for insight in insights)
            }
            business_data['metrics'] = metrics

        # Get competitor data
        analyzer = CompetitiveAnalyzer()
        competitors = analyzer.identify_competitors(business)
        competitor_data = []

        for competitor in competitors[:5]:  # Limit to top 5
            competitor_data.append({
                'name': competitor.name,
                'rating': float(competitor.google_rating) if competitor.google_rating else None,
                'reviews_count': competitor.google_reviews_count,
                'categories': [cm.category.name for cm in competitor.businesscategorymapping_set.all()],
                'website': competitor.website
            })

        # Generate recommendations
        recommendations = gpt_client.generate_recommendations(business_data, competitor_data)

        # Save recommendations to database
        with transaction.atomic():
            for rec in recommendations:
                AIRecommendation.objects.create(
                    business=business,
                    title=rec['title'],
                    description=rec['description'],
                    category=rec['category'],
                    impact=rec['impact'],
                    effort=rec['effort']
                )

        messages.success(request, f"Successfully generated {len(recommendations)} recommendations.")

    except Exception as e:
        logger.error(f"Error generating recommendations: {str(e)}")
        messages.error(request, f"Error generating recommendations: {str(e)}")

    return redirect(reverse('data_acquisition:gbp_dashboard') + f'?location_id={location_id}')
