from django.shortcuts import redirect
from django.http import HttpResponse
import logging

logger = logging.getLogger(__name__)

def oauth_redirect(request):
    """
    A simple redirect handler for OAuth callbacks.
    This view will redirect to the gbp_oauth_callback view with all query parameters.
    """
    # Log the request
    logger.info(f"OAuth redirect received at path: {request.path}")
    logger.info(f"Full redirect URL: {request.build_absolute_uri()}")
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request GET parameters: {request.GET}")
    
    # Get all query parameters
    query_params = request.GET.urlencode()
    
    # Redirect to the gbp_oauth_callback view with all query parameters
    return redirect(f"/gbp/callback/?{query_params}")
