{% extends 'data_acquisition/base.html' %}
{% load static %}

{% block title %}LocalSEO Testing Dashboard{% endblock %}

{% block extra_css %}
<style>
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }

    .dashboard-header {
        text-align: center;
        margin-bottom: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .dashboard-header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .dashboard-header p {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .feature-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-left: 5px solid #667eea;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }

    .feature-card h3 {
        color: #4a5568;
        margin-bottom: 15px;
        font-size: 1.3rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .feature-icon {
        font-size: 1.5rem;
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-success { background: #48bb78; }
    .status-error { background: #f56565; }
    .status-warning { background: #ed8936; }
    .status-pending { background: #9f7aea; }

    .test-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        margin: 5px;
        text-decoration: none;
        display: inline-block;
    }

    .test-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .test-button.secondary {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    }

    .test-button.danger {
        background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #718096;
        font-size: 0.9rem;
    }

    .endpoint-list {
        background: #f7fafc;
        padding: 15px;
        border-radius: 8px;
        margin-top: 15px;
    }

    .endpoint-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e2e8f0;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }

    .endpoint-item:last-child {
        border-bottom: none;
    }

    .endpoint-method {
        background: #667eea;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .endpoint-method.post {
        background: #48bb78;
    }

    .system-status {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e2e8f0;
    }

    .status-item:last-child {
        border-bottom: none;
    }

    .quick-actions {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .action-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }

    @media (max-width: 768px) {
        .feature-grid {
            grid-template-columns: 1fr;
        }

        .dashboard-header h1 {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1>🏢 LocalSEO Testing Dashboard</h1>
        <p>Comprehensive testing suite for all LocalSEO features and API endpoints</p>
        <p><strong>Environment:</strong> {{ request.get_host }} | <strong>User:</strong> {{ user.email|default:"Not authenticated" }}</p>
    </div>

    <!-- System Status -->
    <div class="system-status">
        <h3><span class="feature-icon">⚡</span> System Status</h3>
        <div class="status-item">
            <span>Database Connection</span>
            <span><span class="status-indicator status-success"></span>Connected</span>
        </div>
        <div class="status-item">
            <span>Authentication</span>
            <span>
                {% if user.is_authenticated %}
                    <span class="status-indicator status-success"></span>Authenticated as {{ user.email }}
                {% else %}
                    <span class="status-indicator status-error"></span>Not Authenticated
                {% endif %}
            </span>
        </div>
        <div class="status-item">
            <span>Google Business Profile</span>
            <span>
                {% if user.is_authenticated and user.google_account %}
                    <span class="status-indicator status-success"></span>Connected ({{ user.google_account.email }})
                {% else %}
                    <span class="status-indicator status-error"></span>Not Connected
                {% endif %}
            </span>
        </div>
        <div class="status-item">
            <span>LocalSEO App</span>
            <span><span class="status-indicator status-success"></span>Active</span>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ total_businesses|default:"0" }}</div>
            <div class="stat-label">Total Businesses</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ com_au_businesses|default:"0" }}</div>
            <div class="stat-label">.com.au Businesses</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ gbp_locations|default:"0" }}</div>
            <div class="stat-label">GBP Locations</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ ai_recommendations|default:"0" }}</div>
            <div class="stat-label">AI Recommendations</div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h3><span class="feature-icon">🚀</span> Quick Actions</h3>
        <div class="action-grid">
            <a href="{% url 'data_acquisition:business_search' %}" class="test-button">
                🔍 Search Businesses
            </a>
            {% if user.is_authenticated %}
                <a href="{% url 'data_acquisition:gbp_connect' %}" class="test-button secondary">
                    🔗 Connect Google Business Profile
                </a>
                <a href="{% url 'data_acquisition:gbp_dashboard' %}" class="test-button">
                    📊 GBP Dashboard
                </a>
            {% else %}
                <a href="{% url 'data_acquisition:login' %}" class="test-button danger">
                    🔐 Login Required
                </a>
            {% endif %}
            <a href="/admin/data_acquisition/" class="test-button secondary" target="_blank">
                ⚙️ Admin Panel
            </a>
        </div>
    </div>

    <!-- Feature Testing Grid -->
    <div class="feature-grid">
        <!-- Business Search & Discovery -->
        <div class="feature-card">
            <h3><span class="feature-icon">🔍</span> Business Search & Discovery</h3>
            <p>Test business search functionality with various filters and parameters.</p>

            <div class="endpoint-list">
                <div class="endpoint-item">
                    <span><span class="endpoint-method">GET</span> /localseo/</span>
                    <span class="status-indicator status-success"></span>
                </div>
                <div class="endpoint-item">
                    <span><span class="endpoint-method">GET</span> /localseo/lookup-place/</span>
                    <span class="status-indicator status-success"></span>
                </div>
                <div class="endpoint-item">
                    <span><span class="endpoint-method">GET</span> /localseo/test-api-key/</span>
                    <span class="status-indicator status-success"></span>
                </div>
            </div>

            <div style="margin-top: 15px;">
                <a href="{% url 'data_acquisition:business_search' %}" class="test-button">
                    Test Business Search
                </a>
                <a href="{% url 'data_acquisition:test_api_key' %}" class="test-button secondary">
                    Test API Key
                </a>
            </div>
        </div>

        <!-- Google Business Profile Integration -->
        <div class="feature-card">
            <h3><span class="feature-icon">🔗</span> Google Business Profile</h3>
            <p>Test OAuth integration, dashboard access, and data synchronization.</p>

            <div class="endpoint-list">
                <div class="endpoint-item">
                    <span><span class="endpoint-method">GET</span> /localseo/gbp/connect/</span>
                    <span class="status-indicator status-success"></span>
                </div>
                <div class="endpoint-item">
                    <span><span class="endpoint-method">GET</span> /localseo/gbp/callback/</span>
                    <span class="status-indicator status-success"></span>
                </div>
                <div class="endpoint-item">
                    <span><span class="endpoint-method">GET</span> /localseo/gbp/dashboard/</span>
                    <span class="status-indicator status-success"></span>
                </div>
                <div class="endpoint-item">
                    <span><span class="endpoint-method post">POST</span> /localseo/gbp/refresh/{id}/</span>
                    <span class="status-indicator status-success"></span>
                </div>
            </div>

            <div style="margin-top: 15px;">
                {% if user.is_authenticated %}
                    <a href="{% url 'data_acquisition:gbp_connect' %}" class="test-button">
                        Connect GBP
                    </a>
                    <a href="{% url 'data_acquisition:gbp_dashboard' %}" class="test-button secondary">
                        View Dashboard
                    </a>
                    <a href="{% url 'data_acquisition:gbp_debug' %}" class="test-button" style="background: #ed8936;">
                        Debug OAuth
                    </a>
                {% else %}
                    <a href="{% url 'data_acquisition:login' %}" class="test-button danger">
                        Login Required
                    </a>
                {% endif %}
            </div>
        </div>

        <!-- Competitor Analysis -->
        <div class="feature-card">
            <h3><span class="feature-icon">🏆</span> Competitor Analysis</h3>
            <p>Test competitor discovery, analysis, and benchmarking features.</p>

            <div class="endpoint-list">
                <div class="endpoint-item">
                    <span><span class="endpoint-method">GET</span> /localseo/gbp/competitors/{id}/</span>
                    <span class="status-indicator status-success"></span>
                </div>
                <div class="endpoint-item">
                    <span><span class="endpoint-method">GET</span> /localseo/business/{id}/</span>
                    <span class="status-indicator status-success"></span>
                </div>
            </div>

            <div style="margin-top: 15px;">
                {% if recent_business %}
                    <a href="{% url 'data_acquisition:business_detail' recent_business.id %}" class="test-button">
                        View Business Details
                    </a>
                    {% if recent_gbp_location %}
                        <a href="{% url 'data_acquisition:gbp_competitors' recent_gbp_location.id %}" class="test-button secondary">
                            Analyze Competitors
                        </a>
                    {% endif %}
                {% else %}
                    <span style="color: #718096; font-size: 0.9rem;">No businesses available for testing</span>
                {% endif %}
            </div>
        </div>

        <!-- AI Recommendations -->
        <div class="feature-card">
            <h3><span class="feature-icon">🤖</span> AI-Powered Recommendations</h3>
            <p>Test OpenAI GPT integration for generating business improvement recommendations.</p>

            <div class="endpoint-list">
                <div class="endpoint-item">
                    <span><span class="endpoint-method post">POST</span> /localseo/gbp/recommendations/{id}/</span>
                    <span class="status-indicator status-success"></span>
                </div>
            </div>

            <div style="margin-top: 15px;">
                {% if recent_gbp_location %}
                    <a href="{% url 'data_acquisition:gbp_generate_recommendations' recent_gbp_location.id %}" class="test-button">
                        Generate Recommendations
                    </a>
                {% else %}
                    <span style="color: #718096; font-size: 0.9rem;">Connect GBP first to test recommendations</span>
                {% endif %}
            </div>
        </div>

        <!-- Data Collection -->
        <div class="feature-card">
            <h3><span class="feature-icon">📥</span> Data Collection</h3>
            <p>Test bulk business data collection from Google Places API.</p>

            <div class="endpoint-list">
                <div class="endpoint-item">
                    <span><span class="endpoint-method post">POST</span> /localseo/collect/</span>
                    <span class="status-indicator status-success"></span>
                </div>
            </div>

            <div style="margin-top: 15px;">
                <a href="{% url 'data_acquisition:collect_business_data' %}" class="test-button">
                    Collect Business Data
                </a>
                <a href="{% url 'data_acquisition:lookup_place_details' %}" class="test-button secondary">
                    Lookup Place Details
                </a>
            </div>
        </div>

        <!-- Authentication & System -->
        <div class="feature-card">
            <h3><span class="feature-icon">🔐</span> Authentication & System</h3>
            <p>Test authentication flows and system health monitoring.</p>

            <div class="endpoint-list">
                <div class="endpoint-item">
                    <span><span class="endpoint-method">GET</span> /localseo/login/</span>
                    <span class="status-indicator status-success"></span>
                </div>
                <div class="endpoint-item">
                    <span><span class="endpoint-method post">POST</span> /localseo/logout/</span>
                    <span class="status-indicator status-success"></span>
                </div>
            </div>

            <div style="margin-top: 15px;">
                {% if user.is_authenticated %}
                    <a href="{% url 'data_acquisition:logout' %}" class="test-button danger">
                        Logout
                    </a>
                {% else %}
                    <a href="{% url 'data_acquisition:login' %}" class="test-button">
                        Login
                    </a>
                {% endif %}
                <a href="/admin/" class="test-button secondary" target="_blank">
                    Admin Panel
                </a>
            </div>
        </div>
    </div>

    <!-- Testing Instructions -->
    <div class="feature-card" style="grid-column: 1 / -1;">
        <h3><span class="feature-icon">📋</span> Testing Instructions</h3>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
            <div>
                <h4>🔍 Business Search Testing</h4>
                <ol style="margin-left: 20px; color: #4a5568;">
                    <li>Use search terms like "restaurants", "dentists", "plumbers"</li>
                    <li>Test location filtering with coordinates: -33.8688,151.2093</li>
                    <li>Apply category and domain filters</li>
                    <li>Verify search results and pagination</li>
                </ol>
            </div>

            <div>
                <h4>🔗 Google Business Profile Testing</h4>
                <ol style="margin-left: 20px; color: #4a5568;">
                    <li>Click "Connect GBP" to start OAuth flow</li>
                    <li>Complete Google authentication</li>
                    <li>View dashboard with business locations</li>
                    <li>Test data refresh functionality</li>
                </ol>
            </div>

            <div>
                <h4>🤖 AI Recommendations Testing</h4>
                <ol style="margin-left: 20px; color: #4a5568;">
                    <li>Ensure you have a connected GBP location</li>
                    <li>Click "Generate Recommendations"</li>
                    <li>Verify AI-generated suggestions appear</li>
                    <li>Check recommendation categories and priorities</li>
                </ol>
            </div>

            <div>
                <h4>📥 Data Collection Testing</h4>
                <ol style="margin-left: 20px; color: #4a5568;">
                    <li>Provide a valid Google Places API key</li>
                    <li>Use query: "restaurants in Sydney"</li>
                    <li>Set location: -33.8688,151.2093</li>
                    <li>Monitor collection progress and results</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %}