{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}{{ business.name }} | LocalSEO Platform{% endblock %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static "css/styles.css" %}">
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
  <a href="{% url 'data_acquisition:business_search' %}">Home</a>
  &rsaquo; <a href="{% url 'data_acquisition:business_search' %}">Business Search</a>
  &rsaquo; {{ business.name }}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <div class="business-header">
    <div class="business-name">
      {{ business.name }}
      {% if business.google_rating %}
        <span class="business-rating">{{ business.google_rating }} ★</span>
      {% endif %}
    </div>
    <div>
      {% if business.street_address %}
        {{ business.street_address }}, {{ business.city }} {{ business.state }} {{ business.postcode }}
      {% endif %}
    </div>
    {% if business.phone %}
      <div>Phone: {{ business.phone }}</div>
    {% endif %}
    {% if business.website %}
      <div>Website: <a href="{{ business.website }}" target="_blank">{{ business.website }}</a></div>
    {% endif %}
  </div>

  <div class="business-details">
    <div class="detail-section">
      <div class="section-title">Google Business Profile</div>

      <div class="metrics-container">
        <div class="metric-card">
          <div class="metric-value">{{ business.google_rating|default:"N/A" }}</div>
          <div class="metric-label">Google Rating</div>
        </div>

        <div class="metric-card">
          <div class="metric-value">{{ business.google_reviews_count|default:"0" }}</div>
          <div class="metric-label">Google Reviews</div>
        </div>

        <div class="metric-card">
          <div class="metric-value">{{ business.google_verified|yesno:"Yes,No,Unknown" }}</div>
          <div class="metric-label">Verified</div>
        </div>
      </div>
    </div>

    <div class="detail-section">
      <div class="section-title">Website Performance</div>

      <div class="metrics-container">
        <div class="metric-card">
          <div class="metric-value">{{ business.page_speed_score|default:"N/A" }}</div>
          <div class="metric-label">Page Speed Score</div>
        </div>

        <div class="metric-card">
          <div class="metric-value">{{ business.mobile_friendly_score|default:"N/A" }}</div>
          <div class="metric-label">Mobile Score</div>
        </div>
      </div>
    </div>
  </div>

  <div class="detail-section">
    <div class="section-title">Competitive Analysis</div>

    {% if competitors %}
      <div class="metrics-container">
        <div class="metric-card">
          <div class="metric-value">{{ competitive_metrics.rating.percentile|floatformat:0 }}</div>
          <div class="metric-label">Rating Percentile</div>
        </div>

        <div class="metric-card">
          <div class="metric-value">{{ competitive_metrics.reviews_count.percentile|floatformat:0 }}</div>
          <div class="metric-label">Reviews Percentile</div>
        </div>

        <div class="metric-card">
          <div class="metric-value">{{ competitive_metrics.page_speed.percentile|floatformat:0 }}</div>
          <div class="metric-label">Speed Percentile</div>
        </div>
      </div>

      <div class="competitor-list">
        <h3>Top Competitors</h3>

        {% for competitor in competitors|slice:":5" %}
          <div class="competitor-card">
            <div><strong>{{ competitor.name }}</strong> {% if competitor.google_rating %}<span class="business-rating">{{ competitor.google_rating }} ★</span>{% endif %}</div>
            <div>{{ competitor.google_reviews_count|default:"0" }} reviews</div>
            {% if competitor.distance %}<div>{{ competitor.distance|floatformat:1 }} km away</div>{% endif %}
          </div>
        {% endfor %}
      </div>
    {% else %}
      <p>No competitors found in your area and category.</p>
    {% endif %}
  </div>

  <div class="recommendations">
    <h2>Recommendations</h2>

    {% if recommendations %}
      {% for rec in recommendations %}
        <div class="recommendation-card {% if rec.impact == 'High' %}high-impact{% elif rec.impact == 'Medium' %}medium-impact{% else %}low-impact{% endif %}">
          <div class="recommendation-title">{{ rec.title }}</div>
          <div class="recommendation-meta">
            Impact: <strong>{{ rec.impact }}</strong> |
            Effort: <strong>{{ rec.effort }}</strong> |
            Category: {{ rec.category }}
          </div>
          <div class="recommendation-description">{{ rec.description }}</div>
          {% if rec.action_url %}
            <div style="margin-top: 10px;">
              <a href="{{ rec.action_url }}" target="_blank">Take Action</a>
            </div>
          {% endif %}
        </div>
      {% endfor %}
    {% else %}
      <p>No recommendations available at this time.</p>
    {% endif %}
  </div>
</div>
{% endblock %}
