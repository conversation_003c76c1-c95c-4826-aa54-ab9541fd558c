{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}Google Business Profile Dashboard | LocalSEO Platform{% endblock %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static "css/styles.css" %}">
<style>
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .user-profile {
    display: flex;
    align-items: center;
  }

  .profile-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
  }

  .location-selector {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border-radius: 5px;
  }

  .location-selector select {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    min-width: 300px;
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
  }

  .dashboard-card {
    padding: 20px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }

  .card-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #417690;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-title a {
    font-size: 14px;
    font-weight: normal;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }

  .metric-box {
    padding: 15px;
    background-color: #f8f8f8;
    border-radius: 4px;
    text-align: center;
  }

  .metric-value {
    font-size: 24px;
    font-weight: bold;
    margin: 5px 0;
  }

  .metric-label {
    font-size: 12px;
    color: #666;
  }

  .action-button {
    display: inline-block;
    padding: 8px 16px;
    background-color: #79aec8;
    color: white;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    cursor: pointer;
  }

  .action-button:hover {
    background-color: #417690;
    color: white;
  }

  .action-button.primary {
    background-color: #4CAF50;
  }

  .action-button.primary:hover {
    background-color: #3e8e41;
  }

  .action-button.secondary {
    background-color: #f44336;
  }

  .action-button.secondary:hover {
    background-color: #d32f2f;
  }

  .chart-container {
    height: 300px;
    margin-top: 20px;
  }

  .business-info {
    margin-bottom: 20px;
  }

  .business-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .info-item {
    margin-bottom: 10px;
  }

  .info-label {
    font-weight: bold;
    color: #666;
    font-size: 14px;
  }

  .info-value {
    font-size: 16px;
  }

  .recommendations-list {
    margin-top: 20px;
  }

  .no-data {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
  }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
  <a href="{% url 'business_search' %}">Home</a>
  &rsaquo; Google Business Profile Dashboard
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <div class="dashboard-header">
    <h1>Google Business Profile Dashboard</h1>

    <div class="user-profile">
      {% if google_account.picture_url %}
        <img src="{{ google_account.picture_url }}" alt="{{ google_account.name }}" class="profile-image">
      {% endif %}
      <div>
        <div>{{ google_account.name }}</div>
        <div style="font-size: 12px; color: #666;">{{ google_account.email }}</div>
      </div>
    </div>
  </div>

  {% if locations %}
    <div class="location-selector">
      <form method="get" action="{% url 'gbp_dashboard' %}">
        <label for="location_id">Select Business Location:</label>
        <select name="location_id" id="location_id" onchange="this.form.submit()">
          {% for loc in locations %}
            <option value="{{ loc.id }}" {% if loc.id == selected_location.id %}selected{% endif %}>
              {{ loc.title }}
            </option>
          {% endfor %}
        </select>
      </form>
    </div>

    <div class="business-info">
      <h2>{{ selected_location.title }}</h2>

      {% if is_placeholder %}
      <div style="margin-bottom: 20px; padding: 15px; background-color: #fff3cd; border-radius: 5px; border-left: 5px solid #ffc107;">
        <h3 style="margin-top: 0; color: #856404;">No Business Data Available</h3>
        <p>We've successfully connected to your Google Business Profile, but we couldn't fetch your business data. This could be due to one of the following reasons:</p>
        <ol style="margin-left: 20px;">
          <li><strong>No Business Locations:</strong> You might not have any business locations in your Google Business Profile account.</li>
          <li><strong>Permission Issues:</strong> You might not have permission to access your business locations.</li>
          <li><strong>API Configuration:</strong> The Google Business Profile API might not be properly configured.</li>
          <li><strong>API Rate Limits:</strong> You might have exceeded the API rate limits.</li>
        </ol>
        <p>Please try the following:</p>
        <ol style="margin-left: 20px;">
          <li>Click "Refresh Data" below to try again</li>
          <li>Check your <a href="https://business.google.com/locations" target="_blank">Google Business Profile</a> to make sure you have business locations</li>
          <li>Make sure you've enabled the necessary APIs in the <a href="https://console.cloud.google.com/apis/library" target="_blank">Google Cloud Console</a>:
            <ul>
              <li>My Business Account Management API</li>
              <li>My Business Business Information API</li>
              <li>My Business Place Actions API</li>
              <li>My Business Verifications API</li>
            </ul>
          </li>
          <li>Check that you have proper quota limits set for these APIs</li>
        </ol>
      </div>
      {% endif %}

      <div class="business-info-grid">
        <div class="info-item">
          <div class="info-label">Phone:</div>
          <div class="info-value">{{ selected_location.phone_number|default:"Not specified" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Website:</div>
          <div class="info-value">
            {% if selected_location.website_url %}
              <a href="{{ selected_location.website_url }}" target="_blank">{{ selected_location.website_url }}</a>
            {% else %}
              Not specified
            {% endif %}
          </div>
        </div>
        <div class="info-item">
          <div class="info-label">Address:</div>
          <div class="info-value">{{ selected_location.address|default:"Not specified" }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Last Updated:</div>
          <div class="info-value">{{ selected_location.updated_at|date:"F j, Y, g:i a" }}</div>
        </div>
      </div>

      <div style="margin-top: 15px;">
        <a href="{% url 'gbp_refresh_data' selected_location.id %}" class="action-button">
          Refresh Data
        </a>
        <a href="{% url 'gbp_competitors' selected_location.id %}" class="action-button primary">
          Find Competitors
        </a>
      </div>
    </div>

    <div class="dashboard-grid">
      <div class="dashboard-card">
        <div class="card-title">
          Performance Metrics
          <span style="font-size: 14px; font-weight: normal; color: #666;">Last 30 days</span>
        </div>

        {% if insights %}
          <div class="metrics-grid">
            <div class="metric-box">
              <div class="metric-value">{{ insights.0.views_search|add:insights.0.views_maps }}</div>
              <div class="metric-label">Total Views</div>
            </div>
            <div class="metric-box">
              <div class="metric-value">{{ insights.0.views_search }}</div>
              <div class="metric-label">Search Views</div>
            </div>
            <div class="metric-box">
              <div class="metric-value">{{ insights.0.views_maps }}</div>
              <div class="metric-label">Maps Views</div>
            </div>
            <div class="metric-box">
              <div class="metric-value">{{ insights.0.actions_website }}</div>
              <div class="metric-label">Website Clicks</div>
            </div>
            <div class="metric-box">
              <div class="metric-value">{{ insights.0.actions_phone }}</div>
              <div class="metric-label">Phone Calls</div>
            </div>
            <div class="metric-box">
              <div class="metric-value">{{ insights.0.actions_directions }}</div>
              <div class="metric-label">Direction Requests</div>
            </div>
          </div>

          <div class="chart-container">
            <!-- Chart will be added here with JavaScript -->
            <div style="text-align: center; padding-top: 100px; color: #666;">
              Performance chart will be displayed here
            </div>
          </div>
        {% else %}
          <div class="no-data">
            <p>No performance data available. Click "Refresh Data" to fetch the latest metrics.</p>
          </div>
        {% endif %}
      </div>

      <div class="dashboard-card">
        <div class="card-title">
          AI Recommendations
          <a href="#" onclick="showRecommendationForm(); return false;">Generate New</a>
        </div>

        <div id="recommendation-form" style="display: none; margin-bottom: 20px; padding: 15px; background-color: #f8f8f8; border-radius: 5px;">
          <form method="post" action="{% url 'gbp_generate_recommendations' selected_location.id %}">
            {% csrf_token %}
            <div style="margin-bottom: 10px;">
              <label for="api_key">GPT API Key:</label>
              <input type="text" name="api_key" id="api_key" style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;" placeholder="Enter your GPT API key" required>
            </div>
            <button type="submit" class="action-button primary">Generate Recommendations</button>
          </form>
        </div>

        <div class="recommendations-list">
          {% if recommendations %}
            {% for rec in recommendations %}
              <div class="recommendation-card {% if rec.impact == 'HIGH' %}high-impact{% elif rec.impact == 'MEDIUM' %}medium-impact{% else %}low-impact{% endif %}" style="margin-bottom: 15px;">
                <div class="recommendation-title">{{ rec.title }}</div>
                <div class="recommendation-meta">
                  Impact: <strong>{{ rec.impact }}</strong> |
                  Effort: <strong>{{ rec.effort }}</strong> |
                  Category: {{ rec.category }}
                </div>
                <div class="recommendation-description">{{ rec.description }}</div>
                {% if rec.action_url %}
                  <div style="margin-top: 10px;">
                    <a href="{{ rec.action_url }}" target="_blank">Take Action</a>
                  </div>
                {% endif %}
              </div>
            {% endfor %}
          {% else %}
            <div class="no-data">
              <p>No recommendations available. Click "Generate New" to create AI-powered recommendations for your business.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  {% else %}
    <div class="no-data" style="padding: 50px; text-align: center;">
      <p>No business locations found. Please connect your Google Business Profile to get started.</p>
      <a href="{% url 'gbp_connect' %}" class="action-button primary" style="margin-top: 20px;">Connect Google Business Profile</a>
    </div>
  {% endif %}
</div>

<script>
  function showRecommendationForm() {
    document.getElementById('recommendation-form').style.display = 'block';
  }
</script>
{% endblock %}
