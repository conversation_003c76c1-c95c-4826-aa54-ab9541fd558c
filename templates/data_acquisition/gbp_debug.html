{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}Google OAuth Debug | LocalSEO Platform{% endblock %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static "css/styles.css" %}">
<style>
  .debug-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 30px;
    background-color: #f8f8f8;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .debug-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ddd;
  }

  .debug-section h2 {
    color: #417690;
    margin-bottom: 15px;
  }

  .debug-item {
    margin-bottom: 10px;
  }

  .debug-label {
    font-weight: bold;
    display: inline-block;
    width: 200px;
  }

  .debug-value {
    font-family: monospace;
    background-color: #f0f0f0;
    padding: 5px;
    border-radius: 3px;
    word-break: break-all;
  }

  .debug-instructions {
    margin-top: 30px;
    padding: 15px;
    background-color: #e8f4f8;
    border-radius: 5px;
  }

  .debug-instructions ol {
    margin-left: 20px;
    margin-top: 10px;
  }

  .debug-instructions li {
    margin-bottom: 10px;
  }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
  <a href="{% url 'business_search' %}">Home</a>
  &rsaquo; Google OAuth Debug
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <div class="debug-container">
    <h1>Google OAuth Debug Information</h1>

    <div class="debug-section">
      <h2>OAuth Client Configuration</h2>

      <div class="debug-item">
        <div class="debug-label">Client ID:</div>
        <div class="debug-value">{{ client_id }}</div>
      </div>

      <div class="debug-item">
        <div class="debug-label">Redirect URI (Client):</div>
        <div class="debug-value">{{ redirect_uri }}</div>
      </div>

      <div class="debug-item">
        <div class="debug-label">Redirect URI (Env):</div>
        <div class="debug-value">{{ env_redirect_uri }}</div>
      </div>

      <div class="debug-item">
        <div class="debug-label">Redirect URI (Auth URL):</div>
        <div class="debug-value">{{ redirect_uri_param }}</div>
      </div>

      <div class="debug-item">
        <div class="debug-label">Scopes:</div>
        <div class="debug-value">{{ scopes|join:", " }}</div>
      </div>

      <div class="debug-item">
        <div class="debug-label">Auth URL:</div>
        <div class="debug-value" style="word-break: break-all;">{{ auth_url }}</div>
      </div>

      <div class="debug-item">
        <div class="debug-label">State:</div>
        <div class="debug-value">{{ state }}</div>
      </div>
    </div>

    <div class="debug-section">
      <h2>Server Information</h2>

      <div class="debug-item">
        <div class="debug-label">Hostname:</div>
        <div class="debug-value">{{ hostname }}</div>
      </div>

      <div class="debug-item">
        <div class="debug-label">Base URL:</div>
        <div class="debug-value">{{ base_url }}</div>
      </div>

      <div class="debug-item">
        <div class="debug-label">Available Callback URLs:</div>
        <div class="debug-value">
          {% for url in callback_urls %}
            {{ url }}<br>
          {% endfor %}
        </div>
      </div>
    </div>

    <div class="debug-instructions">
      <h2>Instructions to Fix Redirect URI Mismatch</h2>

      <p>You're seeing the "Error 400: redirect_uri_mismatch" because the redirect URI used in the authentication request doesn't match any of the authorized redirect URIs in the Google Cloud Console.</p>

      <ol>
        <li>Go to the <a href="https://console.cloud.google.com/apis/credentials" target="_blank">Google Cloud Console Credentials page</a></li>
        <li>Find and click on the OAuth 2.0 Client ID with the name "{{ client_id }}"</li>
        <li>Under "Authorized redirect URIs", make sure the following URI is added:
          <div class="debug-value" style="margin-top: 5px; font-weight: bold; color: #d32f2f;">{{ redirect_uri_param }}</div>
        </li>
        <li>If the above URI is not present, add it and click "Save" to update the OAuth client</li>
        <li>Return to the <a href="{% url 'gbp_connect' %}">Google Business Profile Connect page</a> and try again</li>
      </ol>

      <p><strong>Important:</strong> The redirect URI in the authentication request (shown in red above) MUST match exactly one of the authorized redirect URIs in the Google Cloud Console. Even a small difference (like a trailing slash) will cause the error.</p>

      <p>If you've already added the correct URI to the Google Cloud Console and you're still seeing the error, try the following:</p>

      <ol>
        <li>Clear your browser cookies and cache</li>
        <li>Try using a different browser</li>
        <li>Make sure you're signed in to the correct Google account</li>
        <li>Wait a few minutes for the changes to propagate in Google's systems</li>
      </ol>
    </div>
  </div>
</div>
{% endblock %}
