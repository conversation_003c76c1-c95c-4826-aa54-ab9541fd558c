{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static "admin/css/login.css" %}">
<link rel="stylesheet" type="text/css" href="{% static "css/styles.css" %}">
{{ form.media }}
{% endblock %}

{% block bodyclass %}{{ block.super }} login{% endblock %}

{% block usertools %}{% endblock %}

{% block nav-global %}{% endblock %}

{% block nav-sidebar %}{% endblock %}

{% block content_title %}{% endblock %}

{% block breadcrumbs %}{% endblock %}

{% block content %}
<div id="content-main">
  <h1>LocalSEO Platform</h1>
  <p>Login to access the Local SEO Analysis Platform for Australian businesses.</p>

  {% if form.errors and not form.non_field_errors %}
  <p class="errornote">
    {% if form.errors.items|length == 1 %}{% translate "Please correct the error below." %}{% else %}{% translate "Please correct the errors below." %}{% endif %}
  </p>
  {% endif %}

  {% if form.non_field_errors %}
  {% for error in form.non_field_errors %}
  <p class="errornote">
    {{ error }}
  </p>
  {% endfor %}
  {% endif %}

  <div id="content-main">
    <form action="{{ app_path }}" method="post" id="login-form">{% csrf_token %}
      <div class="form-row">
        {{ form.username.errors }}
        {{ form.username.label_tag }} {{ form.username }}
      </div>
      <div class="form-row">
        {{ form.password.errors }}
        {{ form.password.label_tag }} {{ form.password }}
        <input type="hidden" name="next" value="{{ next }}">
      </div>
      <div class="submit-row">
        <input type="submit" value="{% translate 'Log in' %}">
      </div>
    </form>
  </div>
</div>
{% endblock %}
