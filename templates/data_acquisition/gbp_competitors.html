{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block title %}Competitor Analysis | LocalSEO Platform{% endblock %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static "css/styles.css" %}">
<style>
  .competitor-container {
    margin: 20px 0;
  }

  .competitor-card {
    margin-bottom: 20px;
    padding: 20px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  }

  .competitor-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  .competitor-name {
    font-size: 18px;
    font-weight: bold;
  }

  .competitor-rating {
    display: flex;
    align-items: center;
  }

  .rating-value {
    font-size: 18px;
    font-weight: bold;
    color: #f8a100;
    margin-right: 5px;
  }

  .competitor-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .detail-item {
    margin-bottom: 10px;
  }

  .detail-label {
    font-weight: bold;
    color: #666;
    font-size: 14px;
  }

  .detail-value {
    font-size: 16px;
  }

  .comparison-metrics {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
  }

  .comparison-metric {
    text-align: center;
    flex: 1;
  }

  .metric-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
  }

  .metric-comparison {
    font-size: 16px;
    font-weight: bold;
  }

  .better {
    color: #4CAF50;
  }

  .worse {
    color: #f44336;
  }

  .neutral {
    color: #FFC107;
  }

  .action-buttons {
    margin-top: 20px;
    display: flex;
    gap: 10px;
  }

  .action-button {
    display: inline-block;
    padding: 8px 16px;
    background-color: #79aec8;
    color: white;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    cursor: pointer;
  }

  .action-button:hover {
    background-color: #417690;
    color: white;
  }

  .action-button.primary {
    background-color: #4CAF50;
  }

  .action-button.primary:hover {
    background-color: #3e8e41;
  }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
  <a href="{% url 'data_acquisition:business_search' %}">Home</a>
  &rsaquo; <a href="{% url 'data_acquisition:gbp_dashboard' %}?location_id={{ location.id }}">Google Business Profile Dashboard</a>
  &rsaquo; Competitor Analysis
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <h1>Competitor Analysis</h1>

  <div class="business-header" style="margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #eee;">
    <div>
      <h2>{{ business.name }}</h2>
      <div style="margin-top: 5px;">
        {% if business.google_rating %}
          <span class="rating-value">{{ business.google_rating }} ★</span>
          <span>({{ business.google_reviews_count }} reviews)</span>
        {% endif %}
      </div>
    </div>

    <div class="action-buttons">
      <a href="{% url 'gbp_dashboard' %}?location_id={{ location.id }}" class="action-button">
        Back to Dashboard
      </a>
      <a href="{% url 'gbp_generate_recommendations' location.id %}" class="action-button primary">
        Generate Recommendations
      </a>
    </div>
  </div>

  <div class="competitor-container">
    <h2>Nearby Competitors</h2>

    {% if competitors %}
      {% for competitor in competitors %}
        <div class="competitor-card">
          <div class="competitor-header">
            <div class="competitor-name">{{ competitor.name }}</div>
            <div class="competitor-rating">
              {% if competitor.rating %}
                <span class="rating-value">{{ competitor.rating }} ★</span>
                <span>({{ competitor.reviews_count }} reviews)</span>
              {% else %}
                <span>No rating</span>
              {% endif %}
            </div>
          </div>

          <div class="competitor-details">
            <div class="detail-item">
              <div class="detail-label">Address:</div>
              <div class="detail-value">{{ competitor.address|default:"Not available" }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Phone:</div>
              <div class="detail-value">{{ competitor.phone|default:"Not available" }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Website:</div>
              <div class="detail-value">
                {% if competitor.website %}
                  <a href="{{ competitor.website }}" target="_blank">{{ competitor.website }}</a>
                {% else %}
                  Not available
                {% endif %}
              </div>
            </div>
            <div class="detail-item">
              <div class="detail-label">Categories:</div>
              <div class="detail-value">
                {% if competitor.types %}
                  {{ competitor.types|join:", "|title }}
                {% else %}
                  Not available
                {% endif %}
              </div>
            </div>
          </div>

          <div class="comparison-metrics">
            <div class="comparison-metric">
              <div class="metric-title">Rating Comparison</div>
              <div class="metric-comparison {% if business.google_rating > competitor.rating %}better{% elif business.google_rating < competitor.rating %}worse{% else %}neutral{% endif %}">
                {% if business.google_rating > competitor.rating %}
                  +{{ business.google_rating|floatformat:1|default:0|sub:competitor.rating|floatformat:1 }} ↑
                {% elif business.google_rating < competitor.rating %}
                  {{ business.google_rating|floatformat:1|default:0|sub:competitor.rating|floatformat:1 }} ↓
                {% else %}
                  Equal
                {% endif %}
              </div>
            </div>

            <div class="comparison-metric">
              <div class="metric-title">Reviews Comparison</div>
              <div class="metric-comparison {% if business.google_reviews_count > competitor.reviews_count %}better{% elif business.google_reviews_count < competitor.reviews_count %}worse{% else %}neutral{% endif %}">
                {% if business.google_reviews_count > competitor.reviews_count %}
                  +{{ business.google_reviews_count|default:0|sub:competitor.reviews_count }} ↑
                {% elif business.google_reviews_count < competitor.reviews_count %}
                  {{ business.google_reviews_count|default:0|sub:competitor.reviews_count }} ↓
                {% else %}
                  Equal
                {% endif %}
              </div>
            </div>

            <div class="comparison-metric">
              <div class="metric-title">Distance</div>
              <div class="metric-comparison">
                {% if competitor.distance %}
                  {{ competitor.distance|floatformat:1 }} km
                {% else %}
                  Nearby
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      {% endfor %}
    {% else %}
      <p>No competitors found in your area and category.</p>
    {% endif %}
  </div>
</div>
{% endblock %}
