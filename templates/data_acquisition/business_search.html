{% extends "data_acquisition/base.html" %}
{% load static %}

{% block title %}Business Search | LocalSEO Platform{% endblock %}

{% block content %}
<h1>Local SEO Analysis Platform</h1>

  <div style="margin-bottom: 20px; padding: 20px; background-color: #e8f4f8; border-radius: 5px; display: flex; justify-content: space-between; align-items: center;">
    <div>
      <h2 style="margin-top: 0;">Google Business Profile Integration</h2>
      <p>Connect your Google Business Profile to access powerful insights and AI-powered recommendations.</p>
    </div>
    <div>
      <a href="{% url 'gbp_connect' %}" style="display: inline-block; padding: 10px 20px; background-color: #4285F4; color: white; border-radius: 4px; text-decoration: none; font-weight: bold;">
        Connect Google Business Profile
      </a>
    </div>
  </div>

  <div class="collect-container">
    <h2>Collect Business Data</h2>
    <p>Use this form to collect new business data from Google Places API.</p>

    <form class="collect-form" action="{% url 'data_acquisition:collect_business_data' %}" method="post">
      {% csrf_token %}
      <input type="text" name="query" placeholder="Business type or keyword" required>
      <input type="text" name="location" placeholder="Latitude,Longitude (e.g., -33.8688,151.2093)">
      <input type="number" name="radius" placeholder="Radius (meters)" value="5000">
      <input type="number" name="max_results" placeholder="Max results" value="20">
      <input type="text" name="api_key" placeholder="Google Places API Key (required)" style="width: 300px;">
      <button type="submit">Collect Data</button>
    </form>

    <div style="margin-top: 10px; font-size: 14px; color: #666;">
      <strong>Note:</strong> You must provide a valid Google Places API key.
      <a href="https://console.cloud.google.com/" target="_blank">Get a key from Google Cloud Console</a> and make sure the Places API is enabled.
      <a href="{% url 'data_acquisition:test_api_key' %}?api_key=YOUR_API_KEY_HERE" target="_blank">Test your API key</a> (replace YOUR_API_KEY_HERE with your actual key).
    </div>
  </div>

  <div class="collect-container" style="background-color: #f4f8e8;">
    <h2>Lookup Business by Google Place ID</h2>
    <p>Use this form to look up a specific business by its Google Place ID.</p>

    <form class="collect-form" action="{% url 'data_acquisition:lookup_place_details' %}" method="get" target="_blank">
      <input type="text" name="place_id" placeholder="Google Place ID (e.g., ChIJ_wvuwJ-iEmsRAsIW9cBQ5KM)" required style="width: 400px;">
      <input type="text" name="api_key" placeholder="Google Places API Key (required)" style="width: 300px;">
      <label style="margin-left: 10px;">
        <input type="checkbox" name="update_db" value="true"> Update database with details
      </label>
      <button type="submit">Lookup Business</button>
    </form>

    <div style="margin-top: 10px; font-size: 14px; color: #666;">
      <strong>Example:</strong> For the business with Place ID "ChIJ_wvuwJ-iEmsRAsIW9cBQ5KM", this will show all available details from Google.
    </div>
  </div>

  <div class="search-container">
    <h2>Search Businesses</h2>

    <form class="search-form" method="get" action="{% url 'data_acquisition:business_search' %}">
      <input type="text" name="query" placeholder="Business name" value="{{ query }}">
      <select name="category">
        <option value="">All Categories</option>
        {% for cat in categories %}
        <option value="{{ cat.name }}" {% if category == cat.name %}selected{% endif %}>{{ cat.name }}</option>
        {% endfor %}
      </select>
      <input type="text" name="location" placeholder="City, State, or Postcode" value="{{ location }}">

      <select name="domain_filter">
        <option value="" {% if domain_filter == '' %}selected{% endif %}>All Domains</option>
        <option value="com_au" {% if domain_filter == 'com_au' %}selected{% endif %}>.com.au Domains</option>
        <option value="au" {% if domain_filter == 'au' %}selected{% endif %}>.au Domains</option>
        <option value="has_website" {% if domain_filter == 'has_website' %}selected{% endif %}>Has Website</option>
      </select>

      <button type="submit">Search</button>
    </form>

    <div style="margin-top: 10px; font-size: 14px; color: #666;">
      <strong>Statistics:</strong>
      {{ total_count }} total businesses |
      {{ with_website_count }} with websites |
      {{ com_au_count }} with .com.au domains
    </div>
  </div>

  <div class="business-list">
    <h2>Results ({{ businesses|length }})</h2>

    {% if businesses %}
      {% for business in businesses %}
        <div class="business-card">
          <div class="business-name">
            <a href="{% url 'data_acquisition:business_detail' business.id %}">{{ business.name|default:"Unnamed Business" }}</a>
            {% if business.google_rating %}
              <span class="business-rating">{{ business.google_rating }} ★</span>
            {% endif %}
            <span style="font-size: 12px; color: #666; margin-left: 5px;">(ID: {{ business.id }})</span>
          </div>
          <div class="business-details">
            {% if business.street_address %}
              <div>{{ business.street_address }}, {{ business.city }} {{ business.state }} {{ business.postcode }}</div>
            {% endif %}
            {% if business.phone %}
              <div>Phone: {{ business.phone }}</div>
            {% endif %}
            {% if business.website %}
              <div>
                Website:
                <a href="{{ business.website }}" target="_blank">{{ business.website }}</a>
                {% if ".com.au" in business.website %}
                  <span style="background-color: #e8f4f8; padding: 2px 5px; border-radius: 3px; font-size: 12px; margin-left: 5px;">.com.au</span>
                {% elif ".au" in business.website %}
                  <span style="background-color: #f4f8e8; padding: 2px 5px; border-radius: 3px; font-size: 12px; margin-left: 5px;">.au</span>
                {% endif %}
              </div>
            {% endif %}
            {% if business.google_reviews_count %}
              <div>{{ business.google_reviews_count }} reviews on Google</div>
            {% endif %}
          </div>
        </div>
      {% endfor %}
    {% else %}
      <p>No businesses found matching your criteria.</p>
    {% endif %}
  </div>
{% endblock %}
