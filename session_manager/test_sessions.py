from django.test import TestCase, RequestFactory, override_settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import ValidationError
from rest_framework.test import APITestCase, force_authenticate
from rest_framework import status
from unittest.mock import patch, MagicMock
import uuid
import json
from datetime import timedel<PERSON>, datetime
from .models import Session
from .services import SessionManager
from .views import SessionViewSet
from .serializers import SessionSerializer

User = get_user_model()

class SessionIntegrationTests(APITestCase):
    """Integration tests for the Session management system"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.factory = RequestFactory()
        self.request = self.factory.get('/')
        self.request.META = {
            'REMOTE_ADDR': '***********',
            'HTTP_USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

    def test_create_session(self):
        # Test session creation
        pass

    def test_validate_session(self):
        # Test session validation
        pass

    def test_expire_session(self):
        # Test session expiration
        pass

    def test_concurrent_sessions(self):
        """Test handling multiple active sessions for the same user"""
        # Override the max concurrent sessions setting for this test
        with self.settings(SESSION_MAX_CONCURRENT=10):
            # Create multiple sessions
            sessions = []
            for i in range(5):
                session = SessionManager.create_session(
                    self.user,
                    self.request,
                    expiry_hours=24
                )
                sessions.append(session)

            # Verify all sessions are active
            active_sessions = SessionManager.get_active_sessions(self.user)
            self.assertEqual(active_sessions.count(), 5)

            # End one session
            SessionManager.end_session(sessions[0].id)
            active_sessions = SessionManager.get_active_sessions(self.user)
            self.assertEqual(active_sessions.count(), 4)

    def test_session_expiry_behavior(self):
        """Test session expiration behavior"""
        current_time = timezone.now()
        
        # Create session with short expiry
        with patch('django.utils.timezone.now') as mock_now:
            mock_now.return_value = current_time
            session = SessionManager.create_session(
                self.user,
                self.request,
                expiry_hours=1/60  # 1 minute
            )

            # Verify session is active initially
            self.assertTrue(session.is_active)
            self.assertFalse(session.is_expired())

            # Fast forward time to after expiration
            mock_now.return_value = current_time + timedelta(minutes=2)
            self.assertTrue(session.is_expired())
            
            # Cleanup should deactivate expired sessions
            cleaned = SessionManager.cleanup_expired_sessions()
            self.assertEqual(cleaned, 1)

            session.refresh_from_db()
            self.assertFalse(session.is_active)


    def test_max_concurrent_sessions(self):
        """Test handling maximum concurrent sessions limit"""
        current_time = timezone.now()
        with patch('django.utils.timezone.now') as mock_now:
            mock_now.return_value = current_time
            # Create maximum allowed sessions
            sessions = []
            for _ in range(3):
                session = SessionManager.create_session(self.user, self.request)
                sessions.append(session)
            # Verify we have max sessions
            active_sessions = SessionManager.get_active_sessions(self.user)
            self.assertEqual(active_sessions.count(), 3)
            # Try to create one more session
            new_session = SessionManager.create_session(self.user, self.request)
            # Should still have only max allowed sessions (oldest should be ended)
            active_sessions = SessionManager.get_active_sessions(self.user)
            self.assertEqual(active_sessions.count(), 3)
            self.assertTrue(new_session in active_sessions)
            # Verify the oldest session was ended
            sessions[0].refresh_from_db()
            self.assertFalse(sessions[0].is_active)


    def test_session_device_tracking(self):
        """Test device information tracking in sessions"""
        # Test with desktop User-Agent
        desktop_request = self.factory.get('/')
        desktop_request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        desktop_session = SessionManager.create_session(self.user, desktop_request)

        self.assertEqual(desktop_session.device_info['is_mobile'], False)
        self.assertEqual(desktop_session.device_info['is_pc'], True)

        # Test with mobile User-Agent
        mobile_request = self.factory.get('/')
        mobile_request.META['HTTP_USER_AGENT'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
        mobile_session = SessionManager.create_session(self.user, mobile_request)

        self.assertEqual(mobile_session.device_info['is_mobile'], True)
        self.assertEqual(mobile_session.device_info['is_pc'], False)

    def test_session_security(self):
        """Test session security features"""
        # Create a session
        session = SessionManager.create_session(self.user, self.request)
        
        # Set up the view
        view = SessionViewSet()
        view.format_kwarg = None
        view.action_map = {'post': 'end'}  # Add this line
        view.kwargs = {'pk': session.id}    # Add this line

        # Another user shouldn't be able to end someone else's session
        request = self.factory.post(f'/api/sessions/{session.id}/end/')
        force_authenticate(request, user=self.other_user)
        
        response = view.end(request, pk=session.id)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # Session should still be active
        session.refresh_from_db()
        self.assertTrue(session.is_active)

        # Original user should be able to end the session
        request = self.factory.post(f'/api/sessions/{session.id}/end/')
        force_authenticate(request, user=self.user)
        
        response = view.end(request, pk=session.id)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Session should now be inactive
        session.refresh_from_db()
        self.assertFalse(session.is_active)

    def test_session_serialization(self):
        """Test session serialization and deserialization"""
        session = SessionManager.create_session(self.user, self.request)
        serializer = SessionSerializer(session)
        data = serializer.data

        # Verify all expected fields are present
        expected_fields = {
            'id', 'ip_address', 'user_agent', 'last_activity',
            'expiry_time', 'is_active', 'device_info', 'created_at'
        }
        self.assertEqual(set(data.keys()), expected_fields)

        # Verify sensitive data is not exposed
        self.assertNotIn('user', data)

    def test_invalid_session_operations(self):
        """Test handling of invalid session operations"""
        # Test with non-existent session ID
        fake_uuid = uuid.uuid4()
        self.assertFalse(SessionManager.end_session(fake_uuid))

        # Test with invalid UUID
        with self.assertRaises(ValidationError):
            SessionManager.get_session('not-a-uuid')

        # Test creating session with invalid request
        invalid_request = self.factory.get('/')
        invalid_request.META = {}  # Empty META
        session = SessionManager.create_session(self.user, invalid_request)
        self.assertIsNotNone(session)  # Should handle missing META gracefully

    def test_session_cleanup_performance(self):
        """Test performance of session cleanup with large number of sessions"""
        # Create many expired sessions
        batch_size = 1000
        expired_time = timezone.now() - timedelta(days=1)
        
        Session.objects.bulk_create([
            Session(
                user=self.user,
                ip_address='127.0.0.1',
                user_agent='test',
                expiry_time=expired_time,
                device_info={},
                is_active=True
            ) for _ in range(batch_size)
        ])

        # Measure cleanup performance
        import time
        start_time = time.time()
        cleaned = SessionManager.cleanup_expired_sessions()
        end_time = time.time()

        self.assertEqual(cleaned, batch_size)
        self.assertLess(end_time - start_time, 2.0)  # Should complete within 2 seconds


    def test_concurrent_access(self):
        import threading
        import time
        from django.db import transaction, connections
        from concurrent.futures import ThreadPoolExecutor

        # Create session outside of any transaction to ensure it's committed
        session = SessionManager.create_session(self.user, self.request)
        session_id = session.id
        
        # Give a moment for the database to complete the transaction
        time.sleep(0.2)
        
        # Verify the session exists before starting concurrent access
        verification_session = Session.objects.get(id=session_id)
        self.assertTrue(verification_session.is_active)

        successful_attempts = 0
        lock = threading.Lock()
        failed_attempts = []

        def access_session():
            nonlocal successful_attempts
            # Close any existing connections
            for conn in connections.all():
                conn.close_if_unusable_or_obsolete()

            try:
            # Get the session without locking it first
                session = Session.objects.get(id=session_id)
                if session.is_active:
                    with lock:
                        successful_attempts += 1
                    return True
                return False
            except Session.DoesNotExist:
                failed_attempts.append("Session not found")
                return False
            except Exception as e:
                failed_attempts.append(f"Error: {str(e)}")
                return False
            
        # Use a smaller number of concurrent operations
        num_threads = 10
        with ThreadPoolExecutor(max_workers=5) as executor:
            results = list(executor.map(lambda _: access_session(), range(num_threads)))

        self.assertEqual(successful_attempts, num_threads, 
                        f"Expected {num_threads} successful attempts, got {successful_attempts}")
        
        # If test fails, provide detailed information
        if not all(results):
            self.fail(f"""
    Concurrent access test failed:
    - Total attempts: {num_threads}
    - Successful: {sum(results)}
    - Failed: {len(results) - sum(results)}
    - Failure details: {failed_attempts}
    """)



            
