from django.shortcuts import render

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import Session
from .serializers import SessionSerializer
from .services import SessionManager


class SessionViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = SessionSerializer

    def get_queryset(self):
        return Session.objects.filter(user=self.request.user)

    def create(self, request):
        session = SessionManager.create_session(request.user, request)
        serializer = self.get_serializer(session)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['get'])
    def active(self, request):
        sessions = SessionManager.get_active_sessions(request.user)
        serializer = self.get_serializer(sessions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def end(self, request, pk=None):
        try:
            # wrap it in DRF's Request (so .user is populated by your auth classes)
            self.request = self.initialize_request(request)
            
            session = self.get_queryset().get(id=pk, is_active=True)
            session.end_session()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Session.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def end_all(self, request):
        SessionManager.end_all_sessions(request.user)
        return Response(status=status.HTTP_204_NO_CONTENT)
