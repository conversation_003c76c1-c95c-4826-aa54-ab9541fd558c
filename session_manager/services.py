from django.utils import timezone
from django.conf import settings
from .models import Session
import user_agents


class SessionManager:
    @staticmethod
    def create_session(user, request, expiry_hours=24):
        """Create a new session for the user"""
        ua_string = request.META.get('HTTP_USER_AGENT', '')
        user_agent = user_agents.parse(ua_string)
        
        device_info = {
            'browser': user_agent.browser.family,
            'browser_version': user_agent.browser.version_string,
            'os': user_agent.os.family,
            'device': user_agent.device.family,
            'is_mobile': user_agent.is_mobile,
            'is_tablet': user_agent.is_tablet,
            'is_pc': user_agent.is_pc,
        }

        current_time = timezone.now()
        expiry_time = current_time + timezone.timedelta(hours=expiry_hours)

        # Check for max concurrent sessions
        max_sessions = getattr(settings, 'SESSION_MAX_CONCURRENT', 3)
        active_sessions = Session.objects.filter(
            user=user,
            is_active=True,
            expiry_time__gte=timezone.now()
        ).order_by('-created_at')

        # If we're at or over the limit, end the oldest session before creating new one
        if active_sessions.count() >= max_sessions:
            oldest_session = active_sessions.last()
            if oldest_session:
                oldest_session.end_session()

        session = Session.objects.create(
            user=user,
            ip_address=request.META.get('REMOTE_ADDR', '0.0.0.0'),
            user_agent=ua_string,
            expiry_time=expiry_time,
            device_info=device_info
        )
        return session

    @staticmethod
    def get_active_sessions(user):
        """Get all active sessions for a user"""
        return Session.objects.filter(
            user=user,
            is_active=True,
            expiry_time__gt=timezone.now()
        )

    @staticmethod
    def get_session(session_id):
        """Get a specific session by ID"""
        try:
            return Session.objects.get(id=session_id)
        except Session.DoesNotExist:
            return None

    @staticmethod
    def end_session(session_id):
        """End a specific session"""
        try:
            session = Session.objects.get(id=session_id, is_active=True)
            session.end_session()
            return True
        except Session.DoesNotExist:
            return False

    @staticmethod
    def end_all_sessions(user):
        """End all sessions for a user"""
        Session.objects.filter(user=user, is_active=True).update(
            is_active=False
        )

    @staticmethod
    def cleanup_expired_sessions():
        """Clean up expired sessions"""
        return Session.objects.filter(
            expiry_time__lt=timezone.now()
        ).update(is_active=False)
