from django.db import models

from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class Session(models.Model):

    
    def is_valid(self):
        return self.is_active and not self.is_expired()
    
    def refresh_activity(self):
        self.last_activity = timezone.now()
        self.save(update_fields=['last_activity'])
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sessions'
    )
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    last_activity = models.DateTimeField(auto_now=True)
    expiry_time = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    device_info = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-last_activity']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expiry_time']),
        ]

    def __str__(self):
        return f"Session {self.id} - {self.user.email}"

    def is_expired(self):
        return timezone.now() > self.expiry_time

    def extend_session(self, hours=24):
        self.expiry_time = timezone.now() + timezone.timedelta(hours=hours)
        self.save()

    def end_session(self):
        self.is_active = False
        self.save()
