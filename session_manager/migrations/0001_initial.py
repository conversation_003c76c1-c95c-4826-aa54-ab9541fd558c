# Generated by Django 5.2 on 2025-04-29 03:04

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Session',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('expiry_time', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('device_info', models.J<PERSON>NField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_activity'],
                'indexes': [models.Index(fields=['user', 'is_active'], name='session_man_user_id_31bce4_idx'), models.Index(fields=['expiry_time'], name='session_man_expiry__3ff9da_idx')],
            },
        ),
    ]
