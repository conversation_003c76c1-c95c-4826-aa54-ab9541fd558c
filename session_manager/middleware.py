from django.utils import timezone
from .models import Session

class SessionMaintenanceMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.user.is_authenticated:
            # Update last activity
            Session.objects.filter(
                user=request.user,
                is_active=True,
                expiry_time__gt=timezone.now()
            ).update(last_activity=timezone.now())
            
            # Cleanup expired sessions periodically
            if random.random() < 0.01:  # 1% chance to run cleanup
                Session.objects.filter(
                    expiry_time__lt=timezone.now()
                ).update(is_active=False)

        response = self.get_response(request)
        return response