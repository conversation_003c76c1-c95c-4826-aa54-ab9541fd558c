from rest_framework import serializers
from .models import Session


class SessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Session
        fields = [
            'id',
            'ip_address',
            'user_agent',
            'last_activity',
            'expiry_time',
            'is_active',
            'device_info',
            'created_at'
        ]
        read_only_fields = fields