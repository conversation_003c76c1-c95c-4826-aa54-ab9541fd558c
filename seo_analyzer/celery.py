import os
from celery import Celery


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'seo_analyzer.settings')


app = Celery('seo_analyzer')


app.conf.update(
    
    broker_connection_retry=True,
    broker_connection_retry_on_startup=True,
    broker_connection_max_retries=10,
    
    
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    
    
    timezone='UTC',
    enable_utc=True,
    
    
    worker_hijack_root_logger=False,
    worker_prefetch_multiplier=1,
    
    
    task_track_started=True,
    task_ignore_result=False,
    
    
    worker_redirect_stdouts=False,
)


app.config_from_object('django.conf:settings', namespace='CELERY')


app.autodiscover_tasks()
