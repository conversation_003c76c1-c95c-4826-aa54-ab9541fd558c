from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.sitemaps.views import sitemap
from blog.sitemaps import PostSitemap, CategorySitemap, StaticSitemap
from django.views.generic.base import TemplateView
from blog.urls import api_urlpatterns as blog_api_urls
from blog.views import CKEditorUploadView
from accounts.views import StripePricingListView
import logging

logger = logging.getLogger(__name__)

sitemaps = {
    'posts': PostSitemap,
    'categories': CategorySitemap,
    'static': StaticSitemap,
}

urlpatterns = [
    path('admin/', admin.site.urls),
    path('blog/', include('blog.urls', namespace='blog')),  # Frontend URLs with namespace
    path('api/blog/', include(blog_api_urls)),  # API URLs without namespace
    path('api/', include(('tools.urls', 'tools'), namespace='api')),
    path('api/accounts/', include(('accounts.urls', 'accounts'), namespace='accounts')),
    path('api/accounts/google/', include('accounts.oauth_urls')),
    path('api/sessions/', include('session_manager.urls')),
    path('api/pricing/', StripePricingListView.as_view(), name='stripe_pricing_list_api'),

    # LocalSEO URLs
    path('localseo/', include('data_acquisition.urls')),

    path('sitemap.xml', sitemap, {'sitemaps': sitemaps},
         name='django.contrib.sitemaps.views.sitemap'),
    path('robots.txt', TemplateView.as_view(template_name="robots.txt",
         content_type="text/plain")),
    # path('django_ckeditor_5/', include('django_ckeditor_5.urls')), # Comment out or remove if using custom
    # path('upload/', include('django_ckeditor_5.urls')), # Comment out or remove this line
    re_path(r'^api/upload/image_upload/?$', CKEditorUploadView.as_view(), name='ck_editor_5_upload_file'), # Add /api/ prefix
    path('api/contact/', include('contact.urls')),
    *static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT),
]


for pattern in urlpatterns:
    if hasattr(pattern, 'pattern'):
        logger.info(f"Registered URL pattern: {pattern.pattern}")
    else:
        logger.info(f"Registered URL pattern (static): {pattern}")
