from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from rest_framework import viewsets, permissions, filters
from rest_framework.decorators import action, api_view
from rest_framework.response import Response
from .models import Post, Category
from .serializers import PostSerializer, PostCreateUpdateSerializer, CategorySerializer
from .forms import EmailPostForm
from django.core.mail import send_mail
from django.utils.text import slugify
from rest_framework.pagination import PageNumberPagination
from django.db.models import Count
from taggit.models import Tag
from django.shortcuts import render
from .documents import PostDocument
from .forms import SearchForm
from elasticsearch_dsl import Q
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
import os
from django.conf import settings
import uuid
from bs4 import BeautifulSoup

# API Views
class PostPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class PostViewSet(viewsets.ModelViewSet):
    serializer_class = PostSerializer
    pagination_class = PostPagination
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'body']
    ordering_fields = ['publish', 'title']
    ordering = ['-publish']
    lookup_field = 'slug'

    def get_queryset(self):
        queryset = Post.published.all()
        category_slug = self.kwargs.get('category_slug')
        if category_slug:
            category = get_object_or_404(Category, slug=category_slug)
            queryset = queryset.filter(category=category)
        return queryset

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        categories = Category.objects.all()
        category_serializer = CategorySerializer(categories, many=True)
        response.data['categories'] = category_serializer.data
        return response

    def perform_create(self, serializer):
        serializer.save(author=self.request.user)

    @action(detail=True)
    def similar(self, request, pk=None):
        """
        Returns similar posts for a given post
        """
        post = self.get_object()
        similar_posts = post.get_similar_posts(limit=5)  # Get up to 5 similar posts
        serializer = PostSerializer(similar_posts, many=True)
        return Response(serializer.data)

# Template Views
class PostListView(ListView):
    model = Post
    context_object_name = 'posts'
    paginate_by = 3
    template_name = 'blog/post/list.html'

    def get_queryset(self):
        queryset = Post.published.all()
        
        category_slug = self.kwargs.get('category_slug')
        if category_slug:
            category = get_object_or_404(Category, slug=category_slug)
            queryset = queryset.filter(category=category)
            
        tag_slug = self.kwargs.get('tag_slug')
        if tag_slug:
            tag = get_object_or_404(Tag, slug=tag_slug)
            queryset = queryset.filter(tags__in=[tag])
            
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        context['tags'] = Tag.objects.all()
        
        tag_slug = self.kwargs.get('tag_slug')
        if tag_slug:
            context['tag'] = get_object_or_404(Tag, slug=tag_slug)
            
        category_slug = self.kwargs.get('category_slug')
        if category_slug:
            context['category'] = get_object_or_404(Category, slug=category_slug)
            
        return context

def post_detail(request, category_slug, post_slug):
    post = get_object_or_404(Post,
                            status=Post.Status.PUBLISHED,
                            category__slug=category_slug,
                            slug=post_slug)
    
    similar_posts = post.get_similar_posts()
    
    return render(request, 
                 'blog/post/detail.html',
                 {'post': post,
                  'similar_posts': similar_posts,
                  'categories': Category.objects.all(),
                  'tags': Tag.objects.all()})

def post_share(request, post_id):
    post = get_object_or_404(Post, id=post_id, status=Post.Status.PUBLISHED)
    sent = False

    if request.method == 'POST':
        form = EmailPostForm(request.POST)
        if form.is_valid():
            cd = form.cleaned_data
            # ... email sending logic ...
            sent = True
    else:
        form = EmailPostForm()
    return render(request, 'blog/post/share.html', {'post': post, 'form': form, 'sent': sent})

def post_search(request):
    form = SearchForm()
    results = []
    total_hits = 0
    
    if 'query' in request.GET:
        form = SearchForm(request.GET)
        if form.is_valid():
            query = form.cleaned_data['query']
            if query:
                # Multi-match search across multiple fields with different weights
                q = Q(
                    'multi_match',
                    query=query,
                    fields=['title^3', 'body', 'category.name^2', 'tags^2'],
                    fuzziness='AUTO'
                )
                
                search = PostDocument.search().query(q)
                response = search.execute()
                
                # Get total number of hits
                total_hits = response.hits.total.value if hasattr(response.hits.total, 'value') else response.hits.total
                
                # Get results
                results = response.hits
    
    return render(
        request,
        'blog/post/search.html',
        {
            'form': form,
            'query': request.GET.get('query', ''),
            'results': results,
            'total_hits': total_hits,
            'categories': Category.objects.all(),  # Add this to provide categories for the sidebar
        }
    )

@api_view(['GET'])
def api_post_search(request):
    query = request.GET.get('q', '')
    results = []
    
    if query:
        q = Q(
            'multi_match',
            query=query,
            fields=['title^3', 'body', 'category.name^2', 'tags^2'],
            fuzziness='AUTO'
        )
        
        search = PostDocument.search().query(q)
        response = search.execute()
        
        # Convert Elasticsearch results to a list of dictionaries
        results = []
        for hit in response:
            # Convert tags from AttrList to a regular Python list
            tags = list(hit.tags) if hasattr(hit, 'tags') else []
            
            # Convert publish datetime to timestamp
            publish_timestamp = int(hit.publish.timestamp()) if hasattr(hit, 'publish') else 0
            
            # Extract structured blocks
            blocks = extract_blocks_from_html(hit.body)
        
            # Prepare snippet
            soup = BeautifulSoup(hit.body, 'html.parser')
            text_content = soup.get_text()
            snippet = text_content[:200] + '...' if len(text_content) > 200 else text_content

            cover_image_url = None
            if hasattr(hit, 'cover_image') and hit.cover_image:
                if hit.cover_image.startswith('http'): # Already a full URL
                    cover_image_url = hit.cover_image
                else:
                    cover_image_url = f"https://seoanalyser.com.au{hit.cover_image}"

            results.append({
                'id': hit.id,
                'title': hit.title,
                'slug': hit.slug,
                'category': {
                    'name': hit.category.name if hasattr(hit.category, 'name') else '',
                    'slug': hit.category.slug if hasattr(hit.category, 'slug') else ''
                },
                'author': hit.author.username if hasattr(hit.author, 'username') else hit.author.email,
                'publish_timestamp': publish_timestamp,
                'cover_image': cover_image_url,
                'snippet': snippet,
                'tags': tags,
                'url': f"/blog/{hit.category.slug if hasattr(hit.category, 'slug') else ''}/{hit.slug}/"
            })
    
    return Response({
        'query': query,
        'results': results,
        'count': len(results)
    })

def extract_blocks_from_html(html):
    """
    Extract content blocks from HTML
    """
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(html, 'html.parser')
    
    blocks = []
    
    # Process headings
    for i in range(1, 7):
        for heading in soup.find_all(f'h{i}'):
            blocks.append({
                'type': 'heading',
                'level': i,
                'text': heading.get_text()
            })
            
    # Process paragraphs
    for p in soup.find_all('p'):
        blocks.append({
            'type': 'paragraph',
            'text': p.get_text()
        })
        
    # Process images
    for img in soup.find_all('img'):
        blocks.append({
            'type': 'image',
            'src': img.get('src', ''),
            'alt': img.get('alt', '')
        })
        
    # Process lists
    for ul in soup.find_all('ul'):
        items = [li.get_text() for li in ul.find_all('li')]
        blocks.append({
            'type': 'unordered-list',
            'items': items
        })
        
    for ol in soup.find_all('ol'):
        items = [li.get_text() for li in ol.find_all('li')]
        blocks.append({
            'type': 'ordered-list',
            'items': items
        })
    
    return blocks

@method_decorator(csrf_exempt, name='dispatch')
class CKEditorUploadView(View):
    def post(self, request):
        try:
            uploaded_file = request.FILES.get('upload')
            
            if not uploaded_file:
                return JsonResponse({'error': 'No file uploaded'}, status=400)
            
            # Generate a unique filename
            file_extension = os.path.splitext(uploaded_file.name)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            
            # Save the file
            upload_path = os.path.join(settings.MEDIA_ROOT, settings.CKEDITOR_5_UPLOAD_PATH)
            os.makedirs(upload_path, exist_ok=True)
            
            file_path = os.path.join(upload_path, unique_filename)
            
            with open(file_path, 'wb+') as destination:
                for chunk in uploaded_file.chunks():
                    destination.write(chunk)
            
            # Return the URL to the uploaded file
            file_url = f"{settings.MEDIA_URL}{settings.CKEDITOR_5_UPLOAD_PATH}{unique_filename}"
            
            return JsonResponse({
                'url': file_url,
                'uploaded': '1',
                'fileName': unique_filename
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
