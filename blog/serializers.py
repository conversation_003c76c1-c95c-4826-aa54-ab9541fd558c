from rest_framework import serializers
from .models import Post, Category
from django.contrib.auth import get_user_model
from taggit.serializers import (TagListSerializerField, TaggitSerializer)
import json
import re

class AuthorSerializer(serializers.ModelSerializer):
    display_name = serializers.SerializerMethodField()
    
    class Meta:
        model = get_user_model()
        fields = ['id', 'email', 'display_name']
    
    def get_display_name(self, obj):
        # Return the part of the email before the @ symbol as a display name
        return obj.email.split('@')[0] if obj.email else ''

class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['name', 'slug']

class PostListSerializer(TaggitSerializer, serializers.ModelSerializer):
    """A simplified serializer for listing posts without recursive fields"""
    author = AuthorSerializer(read_only=True)
    tags = TagListSerializerField()
    publish_timestamp = serializers.SerializerMethodField()
    
    class Meta:
        model = Post
        fields = ['id', 'title', 'slug', 'author', 'publish_timestamp', 'tags']
        
    def get_publish_timestamp(self, obj):
        return int(obj.publish.timestamp())

class PostSerializer(TaggitSerializer, serializers.ModelSerializer):
    author = AuthorSerializer(read_only=True)
    url = serializers.URLField(source='get_absolute_url', read_only=True)
    tags = TagListSerializerField()
    similar_posts = serializers.SerializerMethodField()
    publish_timestamp = serializers.SerializerMethodField()
    snippet = serializers.SerializerMethodField()
    
    class Meta:
        model = Post

        fields = ['id', 'title', 'slug', 'author', 'body',
                 'publish_timestamp', 'status', 'snippet', 'cover_image',
                 'url', 'tags', 'similar_posts']

    def get_similar_posts(self, obj):
        """
        Returns serialized similar posts using a simplified serializer to avoid recursion
        """
        similar_posts = obj.get_similar_posts()
        return PostListSerializer(similar_posts, many=True, context=self.context).data
        
    def get_publish_timestamp(self, obj):
        return int(obj.publish.timestamp())
        
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if 'body' in representation and representation['body']:
            # Replace relative /media/ URLs with absolute URLs
            representation['body'] = re.sub(
                r'(<img[^>]*src=")/media/',
                r'\1https://seoanalyser.com.au/media/',
                representation['body']
            )
        return representation
        
    def get_snippet(self, obj):
        text_content = self._extract_text_from_html(obj.body)
        snippet_length = 200
        if len(text_content) > snippet_length:
            return text_content[:snippet_length] + '...'
        return text_content
        
    def _extract_text_from_html(self, html):
        """Extract plain text from HTML content"""
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html, 'html.parser')
        return soup.get_text()
        
    def _extract_blocks_from_html(self, html):
        """
        Extract content blocks from HTML
        This is a simplified example - you may need to adjust based on your needs
        """
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html, 'html.parser')
        
        blocks = []
        
        # Process headings
        for i in range(1, 7):
            for heading in soup.find_all(f'h{i}'):
                blocks.append({
                    'type': 'heading',
                    'level': i,
                    'text': heading.get_text()
                })
                
        # Process paragraphs
        for p in soup.find_all('p'):
            blocks.append({
                'type': 'paragraph',
                'text': p.get_text()
            })
            
        # Process images
        for img in soup.find_all('img'):
            blocks.append({
                'type': 'image',
                'src': img.get('src', ''),
                'alt': img.get('alt', '')
            })
            
        # Process lists
        for ul in soup.find_all('ul'):
            items = [li.get_text() for li in ul.find_all('li')]
            blocks.append({
                'type': 'unordered-list',
                'items': items
            })
            
        for ol in soup.find_all('ol'):
            items = [li.get_text() for li in ol.find_all('li')]
            blocks.append({
                'type': 'ordered-list',
                'items': items
            })
        
        return blocks

class PostCreateUpdateSerializer(TaggitSerializer, serializers.ModelSerializer):
    tags = TagListSerializerField()
    
    class Meta:
        model = Post
        fields = ['title', 'body', 'status', 'tags']
