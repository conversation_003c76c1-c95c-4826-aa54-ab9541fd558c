from django.core.management.base import BaseCommand
from django_elasticsearch_dsl.registries import registry

class Command(BaseCommand):
    help = 'Rebuild Elasticsearch index'

    def handle(self, *args, **options):
        self.stdout.write('Rebuilding index...')
        
        # Delete all indices
        for index in registry.get_indices():
            self.stdout.write(f'Deleting index {index._name}')
            index.delete(ignore=404)
        
        # Create indices and populate them
        for index in registry.get_indices():
            self.stdout.write(f'Creating index {index._name}')
            index.create()
            
        # Populate indices
        for doc in registry.get_documents():
            self.stdout.write(f'Indexing {doc._index._name}')
            qs = doc().get_queryset()
            self.stdout.write(f'Found {qs.count()} records to index')
            doc().update(qs)
            
        self.stdout.write(self.style.SUCCESS('Successfully rebuilt index'))
