from django.urls import path
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'blog'

# Frontend URL patterns
frontend_urlpatterns = [
    path('', views.PostListView.as_view(), name='post_list'),
    path('tag/<slug:tag_slug>/', views.PostListView.as_view(), name='post_list_by_tag'),
    path('<slug:category_slug>/<slug:post_slug>/', views.post_detail, name='post_detail'),
    path('<int:post_id>/share/', views.post_share, name='post_share'),
    path('search/', views.post_search, name='post_search'),
]

# API URL patterns using DRF router
router = DefaultRouter()
router.register(r'posts', views.PostViewSet, basename='post')

# API URL patterns
api_urlpatterns = router.urls + [
    path('search/', views.api_post_search, name='api_post_search'),
    path('category/<slug:category_slug>/', views.PostViewSet.as_view({'get': 'list'}), name='api_post_list_by_category'),
]

# Use frontend_urlpatterns as the main urlpatterns
urlpatterns = frontend_urlpatterns
