from django import forms
from .models import Post
from django_ckeditor_5.widgets import CKEditor5Widget

class PostAdminForm(forms.ModelForm):
    class Meta:
        model = Post
        fields = '__all__'
        widgets = {
            'body': CKEditor5Widget(
                attrs={'class': 'django_ckeditor_5'},
                config_name='default'
            )
        }
    
class EmailPostForm(forms.Form):
    name = forms.CharField(max_length=25)
    email = forms.EmailField()
    to = forms.EmailField()

class SearchForm(forms.Form):
    query = forms.CharField(
        label='Search',
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Search posts...'}),
        required=False
    )
    
