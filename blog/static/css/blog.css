body {
    margin:0;
    padding:0;
    font-family:helvetica, sans-serif;
}

a {
    color:#00abff;
    text-decoration:none;
}

h1 {
    font-weight:normal;
    border-bottom:1px solid #bbb;
    padding:0 0 10px 0;
}

h2 {
    font-weight:normal;
    margin:30px 0 0;
}

#content {
    float:left;
    width:60%;
    padding:0 0 0 30px;
}

#sidebar {
    float:right;
    width:30%;
    padding:10px;
    background:#efefef;
    height:100%;
}

p.date {
    color:#ccc;
    font-family: georgia, serif;
    font-size: 12px;
    font-style: italic;
}

.left {
    float:left;
    margin-right:10px;
}

/* pagination */
.pagination {
    margin:40px 0;
    font-weight:bold;
}

/* forms */
label {
    float:left;
    clear:both;
    color:#333;
    margin-bottom:4px;
}
input, textarea {
    clear:both;
    float:left;
    margin:0 0 10px;
    background:#ededed;
    border:0;
    padding:6px 10px;
    font-size:12px;
}
input[type=submit] {
    font-weight:bold;
    background:#00abff;
    color:#fff;
    padding:10px 20px;
    font-size:14px;
    text-transform:uppercase;
}
.errorlist {
    color:#cc0033;
    float:left;
    clear:both;
    padding-left:10px;
}

/* comments */
.comment {
    padding:10px;
}
.comment:nth-child(even) {
    background:#efefef;
}
.comment .info {
    font-weight:bold;
    font-size:12px;
    color:#666;
}

/* Add to existing CSS */
.categories {
    color: #666;
    font-size: 12px;
    margin: 0;
}

#sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#sidebar ul li {
    margin: 0 0 10px;
}

#sidebar ul li.selected a {
    color: #1a1a1a;
    font-weight: bold;
}

.tags {
    color: #666;
    font-size: 12px;
    margin: 10px 0;
}

.tags a {
    color: #1a1a1a;
    text-decoration: none;
    background-color: #f0f0f0;
    padding: 2px 5px;
    border-radius: 3px;
}

.tags a:hover {
    background-color: #e0e0e0;
}

/* Post Detail Styles */
.post-detail {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.post-detail h1 {
    color: #333;
    font-size: 2.5em;
    margin-bottom: 20px;
}

.post-meta {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.post-content {
    font-size: 1.1em;
    line-height: 1.6;
    color: #444;
}

.post-content img {
    max-width: 100%;
    height: auto;
    margin: 20px 0;
}

.post-content h2 {
    color: #444;
    margin: 30px 0 15px;
}

.post-content p {
    margin-bottom: 20px;
}

.post-content ul, .post-content ol {
    margin-bottom: 20px;
    padding-left: 20px;
}

.post-content blockquote {
    margin: 20px 0;
    padding: 10px 20px;
    border-left: 5px solid #eee;
    font-style: italic;
}

.similar-posts {
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.similar-posts h2 {
    color: #666;
    font-size: 1.5em;
    margin-bottom: 15px;
}

/* Search box styles */
.search-box {
    margin: 20px 0;
    padding: 10px;
    background: #f8f8f8;
    border-radius: 5px;
}

.search-box input[type="text"] {
    width: 70%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.search-box button {
    padding: 8px 15px;
    background: #00abff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.search-box button:hover {
    background: #0090d8;
}

/* Search results styles */
.search-results article {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.search-results h2 {
    margin-bottom: 10px;
}

.search-results .highlight {
    background-color: #ffffcc;
    font-weight: bold;
}
