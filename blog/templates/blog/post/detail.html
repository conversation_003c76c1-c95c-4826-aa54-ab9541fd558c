{% extends "blog/base.html" %}
{% load static %}

{% block title %}{{ post.title }}{% endblock %}

{% block content %}
  <article class="post-detail">
    <h1>{{ post.title }}</h1>
    <div class="post-meta">
      <p class="date">
        Published <span class="timestamp" data-timestamp="{{ post.publish.timestamp|floatformat:0 }}">{{ post.publish }}</span> by {{ post.author }}
      </p>
      <p class="tags">
        Tags:
        {% for tag in post.tags.all %}
          <a href="{% url 'blog:post_list_by_tag' tag.slug %}">
            {{ tag.name }}
          </a>
          {% if not forloop.last %}, {% endif %}
        {% endfor %}
      </p>
      <p class="category">
        Category: <a href="{{ post.category.get_absolute_url }}">{{ post.category.name }}</a>
      </p>
    </div>
    
    <div class="post-content">
      {{ post.body|safe }}
    </div>
    
    {% if similar_posts %}
      <div class="similar-posts">
        <h2>Similar posts</h2>
        {% for post in similar_posts %}
          <p>
            <a href="{{ post.get_absolute_url }}">{{ post.title }}</a>
          </p>
        {% endfor %}
      </div>
    {% endif %}
  </article>
{% endblock %}

<script>
  // Convert all timestamps to local time
  document.querySelectorAll('.timestamp').forEach(element => {
    const timestamp = parseInt(element.getAttribute('data-timestamp')) * 1000; // Convert to milliseconds
    const localDate = new Date(timestamp);
    element.textContent = localDate.toLocaleString();
  });
</script>
