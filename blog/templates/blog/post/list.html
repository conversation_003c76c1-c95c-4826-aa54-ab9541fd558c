{% extends "blog/base.html" %}

{% block title %}My Blog{% endblock %}

{% block content %}
  <h1>My Blog</h1>
  {% if tag %}
    <h2>Posts tagged with "{{ tag.name }}"</h2>
  {% endif %}
  {% if category %}
    <h2>Posts in "{{ category.name }}"</h2>
  {% endif %}
  
  {% for post in posts %}
    <article>
      <h2>
        <a href="{{ post.get_absolute_url }}">
          {{ post.title }}
        </a>
      </h2>
      <p class="tags">
        Tags:
        {% for tag in post.tags.all %}
          <a href="{% url 'blog:post_list_by_tag' tag.slug %}">
            {{ tag.name }}
          </a>
          {% if not forloop.last %}, {% endif %}
        {% endfor %}
      </p>
      <p class="categories">
        Category: <a href="{{ post.category.get_absolute_url }}">{{ post.category.name }}</a>
      </p>
      <p class="date">
        Published <span class="timestamp" data-timestamp="{{ post.publish.timestamp|floatformat:0 }}">{{ post.publish }}</span> by {{ post.author }}
      </p>
      {{ post.body|truncatewords:30|linebreaks }}
    </article>
  {% empty %}
    <p>No posts found.</p>
  {% endfor %}
  
  {% include "pagination.html" with page=page_obj %}

  <h2>Categories</h2>
  <ul>
    {% for cat in categories %}
      <li>
        <a href="{{ cat.get_absolute_url }}">{{ cat.name }}</a>
      </li>
    {% empty %}
      <li>No categories yet.</li>
    {% endfor %}
  </ul>
{% endblock %}

<script>
  document.querySelectorAll('.timestamp').forEach(element => {
    const timestamp = parseInt(element.getAttribute('data-timestamp')) * 1000;
    const localDate = new Date(timestamp);
    element.textContent = localDate.toLocaleString();
  });
</script>
