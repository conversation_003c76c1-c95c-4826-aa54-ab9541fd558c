{% extends "blog/base.html" %}

{% block title %}Search Results{% endblock %}

{% block content %}
  <h1>Search Posts</h1>
  <form method="get" class="search-form">
    {{ form.as_p }}
    <input type="submit" value="Search">
  </form>
  
  {% if query %}
    <div class="search-results">
      <h2>Results for "{{ query }}"</h2>
      <p>Found {{ total_hits }} result{{ total_hits|pluralize }}</p>
      
      {% for hit in results %}
        <article>
          <h2>
            <a href="{% url 'blog:post_detail' hit.category.slug hit.slug %}">
              {{ hit.title }}
            </a>
          </h2>
          <p class="tags">
            Tags: 
            {% for tag in hit.tags %}
              {{ tag }}{% if not forloop.last %}, {% endif %}
            {% endfor %}
          </p>
          <p class="categories">
            Category: {{ hit.category.name }}
          </p>
          <p class="date">
            Published {{ hit.publish }} by {{ hit.author.email }}
          </p>
          <p>{{ hit.body|truncatewords:30|linebreaks }}</p>
        </article>
      {% empty %}
        <p>No results found.</p>
      {% endfor %}
    </div>
  {% endif %}
{% endblock %}
