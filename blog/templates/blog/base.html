{% load static %}
<!DOCTYPE html>
<html>
<head>
  <title>{% block title %}{% endblock %}</title>
  <link href="{% static "css/blog.css" %}" rel="stylesheet">
</head>
<body>
  <div id="header">
    <h1><a href="{% url 'blog:post_list' %}">My Blog</a></h1>
    <!-- Add search box here -->
    <div class="search-box">
      <form action="{% url 'blog:post_search' %}" method="get">
        <input type="text" name="query" placeholder="Search posts..." />
        <button type="submit">Search</button>
      </form>
    </div>
  </div>
  
  <div id="content">
    {% block content %}
    {% endblock %}
  </div>
  
  <div id="sidebar">
    <h2>Blog</h2>
    <ul>
      <li><a href="{% url 'blog:post_list' %}">All Posts</a></li>
      <li><a href="{% url 'blog:post_search' %}">Search</a></li>
    </ul>
    
    <h2>Categories</h2>
    <ul>
      <li {% if not category %}class="selected"{% endif %}>
        <a href="{% url "blog:post_list" %}">All posts</a>
      </li>
      {% for cat in categories %}
        <li {% if category.slug == cat.slug %}class="selected"{% endif %}>
          <a href="{{ cat.get_absolute_url }}">
            {{ cat.name }}
          </a>
        </li>
      {% endfor %}
    </ul>
  </div>
</body>
</html>
