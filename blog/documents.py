from django_elasticsearch_dsl import Document, fields
from django_elasticsearch_dsl.registries import registry
from .models import Post, Category

@registry.register_document
class PostDocument(Document):
    # Define fields with specific analyzers
    title = fields.TextField(
        attr='title',
        fields={
            'raw': fields.KeywordField(),
            'suggest': fields.CompletionField(),
        }
    )
    
    slug = fields.TextField(attr='slug')
    
    author = fields.ObjectField(properties={
        'username': fields.TextField(),
        'email': fields.TextField(),
    })
    
    category = fields.ObjectField(properties={
        'name': fields.TextField(),
        'slug': fields.TextField(),
    })
    
    body = fields.TextField(attr='body')
    
    tags = fields.ListField(fields.TextField())
    
    publish = fields.DateField()
    created = fields.DateField()
    updated = fields.DateField()
    status = fields.TextField()
    cover_image = fields.TextField(attr='cover_image.url')
    
    class Index:
        name = 'posts'
        settings = {
            'number_of_shards': 1,
            'number_of_replicas': 0
        }
    
    class Django:
        model = Post
        
        # Only index published posts
        queryset_pagination = 5000  # Adjust based on your data size
        
        # Fields that should be included in the index
        fields = [
            'id',
        ]
        
        # Related fields that should be fetched when indexing
        related_models = [Category]

    def get_queryset(self):
        """Return the queryset that should be indexed by elasticsearch."""
        return self.Django.model.published.all()
    
    def get_instances_from_related(self, related_instance):
        """If related_models is set, define how to retrieve the Post instance(s)."""
        if isinstance(related_instance, Category):
            return related_instance.posts.all()
    
    def prepare_author(self, instance):
        return {
            'username': instance.author.email,  # Use email instead of username
            'email': instance.author.email
        }
    
    def prepare_category(self, instance):
        return {
            'name': instance.category.name,
            'slug': instance.category.slug
        }
    
    def prepare_tags(self, instance):
        return [tag.name for tag in instance.tags.all()]

    def prepare_cover_image(self, instance):
        if instance.cover_image:
            return instance.cover_image.url
        return None
