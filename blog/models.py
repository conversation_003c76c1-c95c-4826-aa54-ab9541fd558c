from django.utils import timezone
from django.conf import settings
from django.db import models
from django.urls import reverse
from taggit.managers import TaggableManager
from django.db.models import Count
from django_ckeditor_5.fields import CKEditor5Field

class Category(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']
        verbose_name_plural = 'categories'

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('blog:post_list_by_category', args=[self.slug])

class PublishManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(status=Post.Status.PUBLISHED)

class Post(models.Model):
    class Status(models.TextChoices):
        DRAFT = 'draft', 'Draft'
        PUBLISHED = 'published', 'Published'

    title = models.CharField(max_length=250)
    slug = models.SlugField(max_length=350, unique=True)  # Changed from unique_for_date
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='blog_posts'
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.PROTECT,
        related_name='posts'
    )
    body = CKEditor5Field('Content', config_name='default')
    publish = models.DateTimeField(default=timezone.now)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)
    status = models.CharField(
        max_length=9,
        choices=Status,
        default=Status.DRAFT
    )
    tags = TaggableManager()
    cover_image = models.ImageField(upload_to='blog_covers/', null=True, blank=True)

    objects = models.Manager()
    published = PublishManager()
    
    class Meta:
        ordering = ['-publish']
        indexes = [
            models.Index(fields=['publish']),
            models.Index(fields=['status']),
            models.Index(fields=['updated']),
        ]

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('blog:post_detail',
                      args=[self.category.slug,
                            self.slug])

    def get_similar_posts(self, limit=3):
        """
        Returns posts that share tags with the current post,
        ordered by number of shared tags and publish date
        """
        post_tags_ids = self.tags.values_list('id', flat=True)
        similar_posts = Post.published.filter(tags__in=post_tags_ids)\
                                    .exclude(id=self.id)
        similar_posts = similar_posts.annotate(same_tags=Count('tags'))\
                                   .order_by('-same_tags', '-publish')[:limit]
        return similar_posts
