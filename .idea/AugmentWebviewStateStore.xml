<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>