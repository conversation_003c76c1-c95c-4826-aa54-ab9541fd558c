/* Base styles */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Login page styles */
.login-container {
    max-width: 400px;
    margin: 50px auto;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.login-container h1 {
    text-align: center;
    margin-bottom: 20px;
}

/* Business search styles */
.search-container {
    margin: 20px 0;
    padding: 20px;
    background-color: #f8f8f8;
    border-radius: 5px;
}

.search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.search-form input, .search-form select {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.search-form button {
    padding: 8px 16px;
    background-color: #79aec8;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.business-list {
    margin-top: 20px;
}

.business-card {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

.business-name {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.business-details {
    font-size: 14px;
    color: #666;
}

.business-rating {
    color: #f8a100;
    font-weight: bold;
}

/* Business detail styles */
.business-header {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.detail-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border-radius: 5px;
}

.section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #417690;
}

.metrics-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.metric-card {
    flex: 1;
    min-width: 150px;
    padding: 15px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
}

.metric-label {
    font-size: 14px;
    color: #666;
}

.recommendation-card {
    margin-bottom: 15px;
    padding: 15px;
    border-left: 4px solid #79aec8;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.high-impact {
    border-left-color: #c82536;
}

.medium-impact {
    border-left-color: #f89406;
}

.low-impact {
    border-left-color: #79aec8;
}

.recommendation-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.recommendation-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.recommendation-description {
    font-size: 14px;
    line-height: 1.4;
}
