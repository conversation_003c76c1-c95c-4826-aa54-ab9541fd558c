from django.urls import path, include
from rest_framework_simplejwt.views import TokenRefreshView

from .views import (
    RegisterView, VerifyEmailView, LoginView, LogoutView,
    ForgotPasswordView, ResetPasswordView, UserProfileView,
    CheckAuthView, ResendVerificationOTPView, 
    GoogleOAuthRedirectView, GoogleOAuthCallbackView,
    GoogleIdTokenLoginView,
    WhiteLabelSettingsView,
    # Payment views
    StripeWebhookView,
    CheckPaymentStatusView, UserPaymentHistoryView,
    CreatePaymentSessionView, StripePaymentCallbackView,
    UserProfileSettingsView,
    StripePricingListView,
    # Subscription management views
    UserSubscriptionsView, ManageSubscriptionView,
    # Admin views
    AdminUserListView, AdminUserSessionListView, AdminRevokeTokenView,
)

app_name = 'accounts'

urlpatterns = [
    path('register/', RegisterView.as_view(), name='register'),
    path('verify-email/', VerifyEmailView.as_view(), name='verify-email'),
    path('resend-otp/', ResendVerificationOTPView.as_view(), name='resend-otp'),

    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token-refresh'),
    path('check-auth/', CheckAuthView.as_view(), name='check_auth'),

    path('forgot-password/', ForgotPasswordView.as_view(), name='forgot-password'),
    path('reset-password/<str:uidb64>/<str:token>/', ResetPasswordView.as_view(), name='reset-password'),

    path('profile/', UserProfileView.as_view(), name='profile'),

    # URLs for the Authorization Code Flow (renamed)
    path('google/oauth-redirect/', GoogleOAuthRedirectView.as_view(), name='google-oauth-redirect'),
    path('google/callback/', GoogleOAuthCallbackView.as_view(), name='google-oauth-callback'),

    # URL for the ID Token based Google Login
    path('google/login/', GoogleIdTokenLoginView.as_view(), name='google-id-token-login'),

    path('whitelabel-setting/', WhiteLabelSettingsView.as_view(), name='whitelabel-setting'),

    # Payment URLs
    path('payments/webhook/stripe/', StripeWebhookView.as_view(), name='stripe_webhook'),
    path('payments/check-status/<uuid:uid>/', CheckPaymentStatusView.as_view(), name='check_payment_status'),
    path('payments/history/', UserPaymentHistoryView.as_view(), name='user_payment_history'),
    path('payments/create-session/', CreatePaymentSessionView.as_view(), name='create_payment_session'),
    path('payments/stripe-callback/', StripePaymentCallbackView.as_view(), name='stripe_payment_callback'),

    # User Subscriptions Management
    path('subscriptions/', UserSubscriptionsView.as_view(), name='user-subscriptions-list'),
    path('subscriptions/<uuid:subscription_id>/manage/', ManageSubscriptionView.as_view(), name='manage-subscription'),

    # Admin User and Session Management
    path('admin/users/', AdminUserListView.as_view(), name='admin-user-list'),
    path('admin/users/<int:user_id>/sessions/', AdminUserSessionListView.as_view(), name='admin-user-session-list'),
    path('admin/sessions/<int:token_id>/revoke/', AdminRevokeTokenView.as_view(), name='admin-revoke-token'),

    # User Profile Settings
    path('profile/settings/', UserProfileSettingsView.as_view(), name='profile-settings'),
]
