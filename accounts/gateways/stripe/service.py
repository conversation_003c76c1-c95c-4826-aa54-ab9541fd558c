from typing import Optional
import stripe
from django.conf import settings
from .config import STRIPE_CONFIG
from ...models import PaymentH<PERSON>ory, CustomUser, Subscription
from tools.models import AnalysisRecord
import logging
import os
from urllib.parse import urljoin, urlparse
from django.utils import timezone
from decimal import Decimal
from django.db import transaction, models
import datetime

logger = logging.getLogger(__name__)

# Define the base URL for redirect URLs - use settings or fallback
SITE_URL = getattr(settings, 'SITE_URL', 'https://seoanalyser.com.au').rstrip('/')
API_BASE_PATH = getattr(settings, 'API_BASE_PATH', '/api')
# This path is for synchronous redirects from <PERSON><PERSON>'s checkout page (success/cancel)
STRIPE_CALLBACK_PATH = f"{API_BASE_PATH}/accounts/payments/stripe-callback/"


# Define final payment statuses that generally shouldn't be overwritten by non-critical events
FINAL_PAYMENT_STATUSES = {'succeeded', 'failed', 'cancelled', 'refunded'}
ACTIVE_SUBSCRIPTION_STATUSES = ['active', 'trialing']
PENDING_SUBSCRIPTION_STATUSES = ['incomplete', 'past_due']


class StripeService:
    def __init__(self):
        stripe.api_key = STRIPE_CONFIG['SECRET_KEY']
        if not stripe.api_key or stripe.api_key == 'your_stripe_secret_key':
            print("Stripe secret key is not configured. StripeService will not function.")
            # raise ImproperlyConfigured("Stripe secret key is not configured.")

        # Setup dedicated logger for Stripe webhooks
        self.stripe_webhook_logger = logging.getLogger('stripe_webhook')
        self.stripe_webhook_logger.setLevel(logging.INFO)
        
        # Prevent propagation to avoid duplicate logs if root logger also has a file handler
        self.stripe_webhook_logger.propagate = False 

        log_file_path = os.path.join(settings.BASE_DIR, 'logs', 'stripe.log')
        
        # Check if handlers are already configured to prevent duplication
        if not self.stripe_webhook_logger.handlers:
            try:
                # Ensure logs directory exists (it should, based on workspace structure)
                os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
                
                file_handler = logging.FileHandler(log_file_path)
                file_handler.setLevel(logging.INFO)
                
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                file_handler.setFormatter(formatter)
                
                self.stripe_webhook_logger.addHandler(file_handler)
            except Exception as e:
                # Fallback to console if logger setup fails, to not break the service
                print(f"Error setting up stripe_webhook_logger: {e}. Webhook payloads will be printed to console.")
                self.stripe_webhook_logger = logging.getLogger(__name__) # Use default logger

    @transaction.atomic
    def create_checkout_session(self,
                                stripe_price_id: str,
                                customer_email: str,
                                user=None,
                                success_url_base: Optional[str] = None,
                                cancel_url_base: Optional[str] = None,
                                task_id: Optional[str] = None,
                                client_reference_id: Optional[str] = None,
                                existing_customer_id: Optional[str] = None,
                                coupon_id: Optional[str] = None,
                                allow_promotion_codes: Optional[bool] = None
                                ):
        # import pdb; pdb.set_trace()
        """Create a Stripe Checkout Session using Stripe Price ID."""
        try:
            # Retrieve Price and Product details from Stripe
            try:
                stripe_price = stripe.Price.retrieve(stripe_price_id, expand=['product'])
                if not stripe_price.active:
                    print(f"Stripe Price ID {stripe_price_id} is not active.")
                    raise ValueError(f"Inactive Stripe Price ID: {stripe_price_id}")
                # Check if product exists and has required attributes instead of strict isinstance check
                if not hasattr(stripe_price, 'product') or not stripe_price.product:
                    print(f"Stripe Product for Price ID {stripe_price_id} could not be retrieved.")
                    raise ValueError(f"Invalid product data for Stripe Price ID: {stripe_price_id}")
                # Check if the product has the required attributes (for mock compatibility)
                if not (hasattr(stripe_price.product, 'id') and hasattr(stripe_price.product, 'name')):
                    print(f"Stripe Product for Price ID {stripe_price_id} is missing required attributes.")
                    raise ValueError(f"Invalid product data for Stripe Price ID: {stripe_price_id}")
            except stripe.error.StripeError as e:
                print(f"Stripe error retrieving price {stripe_price_id}: {e}")
                raise ValueError(f"Invalid or inaccessible Stripe Price ID: {stripe_price_id}. Details: {str(e)}")

            # plan = Pricing.objects.get(id=plan_id, is_active=True) # Commented out DB call
            # final_price = self._calculate_final_price(plan) # Commented out
            
            final_price_decimal = Decimal(stripe_price.unit_amount / 100)
            currency = stripe_price.currency.lower()
            stripe_product = stripe_price.product

            user_id_for_ph = user.id if user and hasattr(user, 'id') else None

            payment_history = PaymentHistory.objects.create(
                user_id=user_id_for_ph,
                amount=final_price_decimal,
                currency=currency,
                payment_gateway='stripe',
                status='pending',
                task_id=task_id,
                gateway_specific_details={
                    # 'plan_id': plan.id, # Commented out
                    # 'plan_name': plan.name, # Commented out
                    'stripe_price_id': stripe_price.id,
                    'stripe_product_id': stripe_product.id,
                    'stripe_product_name': stripe_product.name,
                    'customer_email': customer_email, 
                }
            )
            payment_id = payment_history.id
            logger.info(f"StripeService: Created PaymentHistory record with ID: {payment_id} for user_id: {user_id_for_ph}, email: {customer_email}")
            
            # Construct success and cancel URLs
            success_base = success_url_base or SITE_URL
            cancel_base = cancel_url_base or SITE_URL

            success_callback_url = urljoin(success_base.rstrip('/') + '/', STRIPE_CALLBACK_PATH.lstrip('/')) + f"?uid={payment_id}"
            cancel_callback_url = urljoin(cancel_base.rstrip('/') + '/', STRIPE_CALLBACK_PATH.lstrip('/')) + f"?uid={payment_id}"

            final_client_reference_id = client_reference_id or str(payment_id)

            if task_id:
                success_callback_url += f"&task_id={task_id}"
                cancel_callback_url += f"&task_id={task_id}"

            metadata = {
                # 'plan_id': str(plan.id), # Commented out
                'stripe_price_id': stripe_price.id,
                'stripe_product_id': stripe_product.id,
                'user_id': str(user_id_for_ph) if user_id_for_ph else None,
                'payment_history_id': str(payment_id),
                'task_id': task_id if task_id else None,
                'app_client_reference_id': final_client_reference_id 
            }

            # Stripe interval and interval_count are now directly from the Stripe Price object
            # if plan.period == 'monthly':
            #     stripe_interval = 'month'
            # elif plan.period == 'yearly':
            #     stripe_interval = 'year'
            # elif plan.period == 'daily':
            #     stripe_interval = 'day'
            # elif plan.period and '_months' in plan.period:
            #     try:
            #         count_str = plan.period.split('_months')[0]
            #         stripe_interval_count = int(count_str)
            #         if stripe_interval_count >= 1:
            #             stripe_interval = 'month'
            #         else:
            #             raise ValueError("Interval count must be >= 1")
            #     except (ValueError, IndexError) as e:
            #         print(f"Could not parse interval count from period '{plan.period}' for plan ID {plan.id}: {e}")
            #         raise ValueError(f"Invalid interval count in period '{plan.period}'.")
            
            # if not stripe_interval:
            #     print(f"Unsupported plan period '{plan.period}' for plan ID {plan.id}.")
            #     raise ValueError(f"Cannot map plan period '{plan.period}' to Stripe.")

            # The line_item is now simpler as we directly use the Stripe Price ID
            # line_item_price_data = {
            #     'currency': currency,
            #     'product_data': {
            #         'name': stripe_product.name,
            #         'description': stripe_product.description or f"{stripe_product.name}",
            #     },
            #     'unit_amount': stripe_price.unit_amount, 
            #     'recurring': {
            #         'interval': stripe_price.recurring.interval if stripe_price.recurring else None,
            #         'interval_count': stripe_price.recurring.interval_count if stripe_price.recurring else None,
            #     },
            # }
            
            session_data = {
                'payment_method_types': STRIPE_CONFIG['PAYMENT_METHODS'],
                # 'line_items': [{'price_data': line_item_price_data, 'quantity': 1}], # Old way
                'line_items': [{'price': stripe_price.id, 'quantity': 1}], # New way using Price ID
                'mode': 'subscription' if stripe_price.type == 'recurring' else 'payment', # Adjust mode based on price type
                'success_url': success_callback_url,
                'cancel_url': cancel_callback_url,
                'metadata': metadata,
                'client_reference_id': final_client_reference_id,
            }

            # Add subscription_data if mode is subscription, to pass metadata to the Subscription object
            if session_data['mode'] == 'subscription':
                session_data['subscription_data'] = {
                    'metadata': metadata  # Pass the same metadata here
                }

            # Handle allow_promotion_codes
            if allow_promotion_codes is not None:
                session_data['allow_promotion_codes'] = allow_promotion_codes
            else:
                session_data['allow_promotion_codes'] = True # Default if not specified

            # Handle coupon_id
            if coupon_id:
                session_data['discounts'] = [{'coupon': coupon_id}]

            # Determine Stripe customer
            stripe_customer_to_use = None
            customer_creation_attempted = False

            if existing_customer_id: # Prioritize if explicitly passed
                stripe_customer_to_use = existing_customer_id
                print(f"Using provided existing Stripe customer ID: {stripe_customer_to_use}")
                session_data['customer'] = stripe_customer_to_use
            elif user and hasattr(user, 'stripe_customer_id') and user.stripe_customer_id:
                stripe_customer_to_use = user.stripe_customer_id
                print(f"Using Stripe customer ID {stripe_customer_to_use} from user {user.id}")
                session_data['customer'] = stripe_customer_to_use
            else: # No existing_customer_id, and user either doesn't exist, or doesn't have a stripe_customer_id
                if user: # Logged-in user, create Stripe customer and link it
                    try:
                        customer_name = user.get_full_name() or customer_email
                        created_stripe_customer = stripe.Customer.create(
                            email=customer_email, # Should be user.email
                            name=customer_name,
                            metadata={'user_id': str(user.id)}
                        )
                        stripe_customer_to_use = created_stripe_customer.id
                        user.stripe_customer_id = stripe_customer_to_use
                        user.save(update_fields=['stripe_customer_id'])
                        print(f"Created and linked new Stripe customer ID {stripe_customer_to_use} for user {user.id}")
                        session_data['customer'] = stripe_customer_to_use
                        customer_creation_attempted = True
                    except stripe.error.StripeError as e:
                        print(f"Stripe error creating customer for user {user.id}: {e}")
                        session_data['customer_email'] = customer_email
                    except Exception as e: # Django model errors etc.
                        print(f"Error creating/saving customer for user {user.id}: {e}")
                        session_data['customer_email'] = customer_email
                else: # Anonymous user, use email for checkout
                    session_data['customer_email'] = customer_email
                    print(f"Proceeding with customer_email {customer_email} for anonymous user.")
            
            try:
                checkout_session = stripe.checkout.Session.create(**session_data)
            except stripe.error.InvalidRequestError as e:
                if e.code == 'resource_missing' and e.param == 'customer' and stripe_customer_to_use and not customer_creation_attempted:
                    print(f"Stripe customer {stripe_customer_to_use} not found for user {user.id if user else 'N/A'}. Clearing local ID and attempting to create a new customer.")
                    if user and hasattr(user, 'stripe_customer_id') and user.stripe_customer_id == stripe_customer_to_use:
                        user.stripe_customer_id = None
                        user.save(update_fields=['stripe_customer_id'])
                    
                    # Remove the invalid customer from session_data and try creating one
                    if 'customer' in session_data: del session_data['customer']
                    session_data['customer_email'] = customer_email # Ensure email is set for creation attempt

                    if user: # Logged-in user, create Stripe customer and link it
                        try:
                            customer_name = user.get_full_name() or customer_email
                            created_stripe_customer = stripe.Customer.create(
                                email=customer_email, 
                                name=customer_name,
                                metadata={'user_id': str(user.id)}
                            )
                            stripe_customer_to_use = created_stripe_customer.id
                            user.stripe_customer_id = stripe_customer_to_use
                            user.save(update_fields=['stripe_customer_id'])
                            print(f"Re-created and linked new Stripe customer ID {stripe_customer_to_use} for user {user.id} after initial failure.")
                            session_data['customer'] = stripe_customer_to_use
                            if 'customer_email' in session_data: del session_data['customer_email'] # Remove if customer ID is now set
                        except stripe.error.StripeError as e_create:
                            print(f"Stripe error re-creating customer for user {user.id}: {e_create}")
                            raise # Propagate if customer re-creation also fails
                        except Exception as e_model_save:
                            print(f"Error re-creating/saving customer for user {user.id}: {e_model_save}")
                            raise # Propagate if model saving fails
                    else: # Anonymous user, should just use email, this path less likely for customer missing if not set
                        print("Cannot re-create customer for anonymous user, original error will propagate.")
                        raise e # Re-raise original error
                    
                    # Retry session creation with the new/corrected customer info
                    checkout_session = stripe.checkout.Session.create(**session_data)
                else:
                    raise # Re-raise other InvalidRequestErrors or if customer_creation_attempted

            payment_history.gateway_transaction_id = checkout_session.id
            if 'session_id' not in payment_history.gateway_specific_details:
                 payment_history.gateway_specific_details = payment_history.gateway_specific_details or {}
            payment_history.gateway_specific_details['session_id'] = checkout_session.id
            if stripe_customer_to_use: # If a customer ID was used or created
                payment_history.gateway_specific_details['stripe_customer_id'] = stripe_customer_to_use
            # Add Stripe price and product IDs to gateway_specific_details for easier reference
            payment_history.gateway_specific_details['stripe_price_id'] = stripe_price.id
            payment_history.gateway_specific_details['stripe_product_id'] = stripe_product.id
            payment_history.save()

            # The view expects only the redirect URL from this service method
            return checkout_session.url # Return the URL for redirection

        except stripe.error.StripeError as e:
            # print(f"Stripe error (PH ID: {payment_history.id if 'payment_history' in locals() else 'N/A'}): {str(e)}", exc_info=True)
            logger.error(f"Stripe error creating checkout session (PH ID: {payment_history.id if 'payment_history' in locals() else 'N/A'}): {str(e)}", exc_info=True)
            # Propagate Stripe errors to be handled by the view (e.g. show user a message)
            raise
        except ValueError as e: # Catch our own ValueErrors (e.g., bad plan_id)
            # print(f"Value error creating checkout session (PH ID: {payment_history.id if 'payment_history' in locals() else 'N/A'}): {str(e)}")
            logger.warning(f"Value error creating checkout session (PH ID: {payment_history.id if 'payment_history' in locals() else 'N/A'}): {str(e)}")
            raise
        except Exception as e:
            # print(f"Unexpected error (PH ID: {payment_history.id if 'payment_history' in locals() else 'N/A'}): {str(e)}", exc_info=True)
            logger.error(f"Unexpected error creating checkout session (PH ID: {payment_history.id if 'payment_history' in locals() else 'N/A'}): {str(e)}", exc_info=True)
            raise

    def find_payment_history_for_event(self, event_id, event_type, event_data):
        """Attempts to find a PaymentHistory record associated with a Stripe event."""
        payment_history = None
        lookup_details = {'method': "None", 'value': "N/A"}

        if hasattr(event_data, 'metadata') and event_data.metadata and event_data.metadata.get('payment_history_id'):
            try:
                payment_history = PaymentHistory.objects.get(id=event_data.metadata.payment_history_id)
                lookup_details = {'method': "metadata.payment_history_id", 'value': event_data.metadata.payment_history_id}
                print(f"Event {event_id}: Found PH {payment_history.id} via metadata.payment_history_id.")
                return payment_history, lookup_details
            except (PaymentHistory.DoesNotExist, ValueError): pass
        
        # Try finding PH_id in common nested locations for invoice events
        if event_type.startswith('invoice.'):
            ph_id_from_invoice_lines = None
            ph_id_from_invoice_subscription_meta = None

            # Check invoice.lines.data[0].metadata.payment_history_id
            if hasattr(event_data, 'lines') and event_data.lines and \
               hasattr(event_data.lines, 'data') and event_data.lines.data and \
               len(event_data.lines.data) > 0 and \
               hasattr(event_data.lines.data[0], 'metadata') and event_data.lines.data[0].metadata:
                ph_id_from_invoice_lines = event_data.lines.data[0].metadata.get('payment_history_id')
                if ph_id_from_invoice_lines:
                    try:
                        payment_history = PaymentHistory.objects.get(id=ph_id_from_invoice_lines)
                        lookup_details = {'method': "invoice.lines.data[0].metadata.payment_history_id", 'value': ph_id_from_invoice_lines}
                        print(f"Event {event_id}: Found PH {payment_history.id} via invoice lines metadata.")
                        return payment_history, lookup_details
                    except (PaymentHistory.DoesNotExist, ValueError): pass
            
            # Check invoice.parent.subscription_details.metadata.payment_history_id (seen in stripe log)
            if not payment_history and hasattr(event_data, 'parent') and event_data.parent and \
               hasattr(event_data.parent, 'subscription_details') and event_data.parent.subscription_details and \
               hasattr(event_data.parent.subscription_details, 'metadata') and event_data.parent.subscription_details.metadata:
                ph_id_from_invoice_subscription_meta = event_data.parent.subscription_details.metadata.get('payment_history_id')
                if ph_id_from_invoice_subscription_meta:
                    try:
                        payment_history = PaymentHistory.objects.get(id=ph_id_from_invoice_subscription_meta)
                        lookup_details = {'method': "invoice.parent.subscription_details.metadata.payment_history_id", 'value': ph_id_from_invoice_subscription_meta}
                        print(f"Event {event_id}: Found PH {payment_history.id} via invoice parent subscription metadata.")
                        return payment_history, lookup_details
                    except (PaymentHistory.DoesNotExist, ValueError): pass
        
        # 3. Subscription ID (for subscription or invoice events)
        subscription_id = None
        if event_type.startswith('customer.subscription'):
            subscription_id = event_data.id
        elif event_type.startswith('invoice.') and hasattr(event_data, 'subscription') and event_data.subscription:
            subscription_id = event_data.subscription
        
        if subscription_id:
            try: # Find latest PH for this Stripe subscription ID
                payment_history = PaymentHistory.objects.filter(gateway_subscription_id=subscription_id).latest('created_at')
                lookup_details = {'method': "gateway_subscription_id", 'value': subscription_id}
                print(f"Event {event_id}: Found PH {payment_history.id} via gateway_subscription_id.")
                return payment_history, lookup_details
            except PaymentHistory.DoesNotExist: pass

        # 4. Payment Intent ID (from payment_intent, invoice, or charge events)
        payment_intent_id = None
        if event_type.startswith('payment_intent.'):
            payment_intent_id = event_data.id
        elif event_type.startswith('invoice.') and hasattr(event_data, 'payment_intent') and event_data.payment_intent:
            payment_intent_id = event_data.payment_intent
        elif event_type.startswith('charge.') and hasattr(event_data, 'payment_intent') and event_data.payment_intent:
            payment_intent_id = event_data.payment_intent
            
        if payment_intent_id:
            try: # gateway_charge_id often stores the Payment Intent ID
                payment_history = PaymentHistory.objects.get(gateway_charge_id=payment_intent_id)
                lookup_details = {'method': "gateway_charge_id (for PI)", 'value': payment_intent_id}
                print(f"Event {event_id}: Found PH {payment_history.id} via gateway_charge_id (PI).")
                return payment_history, lookup_details
            except PaymentHistory.DoesNotExist:
                 # Fallback: if PI metadata contains checkout_session_id which is gateway_transaction_id
                try:
                    pi = stripe.PaymentIntent.retrieve(payment_intent_id)
                    if pi.metadata and pi.metadata.get('checkout_session_id'):
                        checkout_session_id = pi.metadata.get('checkout_session_id')
                        payment_history = PaymentHistory.objects.get(gateway_transaction_id=checkout_session_id)
                        lookup_details = {'method': "PI metadata.checkout_session_id -> gateway_transaction_id", 'value': checkout_session_id}
                        print(f"Event {event_id}: Found PH {payment_history.id} via PI metadata linking to checkout session.")
                        return payment_history, lookup_details
                except (stripe.error.StripeError, PaymentHistory.DoesNotExist, ValueError):
                    pass

        # 5. Checkout Session ID in Event Data (e.g. from payment_intent metadata)
        checkout_session_id_from_event = None
        if hasattr(event_data, 'metadata') and event_data.metadata and event_data.metadata.get('checkout_session_id'):
             checkout_session_id_from_event = event_data.metadata.get('checkout_session_id')
        elif hasattr(event_data, 'checkout_session') and event_data.checkout_session: # Direct reference
             checkout_session_id_from_event = event_data.checkout_session

        if checkout_session_id_from_event:
            try:
                payment_history = PaymentHistory.objects.get(gateway_transaction_id=checkout_session_id_from_event)
                lookup_details = {'method': "event_data.checkout_session_id -> gateway_transaction_id", 'value': checkout_session_id_from_event}
                print(f"Event {event_id}: Found PH {payment_history.id} via event_data linking to checkout session.")
                return payment_history, lookup_details
            except (PaymentHistory.DoesNotExist, ValueError): pass

        print(f"Event {event_id} ({event_type}): Could not associate event with a PaymentHistory record using available methods.")
        return None, lookup_details

    def _create_or_update_local_subscription(self, stripe_sub_obj, payment_history_obj: PaymentHistory):
        """Creates or updates a local Subscription record from a Stripe Subscription object."""
        if not stripe_sub_obj or not payment_history_obj:
            logger.error("Missing stripe_sub_obj or payment_history_obj for subscription creation/update.")
            return None

        current_user = payment_history_obj.user
        if not current_user and payment_history_obj.user_id:
            try:
                current_user = CustomUser.objects.get(id=payment_history_obj.user_id)
            except CustomUser.DoesNotExist:
                logger.error(f"User with ID {payment_history_obj.user_id} not found, referenced by PH {payment_history_obj.id}.")
                # If user is critical and not found, we might need to stop. For now, it will proceed and try to link later if possible.

        if not current_user: # If still no user after trying to fetch by user_id
            logger.error(f"Critical: No user associated with PaymentHistory {payment_history_obj.id}. Cannot create/update subscription without a user.")
            return None

        # Determine the Stripe price and product IDs for logging/reference
        stripe_price_id = None
        stripe_product_id = None # For logging/reference

        try: # Outer try for all price ID determination logic
            # Attempt 1: From stripe_sub_obj.items.data[0].price
            if hasattr(stripe_sub_obj, 'items') and stripe_sub_obj.items and \
               hasattr(stripe_sub_obj.items, 'data') and stripe_sub_obj.items.data and \
               len(stripe_sub_obj.items.data) > 0:
                item = stripe_sub_obj.items.data[0]
                if hasattr(item, 'price') and item.price and hasattr(item.price, 'id'):
                    stripe_price_id = item.price.id
                    if hasattr(item.price, 'product'):
                        product_data = item.price.product
                        if isinstance(product_data, str):
                            stripe_product_id = product_data
                        elif hasattr(product_data, 'id'):
                            stripe_product_id = product_data.id
                    logger.info(f"Stripe Sub {stripe_sub_obj.id}: Found price_id={stripe_price_id}, product_id={stripe_product_id} from items.data[0].price")
                else:
                    logger.warning(f"Stripe Sub {stripe_sub_obj.id}, Item {getattr(item, 'id', 'N/A')}: No price or price.id found on item.")
            else:
                logger.info(f"Stripe Sub {stripe_sub_obj.id}: items.data not available or empty. Trying other methods for price_id.")

            # Attempt 2: Fallback to top-level plan (e.g., stripe_sub_obj.plan)
            if not stripe_price_id and hasattr(stripe_sub_obj, 'plan') and stripe_sub_obj.plan and hasattr(stripe_sub_obj.plan, 'id'):
                stripe_price_id = stripe_sub_obj.plan.id
                if hasattr(stripe_sub_obj.plan, 'product'):
                    product_data_plan = stripe_sub_obj.plan.product
                    if isinstance(product_data_plan, str):
                        stripe_product_id = product_data_plan
                    elif hasattr(product_data_plan, 'id'):
                        stripe_product_id = product_data_plan.id
                logger.info(f"Stripe Sub {stripe_sub_obj.id}: Used fallback stripe_price_id={stripe_price_id} from subscription.plan")

            # Attempt 3: Fallback to payment_history_obj.gateway_specific_details
            if not stripe_price_id and payment_history_obj.gateway_specific_details:
                ph_stripe_price_id = payment_history_obj.gateway_specific_details.get('stripe_price_id')
                if ph_stripe_price_id:
                    stripe_price_id = ph_stripe_price_id
                    if not stripe_product_id: # Also try to get product_id from PH
                        stripe_product_id = payment_history_obj.gateway_specific_details.get('stripe_product_id')
                    logger.info(f"Stripe Sub {stripe_sub_obj.id}: Used stripe_price_id={stripe_price_id} from payment_history.gateway_specific_details.")

        except Exception as e_outer: # Catches errors from the main price ID determination logic
            logger.error(f"Stripe Sub {stripe_sub_obj.id}: Critical error in determining stripe_price_id or product_id: {e_outer}", exc_info=True)
            stripe_price_id = None # Ensure these are reset on outer error
            stripe_product_id = None

        # Logging the final determined IDs (or lack thereof)
        if stripe_price_id:
            log_msg_ids = f"Stripe Sub {stripe_sub_obj.id} (PH: {payment_history_obj.id}): Associated with Stripe Price ID {stripe_price_id}"
            if stripe_product_id:
                log_msg_ids += f" and Product ID {stripe_product_id}."
            else:
                log_msg_ids += " (Product ID not determined)."
            logger.info(log_msg_ids)
        else:
            logger.warning(f"Stripe Sub {stripe_sub_obj.id} (PH: {payment_history_obj.id}): Stripe Price ID ultimately NOT determined. Local subscription may lack plan linkage.")

        logger.info(f"Stripe Sub {stripe_sub_obj.id} (PH: {payment_history_obj.id}): Period fields will be fetched from Stripe directly when needed.")

        sub_status = stripe_sub_obj.get('status', getattr(stripe_sub_obj, 'status', None))
        sub_metadata_dict = stripe_sub_obj.get('metadata', getattr(stripe_sub_obj, 'metadata', None)) or {}

        defaults = {
            'user': current_user, # Crucially ensure user is set from PH
            'status': sub_status, # Use the status determined above
            'metadata': sub_metadata_dict,
        }

        try:
            # Use the user object directly in the defaults for update_or_create
            subscription_record, created = Subscription.objects.update_or_create(
                stripe_subscription_id=stripe_sub_obj.id, # Use the reliably extracted ID
                defaults=defaults
            )
            action = "created" if created else "updated"
            
            # Post-creation/update, ensure user is linked if somehow missed (shouldn't be if current_user is set in defaults)
            if not subscription_record.user and current_user:
                subscription_record.user = current_user
                subscription_record.save(update_fields=['user'])
                logger.info(f"Ensured User is set on {action} Subscription {subscription_record.id}")
            elif created and not current_user:
                 logger.warning(f"Newly created Subscription {subscription_record.id} but current_user was None. This is highly unexpected after initial checks.")

            # If created, and user was on PH but not on subscription (e.g. race), ensure user is set on record
            if created and current_user and not subscription_record.user:
                subscription_record.user = current_user
                # Ensure all default fields are set again if any were missed or if user needed linking.
                # This is a bit redundant if update_or_create worked perfectly, but safer.
                current_defaults_for_save = defaults.copy()
                for key, value in current_defaults_for_save.items():
                    setattr(subscription_record, key, value)
                subscription_record.save() 
                logger.info(f"Ensured User and all defaults are set on newly created Subscription {subscription_record.id}")
            elif created and not current_user:
                 logger.warning(f"Newly created Subscription {subscription_record.id} but current_user was None. This is highly unexpected after initial checks.")

            # Print statement updated to reflect that we don't use local plans anymore
            print(f"Successfully {action} local Subscription {subscription_record.id} (User: {subscription_record.user.id if subscription_record.user else 'N/A'}, Status: {subscription_record.status}) for Stripe Sub {stripe_sub_obj.id}.")
            return subscription_record
        except Exception as e:
            logger.error(f"Error creating/updating local Subscription for Stripe Sub {stripe_sub_obj.id} (User: {current_user.id if current_user else 'N/A'}): {e}", exc_info=True)
            return None

    @transaction.atomic
    def handle_webhook_event(self, payload: bytes, sig_header: str):
        """Handles incoming Stripe webhook events."""
        # print("---- RAW WEBHOOK PAYLOAD RECEIVED ----")
        try:
            payload_str = payload.decode('utf-8')
            self.stripe_webhook_logger.info(f"Received webhook payload: {payload_str}")
            # print(payload.decode('utf-8')) # Decode bytes to string for printing
        except Exception as e:
            self.stripe_webhook_logger.error(f"Error decoding/logging payload: {e}")
            # print(f"Error decoding payload for printing: {e}")
        # print("------------------------------------")

        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, STRIPE_CONFIG['WEBHOOK_SECRET']
            )
        except ValueError as e: # Invalid payload
            self.stripe_webhook_logger.error(f"Webhook Error: Invalid payload. {str(e)}")
            return False # Or raise, to return 400
        except stripe.error.SignatureVerificationError as e:
            self.stripe_webhook_logger.error(f"Webhook Error: Invalid signature. {str(e)}")
            return False # Or raise, to return 400
        except Exception as e:
            logger.error(f"Webhook Error: Could not construct event. {str(e)}", exc_info=True)
            return False

        event_data = event.data.object
        event_id = event.id
        event_type = event.type
        print(f"Webhook Received: ID={event_id}, Type={event_type}, Livemode={event.livemode}")

        payment_history, lookup_details = self.find_payment_history_for_event(event_id, event_type, event_data)

        if not payment_history:
            print(f"Webhook {event_id} ({event_type}): No PaymentHistory found. Lookup: {lookup_details}. Event might be unrelated or PH creation failed.")
            # Depending on the event type, you might still want to process some events (e.g. customer.created not tied to a specific PH)
            # For now, we'll only process events linked to a PH
            return True # Acknowledge to Stripe to prevent retries for unprocessable events

        print(f"Event {event_id}: Associated with PaymentHistory {payment_history.id} via {lookup_details['method']} ('{lookup_details['value']}')")

        # Basic Idempotency: If PH is already in a final state, and event is for that same state, skip.
        # Ensure 'stripe_price_id' is present in metadata for new flow
        if hasattr(event_data, 'metadata') and event_data.metadata and event_data.metadata.get('stripe_price_id'):
            print(f"Event {event_id}: Found stripe_price_id {event_data.metadata.get('stripe_price_id')} in event metadata.")
        # Check for price on line item more safely
        elif hasattr(event_data, 'lines') and event_data.lines and hasattr(event_data.lines, 'data') and event_data.lines.data and len(event_data.lines.data) > 0 and hasattr(event_data.lines.data[0], 'price') and event_data.lines.data[0].price:
             # For checkout.session.completed, price ID is in line items
            price_id_from_line_item = event_data.lines.data[0].price.id
            print(f"Event {event_id}: Found stripe_price_id {price_id_from_line_item} via event_data.lines.data[0].price.id.")
            # Potentially inject this into metadata if needed by downstream logic, or pass directly
            if not (hasattr(event_data, 'metadata') and event_data.metadata):
                event_data.metadata = {} # Ensure metadata exists
            if event_data.metadata is not None and 'stripe_price_id' not in event_data.metadata: # Check if None before adding key
                 event_data.metadata['stripe_price_id'] = price_id_from_line_item
                 if event_data.lines.data[0].price.product: # Add product ID too if available
                    event_data.metadata['stripe_product_id'] = event_data.lines.data[0].price.product


        if payment_history.status in FINAL_PAYMENT_STATUSES:
            if (event_type in ('checkout.session.completed', 'invoice.paid', 'payment_intent.succeeded', 'checkout.session.async_payment_succeeded') and payment_history.status == 'succeeded') or \
               (event_type in ('invoice.payment_failed', 'payment_intent.payment_failed', 'checkout.session.async_payment_failed') and payment_history.status == 'failed') or \
               (event_type == 'customer.subscription.deleted' and payment_history.status == 'cancelled'):
                print(f"Webhook {event_id}: PaymentHistory {payment_history.id} already in relevant final status '{payment_history.status}'. Skipping redundant event {event_type}.")
                return True # Acknowledge

        # --- Event Handlers Mapping ---
        event_handlers = {
            'checkout.session.completed': self._handle_checkout_session_completed,
            'checkout.session.async_payment_succeeded': self._handle_checkout_async_succeeded,
            'checkout.session.async_payment_failed': self._handle_checkout_async_failed,
            'invoice.paid': self._handle_invoice_paid,
            'invoice.payment_failed': self._handle_invoice_payment_failed,
            'invoice.upcoming': self._handle_invoice_upcoming, # For renewal notifications
            'customer.subscription.created': self._handle_subscription_event, # Generic handler
            'customer.subscription.updated': self._handle_subscription_event,
            'customer.subscription.deleted': self._handle_subscription_deleted,
            'customer.subscription.trial_will_end': self._handle_subscription_trial_will_end,
            'payment_intent.succeeded': self._handle_payment_intent_succeeded,
            'payment_intent.payment_failed': self._handle_payment_intent_payment_failed,
            # Add more handlers as needed:
            # 'charge.succeeded', 'charge.failed', 'charge.refunded', 
            # 'customer.created', 'customer.updated', 'customer.deleted',
            # 'payment_method.attached'
        }

        handler_method = event_handlers.get(event_type)
        if handler_method:
            try:
                # Pass the raw event_data and the resolved payment_history
                # The handler itself will decide what to do based on event_type if needed
                handler_method(event_data, payment_history) # Call bound method without explicit self
                print(f"Webhook {event_id} ({event_type}): Successfully handled.")
                return True
            except Exception as e:
                logger.error(f"Error in handler for {event_type} (PH: {payment_history.id if payment_history else 'N/A'}): {e}", exc_info=True)
                # Decide if we should return True (ack) or False (retry)
                return False # Let Stripe retry if handler fails
        else:
            print(f"Webhook {event_id}: No handler for event type {event_type}.")
            # Log unhandled event types if they are important
            self.stripe_webhook_logger.info(f"Unhandled webhook event type: {event_type} for PH: {payment_history.id if payment_history else 'N/A'}")
            return True # Acknowledge unhandled events

    # --- Event Handler Implementations ---

    def _handle_checkout_session_completed(self, checkout_session, payment_history: PaymentHistory):
        """Handles successful checkout session, focusing on subscription setup if present."""
        print(f"Processing checkout.session.completed for PH {payment_history.id}")
        # Update PaymentHistory from CheckoutSession data
        payment_history.status = 'succeeded' # Mark as succeeded based on this primary event
        payment_history.gateway_transaction_id = checkout_session.id # Already set, but confirm
        
        details = payment_history.gateway_specific_details or {}
        details.update({
            'checkout_session_id': checkout_session.id,
            'payment_status': checkout_session.payment_status,
            'customer_id': checkout_session.customer,
            'subscription_id': checkout_session.subscription, # For subscriptions
        })
        if checkout_session.amount_total is not None: # Stripe returns amount in cents
             details['amount_total_cents'] = checkout_session.amount_total
        if checkout_session.currency:
             details['currency'] = checkout_session.currency.lower()
        
        payment_intent_id_for_ph = checkout_session.payment_intent # Default for one-time payments

        if checkout_session.subscription:
            print(f"PH {payment_history.id}: Checkout session created subscription {checkout_session.subscription}")
            try:
                # It's crucial to expand items and their prices/products for subscription details.
                stripe_sub = stripe.Subscription.retrieve(
                    checkout_session.subscription, 
                    expand=[
                        'items.data.price.product', 
                        'plan.product', # For older/fallback cases
                        'default_payment_method',
                        'latest_invoice' # <<< EXPAND LATEST INVOICE
                    ]
                )
                payment_history.gateway_subscription_id = stripe_sub.id # Link PH to Stripe Sub ID
                
                # For subscriptions, the relevant PI is on the latest_invoice
                if stripe_sub.latest_invoice and hasattr(stripe_sub.latest_invoice, 'payment_intent') and stripe_sub.latest_invoice.payment_intent:
                    payment_intent_id_for_ph = stripe_sub.latest_invoice.payment_intent
                    details['latest_invoice_id'] = stripe_sub.latest_invoice.id
                    print(f"PH {payment_history.id}: Found PI {payment_intent_id_for_ph} from latest_invoice {stripe_sub.latest_invoice.id} of subscription {stripe_sub.id}")
                elif stripe_sub.latest_invoice and hasattr(stripe_sub.latest_invoice, 'id'): # Store invoice ID even if PI is missing for some reason
                    details['latest_invoice_id'] = stripe_sub.latest_invoice.id
                    print(f"PH {payment_history.id}: Found latest_invoice {stripe_sub.latest_invoice.id} for subscription {stripe_sub.id} (PI not found on it).")


                local_sub_record = self._create_or_update_local_subscription(stripe_sub, payment_history)
                
                if local_sub_record:
                    payment_history.subscription = local_sub_record # Link PH to local Subscription
                    print(f"PH {payment_history.id}: Linked to local Subscription {local_sub_record.id}")

                    if payment_history.user and not local_sub_record.user:
                        local_sub_record.user = payment_history.user
                        local_sub_record.save(update_fields=['user'])
                        print(f"Local Subscription {local_sub_record.id} also linked to User {payment_history.user.id}")
                else:
                    print(f"PH {payment_history.id}: Failed to create/update local subscription record from Stripe Sub {stripe_sub.id if 'stripe_sub' in locals() and stripe_sub else stripe_session.subscription}")

            except stripe.error.StripeError as e:
                logger.error(f"PH {payment_history.id}: Stripe error retrieving subscription {checkout_session.subscription}: {e}", exc_info=True)
            except Exception as e: # Other errors (e.g., DB)
                logger.error(f"PH {payment_history.id}: Error processing subscription part of checkout.session.completed: {e}", exc_info=True)
        
        else: # One-time payment from checkout session
            # If there's a task_id, try to complete it (logic depends on AnalysisRecord)
            if payment_history.task_id:
                try:
                    analysis_record = AnalysisRecord.objects.get(task_id=payment_history.task_id)
                    # Update analysis_record or perform related actions
                    analysis_record.payment_status = 'paid' 
                    # analysis_record.save() # Adapt as per AnalysisRecord model
                    print(f"PH {payment_history.id}: Marked associated AnalysisRecord {analysis_record.id} (task {payment_history.task_id}) as paid.")
                except AnalysisRecord.DoesNotExist:
                    logger.warning(f"PH {payment_history.id}: AnalysisRecord with task_id {payment_history.task_id} not found for one-time payment.")
                except Exception as e:
                    logger.error(f"PH {payment_history.id}: Error updating AnalysisRecord for task_id {payment_history.task_id}: {e}", exc_info=True)
            print(f"PH {payment_history.id}: Processed as one-time payment via checkout.session.completed.")

        # Ensure all relevant fields are saved, including the new subscription link
        payment_history.gateway_charge_id = payment_intent_id_for_ph # Set the determined PI ID
        payment_history.gateway_specific_details = details # Save updated details

        fields_to_update = ['status', 'gateway_transaction_id', 'gateway_charge_id', 'gateway_specific_details']
        if payment_history.gateway_subscription_id: # Only add if it was set
            fields_to_update.append('gateway_subscription_id')
        payment_history.save(update_fields=fields_to_update)
        
        print(f"PH {payment_history.id}: Successfully processed checkout.session.completed.")
        return True

    def _handle_checkout_async_succeeded(self, event_data, payment_history: PaymentHistory):
        """Handles asynchronous payment success for a checkout session."""
        # This is similar to checkout.session.completed but for async payment methods
        print(f"Processing checkout.session.async_payment_succeeded for PH {payment_history.id}")
        # Essentially, treat it like a checkout.session.completed event for updating local records
        return self._handle_checkout_session_completed(event_data, payment_history)

    def _handle_checkout_async_failed(self, event_data, payment_history: PaymentHistory):
        """Handles asynchronous payment failure for a checkout session."""
        print(f"Processing checkout.session.async_payment_failed for PH {payment_history.id}")
        payment_history.status = 'failed'
        details = payment_history.gateway_specific_details or {}
        details.update({
            'checkout_session_id': event_data.id,
            'payment_status': 'failed', # Reflects the async failure
            'failure_reason': event_data.get('last_error', {}).get('message') if hasattr(event_data, 'last_error') and event_data.last_error else 'Unknown async failure'
        })
        payment_history.gateway_specific_details = details
        payment_history.save()
        print(f"PH {payment_history.id}: Marked as failed due to checkout.session.async_payment_failed.")
        return True

    def _handle_invoice_paid(self, invoice, payment_history: PaymentHistory):
        """Handles successful invoice payment, often for subscription renewals."""
        print(f"Processing invoice.paid for PH {payment_history.id}, Invoice ID: {invoice.id}")
        
        # If this invoice payment is for a subscription, update the local subscription
        stripe_subscription_id_from_invoice = invoice.get('subscription') # MODIFIED: Safe access
        if stripe_subscription_id_from_invoice:
            try:
                stripe_sub = stripe.Subscription.retrieve(
                    stripe_subscription_id_from_invoice, # MODIFIED: Use the retrieved ID
                    expand=[
                        'items.data.price.product', 
                        'plan.product',
                        'default_payment_method'
                    ]
                )
                # Update the local subscription record with potentially new period dates, status etc.
                local_sub_record = self._create_or_update_local_subscription(stripe_sub, payment_history)
                if local_sub_record:
                    print(f"PH {payment_history.id}: Local Subscription {local_sub_record.id} updated from invoice.paid.")
                    # Ensure PH is linked if it wasn't before (e.g. for renewal PHs)
                    # payment_history.subscription = local_sub_record # This field no longer exists on PaymentHistory
                    if not payment_history.gateway_subscription_id:
                        payment_history.gateway_subscription_id = stripe_sub.id
                else:
                    print(f"PH {payment_history.id}: Failed to update local subscription from invoice.paid for Stripe Sub {stripe_sub.id if 'stripe_sub' in locals() and stripe_sub else stripe_subscription_id_from_invoice}")

            except stripe.error.StripeError as e:
                logger.error(f"PH {payment_history.id}: Stripe error retrieving subscription {stripe_subscription_id_from_invoice} for invoice.paid: {e}", exc_info=True)
            except Exception as e:
                 logger.error(f"PH {payment_history.id}: Error updating local subscription for invoice.paid: {e}", exc_info=True)
        else:
            logger.info(f"PH {payment_history.id}: Invoice {invoice.id} is not associated with a subscription (invoice.subscription is null or missing).")
        
        # Update PaymentHistory status and details
        # Only update to 'succeeded' if it's not already in a more final state from another event
        if payment_history.status not in FINAL_PAYMENT_STATUSES or payment_history.status == 'pending':
            payment_history.status = 'succeeded'
        
        # Safely get charge or payment_intent ID from invoice
        invoice_charge_id = invoice.get('charge')
        invoice_payment_intent_id = invoice.get('payment_intent')
        payment_history.gateway_charge_id = invoice_charge_id or invoice_payment_intent_id # Link to charge/PI
        
        details = payment_history.gateway_specific_details or {}
        details.update({
            'invoice_id': invoice.id,
            'invoice_status': invoice.status,
            'amount_paid_cents': invoice.amount_paid,
            'billing_reason': invoice.billing_reason,
        })
        payment_history.gateway_specific_details = details
        payment_history.save()
        print(f"PH {payment_history.id}: Successfully processed invoice.paid.")
        return True

    def _handle_invoice_payment_failed(self, invoice, payment_history: PaymentHistory):
        """Handles failed invoice payment."""
        print(f"Processing invoice.payment_failed for PH {payment_history.id}, Invoice ID: {invoice.id}")
        payment_history.status = 'failed'
        details = payment_history.gateway_specific_details or {}
        details.update({
            'invoice_id': invoice.id,
            'invoice_status': invoice.status, # Should be 'open' or 'uncollectible'
            'failure_reason': invoice.last_finalization_error.message if invoice.last_finalization_error else 'Unknown failure',
            'next_payment_attempt_ts': invoice.next_payment_attempt,
        })
        payment_history.gateway_specific_details = details

        # If related to a subscription, update its status too (e.g., to 'past_due')
        if invoice.subscription:
            try:
                stripe_sub = stripe.Subscription.retrieve(invoice.subscription, expand=['items.data.price.product'])
                self._create_or_update_local_subscription(stripe_sub, payment_history) # This will update status based on stripe_sub.status
            except stripe.error.StripeError as e:
                logger.error(f"PH {payment_history.id}: Stripe error retrieving subscription {invoice.subscription} for invoice.payment_failed: {e}", exc_info=True)

        payment_history.save()
        print(f"PH {payment_history.id}: Marked as failed due to invoice.payment_failed.")
        return True

    def _handle_invoice_upcoming(self, invoice, payment_history: Optional[PaymentHistory]): # payment_history might be None
        """Handles upcoming invoice notifications, e.g., for renewals."""
        # This event might not always have a direct existing PaymentHistory record,
        # especially for automatically generated renewal invoices.
        # We might create a new 'pending' PH or just log/notify.
        
        user = None
        if payment_history and payment_history.user:
            user = payment_history.user
        elif invoice.customer: # Try to find user via stripe_customer_id
            try:
                user = CustomUser.objects.get(stripe_customer_id=invoice.customer)
            except CustomUser.DoesNotExist:
                logger.warning(f"Invoice.upcoming (Invoice: {invoice.id}): No user found for Stripe customer {invoice.customer}")
        
        print(f"Processing invoice.upcoming for Invoice ID: {invoice.id}, User: {user.id if user else 'N/A'}")
        
        # Potentially send a notification to the user
        # TODO: Implement user notification logic here if desired
        # Example: send_renewal_notification(user, invoice.amount_due, invoice.due_date)
        
        # Optionally, create a pending PaymentHistory record if one doesn't exist for this upcoming payment.
        # This helps in tracking the lifecycle if the payment later succeeds or fails.
        if invoice.subscription and user: # Ensure it's for a subscription and we have a user
            try:
                # Check if a PH already exists for this specific upcoming invoice (less likely for 'upcoming')
                # Or more likely, a PH for the subscription's current/next cycle.
                # For simplicity, we could log and let 'invoice.created' or 'invoice.paid' handle PH creation/updates.
                
                # If we want to create a pending PH:
                # local_subscription = Subscription.objects.get(stripe_subscription_id=invoice.subscription, user=user)
                # new_ph = PaymentHistory.objects.create(
                #     user=user,
                #     amount=Decimal(invoice.amount_due / 100), # Stripe amount in cents
                #     currency=invoice.currency.lower(),
                #     payment_gateway='stripe',
                #     status='pending_renewal', # Custom status
                #     gateway_subscription_id=invoice.subscription,
                #     subscription=local_subscription,
                #     gateway_specific_details={
                #         'upcoming_invoice_id': invoice.id,
                #         'due_date_ts': invoice.due_date,
                #         'billing_reason': invoice.billing_reason
                #     }
                # )
                # print(f"Created pending_renewal PaymentHistory {new_ph.id} for upcoming invoice {invoice.id}")
                pass # For now, just log. More complex logic can be added.

            except Subscription.DoesNotExist:
                 logger.warning(f"Invoice.upcoming (Invoice: {invoice.id}): Corresponding local Subscription not found for Stripe Sub ID {invoice.subscription} and User {user.id if user else 'N/A'}.")
            except Exception as e:
                 logger.error(f"Invoice.upcoming (Invoice: {invoice.id}): Error processing for user {user.id if user else 'N/A'}: {e}", exc_info=True)

        print(f"PH {'N/A' if not payment_history else payment_history.id}: Successfully noted invoice.upcoming.")
        return True


    def _handle_subscription_event(self, stripe_sub_object, payment_history: PaymentHistory):
        """Generic handler for customer.subscription.created and customer.subscription.updated."""
        # payment_history is crucial here for linking to user and existing records.
        # If find_payment_history_for_event failed to find a PH for a 'created' event, this might be an issue.
        # However, checkout.session.completed should be the primary driver for initial creation.
        print(f"Processing subscription event for Stripe Sub ID: {stripe_sub_object.id}, PH: {payment_history.id}")

        local_sub_record = self._create_or_update_local_subscription(stripe_sub_object, payment_history)
        if local_sub_record:
            print(f"PH {payment_history.id}: Local Subscription {local_sub_record.id} created/updated from subscription event.")
            # Ensure PaymentHistory is linked to this local subscription and Stripe subscription ID
            # payment_history.subscription = local_sub_record # COMMENTED OUT/REMOVED
            if not payment_history.gateway_subscription_id: # Set if not already set
                payment_history.gateway_subscription_id = stripe_sub_object.id
            # payment_history.save(update_fields=['subscription', 'gateway_subscription_id']) # 'subscription' field is now valid -- MODIFIED
            payment_history.save(update_fields=['gateway_subscription_id']) # Save only gateway_subscription_id if it was updated
        else:
            print(f"PH {payment_history.id}: Failed to create/update local subscription from Stripe Sub {stripe_sub_object.id} via subscription event.")
        return True

    def _handle_subscription_deleted(self, stripe_sub_object, payment_history: PaymentHistory):
        """Handles customer.subscription.deleted event."""
        print(f"Processing subscription.deleted for Stripe Sub ID: {stripe_sub_object.id}, PH: {payment_history.id}")
        
        # Update local subscription status to 'canceled' or reflect the ended_at/canceled_at timestamps.
        local_sub_record = self._create_or_update_local_subscription(stripe_sub_object, payment_history)
        if local_sub_record:
            print(f"PH {payment_history.id}: Local Subscription {local_sub_record.id} status updated due to subscription.deleted.")
            # payment_history status might also need update, e.g., to 'cancelled' if it's the final one.
            if payment_history.status not in FINAL_PAYMENT_STATUSES:
                 payment_history.status = 'cancelled' # Or based on subscription_record.status
                 payment_history.save(update_fields=['status'])
        else:
            # If local sub couldn't be found/updated, log it. This implies an issue.
            logger.error(f"PH {payment_history.id}: Could not find/update local subscription for deleted Stripe Sub ID {stripe_sub_object.id}.")
            # Potentially try to find Subscription by stripe_subscription_id directly if PH is not perfectly linked
            try:
                sub_to_mark_deleted = Subscription.objects.get(stripe_subscription_id=stripe_sub_object.id)
                sub_to_mark_deleted.status = stripe_sub_object.status # Should be 'canceled'
                # Removed ended_at and canceled_at assignments as these fields will be removed from the model
                # sub_to_mark_deleted.ended_at = datetime.datetime.fromtimestamp(stripe_sub_object.ended_at, tz=datetime.timezone.utc) if stripe_sub_object.ended_at else timezone.now()
                # sub_to_mark_deleted.canceled_at = datetime.datetime.fromtimestamp(stripe_sub_object.canceled_at, tz=datetime.timezone.utc) if stripe_sub_object.canceled_at else timezone.now()
                sub_to_mark_deleted.save() # Save only status
                print(f"Directly updated local Subscription for {stripe_sub_object.id} to status {sub_to_mark_deleted.status}.")
            except Subscription.DoesNotExist:
                logger.error(f"No local subscription found with Stripe ID {stripe_sub_object.id} to mark as deleted directly.")
            except Exception as e:
                logger.error(f"Error directly updating subscription {stripe_sub_object.id} as deleted: {e}")

        return True

    def _handle_subscription_trial_will_end(self, stripe_sub_object, payment_history: PaymentHistory):
        """Handles subscription trial ending soon."""
        print(f"Processing subscription.trial_will_end for Stripe Sub ID: {stripe_sub_object.id}, PH: {payment_history.id}")
        # TODO: Implement user notification logic here (e.g., email)
        # E.g., send_trial_ending_notification(payment_history.user, stripe_sub_object.trial_end)
        
        # Update local subscription record if necessary (trial_end date might be metadata)
        local_sub_record = self._create_or_update_local_subscription(stripe_sub_object, payment_history)
        if local_sub_record:
            print(f"PH {payment_history.id}: Noted trial_will_end for local Subscription {local_sub_record.id}.")
        return True

    def _handle_payment_intent_succeeded(self, payment_intent, payment_history: PaymentHistory):
        """Handles successful payment intent."""
        print(f"Processing payment_intent.succeeded for PI ID: {payment_intent.id}, PH: {payment_history.id}")
        
        # This event is often supplemental. checkout.session.completed or invoice.paid are primary.
        # Update PaymentHistory if it's still pending.
        if payment_history.status == 'pending':
            payment_history.status = 'succeeded'
        
        # Ensure gateway_charge_id is set to the Payment Intent ID
        payment_history.gateway_charge_id = payment_intent.id
        details = payment_history.gateway_specific_details or {}
        details.update({
            'payment_intent_id': payment_intent.id,
            'payment_intent_status': payment_intent.status, # Should be 'succeeded'
            'amount_received_cents': payment_intent.amount_received,
        })
        # If PI is for an invoice/subscription, those IDs might be in PI metadata or invoice field
        if hasattr(payment_intent, 'invoice') and payment_intent.invoice:
            details['invoice_id'] = payment_intent.invoice
        if hasattr(payment_intent, 'subscription') and payment_intent.subscription: # Less common on PI itself
            details['subscription_id'] = payment_intent.subscription
            if not payment_history.gateway_subscription_id:
                payment_history.gateway_subscription_id = payment_intent.subscription

        payment_history.gateway_specific_details = details
        payment_history.save()
        
        # If this payment intent is tied to a subscription that wasn't fully processed,
        # this might be a point to re-check/update the subscription.
        if payment_history.subscription and payment_history.gateway_subscription_id:
            try:
                stripe_sub = stripe.Subscription.retrieve(
                    payment_history.gateway_subscription_id,
                    expand=['items.data.price.product']
                )
                self._create_or_update_local_subscription(stripe_sub, payment_history)
            except stripe.error.StripeError as e:
                logger.error(f"PI Succeeded (PH {payment_history.id}): Stripe error retrieving linked subscription {payment_history.gateway_subscription_id}: {e}")
            except Exception as e:
                logger.error(f"PI Succeeded (PH {payment_history.id}): Error updating linked subscription {payment_history.gateway_subscription_id}: {e}")


        print(f"PH {payment_history.id}: Successfully processed payment_intent.succeeded.")
        return True

    def _handle_payment_intent_payment_failed(self, payment_intent, payment_history: PaymentHistory):
        """Handles failed payment intent."""
        print(f"Processing payment_intent.payment_failed for PI ID: {payment_intent.id}, PH: {payment_history.id}")
        payment_history.status = 'failed'
        payment_history.gateway_charge_id = payment_intent.id # Store the failed PI ID
        details = payment_history.gateway_specific_details or {}
        details.update({
            'payment_intent_id': payment_intent.id,
            'payment_intent_status': payment_intent.status, # Should be 'requires_payment_method' or similar
            'failure_reason': payment_intent.last_payment_error.message if payment_intent.last_payment_error else 'Unknown PI failure',
            'failure_code': payment_intent.last_payment_error.code if payment_intent.last_payment_error else None,
        })
        payment_history.gateway_specific_details = details
        payment_history.save()
        print(f"PH {payment_history.id}: Marked as failed due to payment_intent.payment_failed.")
        return True

    def construct_event_locally(self, payload_json_str: str, sig_header: str):
        """Utility to construct event from JSON string for testing/dev purposes."""
        return stripe.Webhook.construct_event(
            payload_json_str.encode('utf-8'), sig_header, STRIPE_CONFIG['WEBHOOK_SECRET']
        )