import os
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured

# Stripe configuration fetched from Django settings (which should load from .env or other sources)

def get_stripe_setting(setting_name, default=None, required=False):
    """Helper to fetch Stripe settings and raise ImproperlyConfigured if required and not found."""
    value = getattr(settings, setting_name, default)
    if required and value is None:
        raise ImproperlyConfigured(f"Required Stripe setting '{setting_name}' is not configured in Django settings.")
    return value

STRIPE_CONFIG = {
    'PUBLISHABLE_KEY': get_stripe_setting('STRIPE_PUBLISHABLE_KEY', required=False), # Often only needed in frontend
    'SECRET_KEY': get_stripe_setting('STRIPE_SECRET_KEY', required=True),
    'WEBHOOK_SECRET': get_stripe_setting('STRIPE_WEBHOOK_SECRET', required=True),
    'CURRENCY': get_stripe_setting('STRIPE_CURRENCY', default='usd'),
    'PAYMENT_METHODS': get_stripe_setting('STRIPE_PAYMENT_METHODS', default=['card']),
}

# The helper function `get_stripe_setting` already handles raising ImproperlyConfigured
# for required settings if they are missing from Django's settings.

# You can override or add more specific settings here if needed
# For example, for different environments or specific features.

# Ensure critical keys are set, especially the secret key and webhook secret for backend operations.
if not STRIPE_CONFIG['SECRET_KEY'] or STRIPE_CONFIG['SECRET_KEY'] == 'your_stripe_secret_key':
    # In a real application, you might raise an ImproperlyConfigured error or log a critical warning.
    print("WARNING: STRIPE_SECRET_KEY is not configured. Please set it in your environment variables.")

if not STRIPE_CONFIG['WEBHOOK_SECRET'] or STRIPE_CONFIG['WEBHOOK_SECRET'] == 'your_stripe_webhook_secret':
    print("WARNING: STRIPE_WEBHOOK_SECRET is not configured. Please set it in your environment variables.") 