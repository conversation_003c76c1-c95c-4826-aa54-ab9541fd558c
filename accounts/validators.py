import re
import base64
import io
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from PIL import Image

class ComplexPasswordValidator:
    """
    Validate that the password meets minimum complexity requirements:
    - At least 8 characters long
    - Contains at least one uppercase letter
    - Contains at least one lowercase letter
    - Contains at least one digit
    - Contains at least one special character
    """
    
    def __init__(self):
        self.password_regex = re.compile(
            r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&_])[A-Za-z\d@$!%*?&_]{8,}$'
        )

    def validate(self, password, user=None):
        if not self.password_regex.match(password):
            raise ValidationError(
                _(
                    "Password must be at least 8 characters long and contain:"
                    " uppercase letter, lowercase letter, number and special character."
                ),
                code='password_too_simple',
            )

    def get_help_text(self):
        return _(
            "Your password must be at least 8 characters long and contain:"
            " uppercase letter, lowercase letter, number and special character."
        )

def validate_logo(file):
    """
    Validates the uploaded logo file.
    - Max size: 1MB
    - Max dimensions: 1000x1000
    - Type: PNG
    """
    # Check file size (1MB)
    if file.size > 1 * 1024 * 1024:
        raise ValidationError(_('File too large. Maximum size is 1MB.'))

    # Check file type
    content_type = None
    if hasattr(file, 'file') and hasattr(file.file, 'content_type'):
        content_type = file.file.content_type
    elif hasattr(file, 'content_type'):
        content_type = file.content_type

    if content_type != 'image/png':
        raise ValidationError(_('Invalid file type. Only PNG files are allowed.'))

    try:
        # Open image to check dimensions
        img = Image.open(file)
        if img.width > 1000 or img.height > 1000:
            raise ValidationError(_('Image dimensions too large. Maximum size is 1000x1000 pixels.'))
    except Exception as e:
        raise ValidationError(_('Invalid image file. Please upload a valid PNG file.'))

def validate_logo_data(value):
    """
    Validates the base64 encoded logo data.
    - Max decoded size: 1MB
    - Max dimensions: 1000x1000
    - Type: PNG
    """
    try:
        # Check if the value is a valid base64 string
        if not value.startswith('data:image/png;base64,'):
            raise ValidationError(_("Logo data must be a base64 encoded PNG image."))

        # Remove the data URL prefix
        base64_data = value.replace('data:image/png;base64,', '')

        try:
            # Decode base64 data
            decoded_data = base64.b64decode(base64_data)
        except Exception:
            raise ValidationError(_("Invalid base64 data."))

        # Check size
        if len(decoded_data) > 1 * 1024 * 1024:  # 1MB
            raise ValidationError(_("Logo size cannot exceed 1MB."))

        # Validate image
        try:
            img = Image.open(io.BytesIO(decoded_data))
            if img.format != 'PNG':
                raise ValidationError(_("Logo must be a PNG image."))
            if img.width > 1000 or img.height > 1000:
                raise ValidationError(_("Logo dimensions cannot exceed 1000x1000 pixels."))
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            raise ValidationError(_("Invalid image data: %(error)s") % {'error': str(e)})
    except ValidationError:
        raise
    except Exception as e:
        raise ValidationError(_("Error processing logo data: %(error)s") % {'error': str(e)})