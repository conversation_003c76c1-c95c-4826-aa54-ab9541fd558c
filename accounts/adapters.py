from django.conf import settings
from allauth.account.adapter import DefaultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter


class CustomAccountAdapter(DefaultAccountAdapter):
    """Custom adapter for Django Allauth"""
    
    def save_user(self, request, user, form, commit=True):
        """Save a new user instance"""
        user = super().save_user(request, user, form, commit=False)
        user.is_active = False  # User needs to verify email
        user.is_verified = False
        if commit:
            user.save()
        return user


class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):
    """Custom adapter for social account authentication"""
    
    def save_user(self, request, sociallogin, form=None):
        """Save a new user instance from social login"""
        user = super().save_user(request, sociallogin, form)
        user.is_active = True  # Social users are automatically active
        user.is_verified = True  # Social users are automatically verified
        user.save()
        return user
