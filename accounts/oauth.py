import logging
import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils.translation import gettext as _
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import AllowAny
from .exceptions import OAuth2Error

User = get_user_model()
logger = logging.getLogger(__name__)


class GoogleLoginView(APIView):
    """View for Google OAuth2 login"""
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            # Get the authorization code from the request
            code = request.data.get('code')
            if not code:
                return Response(
                    {'error': _('Authorization code is required')},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Exchange the authorization code for an access token
            token_url = 'https://oauth2.googleapis.com/token'
            token_payload = {
                'code': code,
                'client_id': settings.GOOGLE_OAUTH2_CLIENT_ID,
                'client_secret': settings.GOOGLE_OAUTH2_CLIENT_SECRET,
                'redirect_uri': settings.GOOGLE_OAUTH2_REDIRECT_URI,
                'grant_type': 'authorization_code'
            }

            token_response = requests.post(token_url, data=token_payload)
            if token_response.status_code != 200:
                return Response(
                    {'error': _('Failed to obtain access token')},
                    status=status.HTTP_400_BAD_REQUEST
                )

            token_data = token_response.json()
            access_token = token_data.get('access_token')

            # Get user info from Google
            userinfo_url = 'https://www.googleapis.com/oauth2/v3/userinfo'
            userinfo_response = requests.get(
                userinfo_url,
                headers={'Authorization': f'Bearer {access_token}'}
            )
            
            if userinfo_response.status_code != 200:
                return Response(
                    {'error': _('Failed to get user info')},
                    status=status.HTTP_400_BAD_REQUEST
                )

            userinfo = userinfo_response.json()
            email = userinfo.get('email')

            if not email:
                return Response(
                    {'error': _('Email not provided by Google')},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Create or update user
            try:
                user = User.objects.get(email=email)
                # Update user info if needed
                user.first_name = userinfo.get('given_name', user.first_name)
                user.last_name = userinfo.get('family_name', user.last_name)
                user.save()
            except User.DoesNotExist:
                # Create a new user
                user = User.objects.create_user(
                    email=email,
                    first_name=userinfo.get('given_name', ''),
                    last_name=userinfo.get('family_name', ''),
                    is_active=True,
                    is_verified=True
                )

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)

            response = Response({
                'access': access_token,
                'refresh': refresh_token,
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'is_verified': user.is_verified,
                    'date_joined': user.date_joined
                }
            })

            # Set cookies
            response.set_cookie(
                'access_token',
                access_token,
                httponly=True,
                secure=not settings.DEBUG,
                samesite='Lax'
            )
            response.set_cookie(
                'refresh_token',
                refresh_token,
                httponly=True,
                secure=not settings.DEBUG,
                samesite='Lax'
            )

            return response

        except requests.RequestException as e:
            logger.error(f"Google OAuth request failed: {str(e)}")
            return Response(
                {'error': _('Failed to communicate with Google OAuth service')},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error during Google OAuth: {str(e)}", exc_info=True)
            return Response(
                {'error': _('An unexpected error occurred during authentication')},
                status=status.HTTP_400_BAD_REQUEST
            )
