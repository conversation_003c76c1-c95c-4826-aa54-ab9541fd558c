from django.conf import settings
from django.db import models
from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager, PermissionsMixin
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.validators import MaxValueValidator, MinValueValidator # Added validator imports
from .validators import validate_logo # Import the validator
import uuid # Uncomment uuid
# from tools.models import Pricing # Import Pricing from tools.models # Removed import


class CustomUserManager(BaseUserManager):
    """Custom user manager for email-based authentication"""

    def create_user(self, email, password=None, **extra_fields):
        """Create and save a regular user with the given email and password."""
        if not email:
            raise ValueError(_('The Email field must be set'))
        email = self.normalize_email(email)
        # Ensure auth_provider is set, defaulting to 'email' if not provided
        extra_fields.setdefault('auth_provider', 'email')
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """Create and save a superuser with the given email and password."""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        extra_fields.setdefault('is_verified', True)
        extra_fields.setdefault('auth_provider', 'email') # Superusers created manually are via 'email'

        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))

        return self.create_user(email, password, **extra_fields)


class CustomUser(AbstractBaseUser, PermissionsMixin):
    """Custom user model that uses email as the unique identifier"""
    AUTH_PROVIDERS = [
        ('email', _('Email')),
        ('google', _('Google')),
    ]

    BUSINESS_TYPE_CHOICES = [
        ('digital_agency', _('Digital Agency')),
        ('saas', _('SaaS')),
        ('freelance', _('Freelance')),
        ('it_services', _('IT Services')),
        ('franchise', _('Franchise')),
        ('e_commerce', _('E-Commerce')),
        ('multi_location', _('Multi-location business')),
        ('other', _('Other')),
    ]

    COMPANY_SIZE_CHOICES = [
        ('just_me', _('Just me')),
        ('2_10', _('2–10')),
        ('11_50', _('11–50')),
        ('51_200', _('51–200')),
        ('201_500', _('201–500')),
        ('500_plus', _('500+')),
    ]

    ROLE_CHOICES = [
        ('head_of_seo', _('Head of SEO')),
        ('marketing_manager', _('Marketing Manager')),
        ('owner_vp', _('Owner/VP')),
        ('developer', _('Developer')),
        ('seo_specialist', _('SEO Specialist')),
        ('content_manager', _('Content Manager')),
        ('analyst', _('Analyst')),
        ('other', _('Other')),
    ]

    SEO_SERVICES_CHOICES = [
        ('agency', _("Yes, I run an SEO agency")),
        ('part_of_service', _("Yes, but it's part of a larger service offering")),
        ('own_websites_only', _("No, I'm working on my own websites only")),
    ]

    HELP_AREAS_CHOICES = [
        ('on_page_seo', _('On-page SEO')),
        ('Technology_review', _('Technology Review')),
        ('backlink_analysis', _('Backlink analysis')),
        ('local_seo_gbp', _('Local SEO (Google Business)')),
        ('content_optimisation', _('Content optimisation')),
        ('competitor_tracking', _('Competitor tracking')),
    ]

    INTERESTED_FEATURES_CHOICES = [
        ('full_seo_audit', _('Full SEO audit reports')),
        ('whitelabel_reporting', _('White-label reporting for clients')),
        ('competitor_benchmarking', _('Competitor benchmarking')),
        ('gbp_insights', _('Google Business Profile insights')),
        ('content_recommendations', _('Content recommendations')),
        ('backlink_monitoring', _('Backlink monitoring')),
        ('ai_seo_tasks', _('AI-generated SEO tasks')),
    ]

    email = models.EmailField(_('email address'), unique=True)
    first_name = models.CharField(_('first name'), max_length=150, blank=True)
    last_name = models.CharField(_('last name'), max_length=150, blank=True)
    is_staff = models.BooleanField(
        _('staff status'),
        default=False,
        help_text=_('Designates whether the user can log into this admin site.'),
    )
    is_active = models.BooleanField(
        _('active'),
        default=False,
        help_text=_(
            'Designates whether this user should be treated as active. '
            'Unselect this instead of deleting accounts.'
        ),
    )
    is_verified = models.BooleanField(
        _('verified'),
        default=False,
        help_text=_('Designates whether this user has verified their email address.'),
    )
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)
    stripe_customer_id = models.CharField(max_length=255, blank=True, null=True, db_index=True, help_text=_('Stripe Customer ID'))
    google_id = models.CharField(max_length=255, blank=True, null=True, unique=True, db_index=True, help_text=_('Google User ID'))
    auth_provider = models.CharField(
        _('authentication provider'),
        max_length=50,
        choices=AUTH_PROVIDERS,
        default='email',
        help_text=_('The authentication provider used to register the account.')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # New profile fields
    business_type = models.CharField(
        _("business type"),
        max_length=50,
        choices=BUSINESS_TYPE_CHOICES,
        blank=True, null=True,
        help_text=_("Which of these describes your business best?")
    )
    company_size = models.CharField(
        _("company size"),
        max_length=50,
        choices=COMPANY_SIZE_CHOICES,
        blank=True, null=True,
        help_text=_("What's the size of your company or team?")
    )
    user_role = models.CharField(
        _("user role"),
        max_length=50,
        choices=ROLE_CHOICES,
        blank=True, null=True,
        help_text=_("What's your role?")
    )
    offers_seo_services = models.CharField(
        _("offers seo services"),
        max_length=50,
        choices=SEO_SERVICES_CHOICES,
        blank=True, null=True,
        help_text=_("Do you offer SEO services to clients?")
    )
    help_areas = models.JSONField(
        _("help areas"),
        default=list, 
        blank=True, 
        null=True,
        help_text=_("What areas do you want help with? (Multi-select)")
    )
    interested_features = models.JSONField(
        _("interested features"),
        default=list, 
        blank=True, 
        null=True,
        help_text=_("Which features are you most interested in using? (multi-select)")
    )

    objects = CustomUserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')

    def __str__(self):
        return self.email

    def get_full_name(self):
        """Return the first_name plus the last_name, with a space in between."""
        full_name = f"{self.first_name} {self.last_name}"
        return full_name.strip()

    def get_short_name(self):
        """Return the short name for the user."""
        return self.first_name


class WhiteLabel(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name='whitelabel_settings')
    brand_name = models.CharField(max_length=255)
    logo = models.ImageField(
        upload_to='whitelabel_logos/',
        validators=[validate_logo],
        help_text=_("Upload your logo (PNG format, max 1MB, max dimensions 1000x1000)")
    )
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.email} - White Label Settings"

    class Meta:
        verbose_name = _('White Label Setting')
        verbose_name_plural = _('White Label Settings')

    @property
    def logo_url(self):
        """Get the full URL of the logo"""
        if self.logo:
            return self.logo.url
        return None

class PaymentHistory(models.Model):
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('succeeded', 'Succeeded'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
        ('requires_action', 'Requires Action'),
    ]
    GATEWAY_CHOICES = [
        ('stripe', 'Stripe'),
        ('paypal', 'PayPal'),
        # Add other gateways as needed
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='payment_histories')
    task_id = models.CharField(max_length=255, null=True, blank=True, db_index=True)
    gateway_transaction_id = models.CharField(max_length=255, null=True, blank=True, unique=True, help_text="Transaction/Session ID from the payment gateway")
    gateway_charge_id = models.CharField(max_length=255, blank=True, null=True, help_text="Charge ID from the payment gateway, if applicable")
    gateway_subscription_id = models.CharField(max_length=255, blank=True, null=True, db_index=True, help_text="Subscription ID from the payment gateway, if applicable")
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=10, default='usd')
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending', db_index=True)
    payment_gateway = models.CharField(max_length=50, choices=GATEWAY_CHOICES, default='stripe')
    gateway_specific_details = models.JSONField(default=dict, blank=True, help_text="Raw response or specific details from the gateway")
    error_message = models.TextField(blank=True, null=True)
    paid_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        user_identifier = self.user.email if self.user else (str(self.id) if self.id else 'Unknown Payment')
        return f"Payment {self.id} by {user_identifier} - {self.get_status_display()}"

    class Meta:
        verbose_name = _("Payment History")
        verbose_name_plural = _("Payment Histories")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['created_at']),
        ]

    def get_status_display(self):
        return dict(self.PAYMENT_STATUS_CHOICES).get(self.status, self.status)

    def get_payment_gateway_display(self):
        return dict(self.GATEWAY_CHOICES).get(self.payment_gateway, self.payment_gateway)

class Subscription(models.Model):
    SUBSCRIPTION_STATUS_CHOICES = [
        ('active', 'Active'),
        ('trialing', 'Trialing'),
        ('past_due', 'Past Due'), # Payment failed, dunning
        ('canceled', 'Canceled'),   # Subscription has been definitively canceled
        ('unpaid', 'Unpaid'),       # Similar to past_due, but might be final
        ('incomplete', 'Incomplete'), # Initial payment failed or requires action
        ('incomplete_expired', 'Incomplete Expired'), # Incomplete and time limit passed
        ('paused', 'Paused'), # Stripe Subscription pause feature
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='subscriptions')
    # plan = models.ForeignKey(Pricing, on_delete=models.SET_NULL, null=True, blank=True, related_name='subscriptions') # REMOVED
    stripe_subscription_id = models.CharField(max_length=255, unique=True, db_index=True, help_text="Stripe Subscription ID")
    status = models.CharField(max_length=30, choices=SUBSCRIPTION_STATUS_CHOICES, db_index=True)
    
    # current_period_start = models.DateTimeField(null=True, blank=True, help_text="Start of the current billing period")
    # current_period_end = models.DateTimeField(null=True, blank=True, help_text="End of the current billing period (expiration date)")
    # cancel_at_period_end = models.BooleanField(default=False, help_text="True if the subscription is set to cancel at the end of the current period")
    
    # To store Stripe specific trial start and end dates if applicable, separate from period dates
    # trial_start = models.DateTimeField(null=True, blank=True)
    # trial_end = models.DateTimeField(null=True, blank=True)

    # When the subscription was actually cancelled on Stripe's end (if applicable)
    # ended_at = models.DateTimeField(null=True, blank=True, help_text="Timestamp when the subscription truly ended (e.g., after cancellation or non-payment)")
    
    # When the subscription was cancelled by the user or system, might be before ended_at if cancel_at_period_end is true
    # canceled_at = models.DateTimeField(null=True, blank=True, help_text="Timestamp when a cancellation was requested")

    metadata = models.JSONField(default=dict, blank=True, help_text="Additional metadata from Stripe or internal notes")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        # Since plan is removed, we might not have a local plan name easily.
        # Consider fetching from Stripe here if needed for admin __str__, or use a placeholder.
        return f"{self.user.email} - Subscription {self.stripe_subscription_id} - {self.get_status_display()}"

    class Meta:
        verbose_name = _("Subscription")
        verbose_name_plural = _("Subscriptions")
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            # models.Index(fields=['current_period_end']), # Removed index for current_period_end
        ]

    def is_active(self):
        return self.status in ['active', 'trialing']

    def needs_payment(self):
        return self.status in ['past_due', 'unpaid', 'incomplete']
