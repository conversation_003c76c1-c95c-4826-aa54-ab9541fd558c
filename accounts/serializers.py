import stripe
import logging
import datetime
import os
from decimal import Decimal
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from rest_framework import serializers
from rest_framework.validators import UniqueValidator
from .models import WhiteLabel, PaymentHistory, Subscription
from .gateways.stripe.service import ACTIVE_SUBSCRIPTION_STATUSES
from .validators import validate_logo_data, validate_logo

# Added for Google OAuth
import requests
from google.oauth2 import id_token as google_id_token
from google.auth.transport import requests as google_auth_requests

from .models import WhiteLabel, CustomUser, PaymentHistory, Subscription
from .gateways.stripe.service import StripeService
from tools.models import AnalysisRecord # Removed Pricing import, kept AnalysisRecord
from django.db import transaction # Added for atomic transactions
from urllib.parse import urlparse, urlunparse, urlencode, parse_qs # Added for URL manipulation
from django.db import models # Added for models
from django.views.generic import TemplateView # Added for serving test HTML page

User = get_user_model()

# --- Admin Serializers ---
from rest_framework_simplejwt.token_blacklist.models import OutstandingToken

class AdminUserListSerializer(serializers.ModelSerializer):
    class Meta:
        model = User # User = get_user_model()
        fields = ('id', 'email', 'first_name', 'last_name', 'is_active', 'is_staff', 'date_joined', 'last_login')
        read_only_fields = fields

class OutstandingTokenAdminSerializer(serializers.ModelSerializer):
    user_email = serializers.ReadOnlyField(source='user.email')
    is_blacklisted = serializers.SerializerMethodField()

    class Meta:
        model = OutstandingToken
        fields = ['id', 'user_email', 'jti', 'created_at', 'expires_at', 'is_blacklisted']
        read_only_fields = fields

    def get_is_blacklisted(self, obj):
        # Check if a BlacklistedToken entry exists for this OutstandingToken
        # This works because BlacklistedToken has a OneToOneField to OutstandingToken
        return hasattr(obj, 'blacklistedtoken')

# --- End Admin Serializers ---

logger = logging.getLogger(__name__)

# --- Dedicated logger for Stripe subscription processing in UserSerializer ---
stripe_processing_logger = logging.getLogger('user_serializer_stripe_processing')
stripe_processing_logger.setLevel(logging.INFO)
stripe_processing_logger.propagate = False # Important to avoid duplicate logs

# Ensure logs directory exists and set up FileHandler
# This assumes your project has a 'logs' directory at settings.BASE_DIR
# and that settings.BASE_DIR is correctly defined.
if not stripe_processing_logger.handlers: # Add handler only if not already added
    try:
        log_file_path_other = os.path.join(settings.BASE_DIR, 'logs', 'other.log')
        os.makedirs(os.path.dirname(log_file_path_other), exist_ok=True)

        other_log_file_handler = logging.FileHandler(log_file_path_other)
        other_log_file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        other_log_file_handler.setFormatter(formatter)
        stripe_processing_logger.addHandler(other_log_file_handler)
    except Exception as e:
        print(f"Error setting up stripe_processing_logger for other.log: {e}. Logs will use default config.")
        # As a fallback, if file handler setup fails, it might log to console via root logger if not handled.
        # Or, reassign to the module's default logger if strict separation isn't critical on failure:
        # stripe_processing_logger = logger
# --- End dedicated logger setup ---


class RegisterSerializer(serializers.ModelSerializer):
    """Serializer for user registration"""
    email = serializers.EmailField(
        required=True,
        validators=[UniqueValidator(queryset=User.objects.all())]
    )
    password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'},
        validators=[validate_password]
    )
    confirm_password = serializers.CharField(
        write_only=True,
        required=True,
        style={'input_type': 'password'}
    )

    class Meta:
        model = User
        fields = ('email', 'password', 'confirm_password', 'first_name', 'last_name')
        extra_kwargs = {
            'first_name': {'required': False},
            'last_name': {'required': False},
        }

    def validate(self, attrs):
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError({
                "password": _("Password fields didn't match.")
            })
        return attrs

    def create(self, validated_data):
        validated_data.pop('confirm_password')
        user = User.objects.create_user(**validated_data)
        return user


class LoginSerializer(serializers.Serializer):
    """Serializer for user login"""
    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True, write_only=True)
    remember_me = serializers.BooleanField(required=False, default=False)
    recaptcha_token = serializers.CharField(required=False)


class ForgotPasswordSerializer(serializers.Serializer):
    """Serializer for forgot password request"""
    email = serializers.EmailField(required=True)


class ResetPasswordSerializer(serializers.Serializer):
    """Serializer for password reset"""
    new_password = serializers.CharField(
        write_only=True,
        required=True,
        validators=[validate_password]
    )
    confirm_password = serializers.CharField(write_only=True, required=True)

    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError(
                {"password": _("Password fields didn't match.")}
            )
        return attrs


class UserSerializer(serializers.ModelSerializer):
    """Serializer for user profile"""
    subscriptions = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('email', 'first_name', 'last_name', 'is_verified', 'subscriptions')
        read_only_fields = ('email', 'is_verified')

    def get_subscriptions(self, obj):
        user_local_subscriptions = Subscription.objects.filter(user=obj)
        active_subscriptions_data = []

        for local_sub in user_local_subscriptions:
            if not local_sub.stripe_subscription_id:
                continue

            try:
                stripe_sub = stripe.Subscription.retrieve(
                    local_sub.stripe_subscription_id,
                    expand=['items.data.price.product', 'default_payment_method']
                )
                stripe_processing_logger.info(f"User {obj.id} Sub Processing: Stripe Sub ID: {stripe_sub.id}")
                stripe_processing_logger.info(f"  Raw current_period_start from Stripe: {stripe_sub.get('current_period_start')}")
                stripe_processing_logger.info(f"  Raw current_period_end from Stripe: {stripe_sub.get('current_period_end')}")
                stripe_processing_logger.info(f"  Raw status from Stripe: {stripe_sub.get('status')}")
                stripe_processing_logger.info(f"  Raw items from Stripe: {stripe_sub.get('items')}")

                if stripe_sub.status in ACTIVE_SUBSCRIPTION_STATUSES:
                    plan_name = "Unknown Plan"
                    price_amount = None
                    price_currency = None
                    price_interval = None
                    price_interval_count = None

                    stripe_processing_logger.info(f"  Subscription {stripe_sub.id} is considered active.")

                    items_obj = stripe_sub.get('items')
                    items_list = None
                    if items_obj and hasattr(items_obj, 'data') and isinstance(items_obj.data, list) and items_obj.data:
                        items_list = items_obj.data
                        stripe_processing_logger.info(f"  Subscription {stripe_sub.id} has items_list with {len(items_list)} item(s) via items_obj.data.")
                    elif items_obj and isinstance(items_obj.get('data'), list) and items_obj.get('data'):
                        items_list = items_obj.get('data')
                        stripe_processing_logger.info(f"  Subscription {stripe_sub.id} has items_list with {len(items_list)} item(s) via items_obj.get('data').")

                    sub_current_period_start_ts = stripe_sub.get('current_period_start')
                    sub_current_period_end_ts = stripe_sub.get('current_period_end')
                    stripe_processing_logger.info(f"  Top-level current_period_start_ts: {sub_current_period_start_ts}, current_period_end_ts: {sub_current_period_end_ts}")

                    if items_list:
                        item = items_list[0]
                        item_id_for_log = item.id if hasattr(item, 'id') else 'Item has no ID attribute'
                        stripe_processing_logger.info(f"  Processing first item ID: {item_id_for_log}")

                        item_period_start_ts = item.get('current_period_start')
                        item_period_end_ts = item.get('current_period_end')

                        if sub_current_period_start_ts is None and item_period_start_ts is not None:
                            sub_current_period_start_ts = item_period_start_ts
                            stripe_processing_logger.info(f"    Used item's current_period_start: {sub_current_period_start_ts}")
                        if sub_current_period_end_ts is None and item_period_end_ts is not None:
                            sub_current_period_end_ts = item_period_end_ts
                            stripe_processing_logger.info(f"    Used item's current_period_end: {sub_current_period_end_ts}")
                        
                        price_data_on_item = item.get('price')
                        if price_data_on_item:
                            stripe_processing_logger.info(f"  Item {item_id_for_log} has a price object.")
                            price_info = price_data_on_item
                            price_id_for_log = price_info.id if hasattr(price_info, 'id') else 'Price object has no ID attribute'
                            stripe_processing_logger.info(f"  Price object ID: {price_id_for_log}")
                            stripe_processing_logger.info(f"    Price unit_amount: {price_info.get('unit_amount')}")
                            stripe_processing_logger.info(f"    Price currency: {price_info.get('currency')}")
                            raw_price_amount = price_info.get('unit_amount')
                            if raw_price_amount is not None:
                                price_amount = Decimal(raw_price_amount) / Decimal(100)
                            else:
                                price_amount = None
                            price_currency = price_info.get('currency')

                            recurring_info = price_info.get('recurring')
                            if recurring_info:
                                stripe_processing_logger.info(f"    Price has recurring attribute: interval={recurring_info.get('interval')}, count={recurring_info.get('interval_count')}")
                                price_interval = recurring_info.get('interval')
                                price_interval_count = recurring_info.get('interval_count')
                            else:
                                stripe_processing_logger.info(f"    Price has NO recurring attribute.")
                            
                            product_data_on_price = price_info.get('product')
                            if product_data_on_price:
                                stripe_processing_logger.info(f"    Price object has a product attribute.")
                                product_data = product_data_on_price 
                                stripe_processing_logger.info(f"    Product data type: {type(product_data)}")
                                if hasattr(product_data, 'name') and product_data.name:
                                    plan_name = product_data.name
                                    stripe_processing_logger.info(f"    Product name from expanded object: {plan_name}")
                                elif isinstance(product_data, str): 
                                    stripe_processing_logger.info(f"    Product data is a string ID: {product_data}. Attempting to retrieve product...")
                                    try:
                                        product_obj_retrieved = stripe.Product.retrieve(product_data)
                                        plan_name = product_obj_retrieved.name
                                        stripe_processing_logger.info(f"      Successfully retrieved product name: {plan_name}")
                                    except stripe.error.StripeError as e_prod:
                                        stripe_processing_logger.warning(f"User {obj.id}: Could not retrieve product {product_data} for sub {stripe_sub.id}: {e_prod}")
                                else:
                                    stripe_processing_logger.info(f"    Product data is neither an expanded object with 'name' nor a string ID. Product data: {str(product_data)[:200]}")
                            else:
                                stripe_processing_logger.info(f"    Price object has NO product attribute (product_data_on_price is None).")
                        else:
                            stripe_processing_logger.info(f"  Item {item_id_for_log} has NO price object (price_data_on_item is None).")
                    else:
                        stripe_processing_logger.info(f"  Subscription {stripe_sub.id} does NOT have a valid items_list or it is empty.")
                        
                    sub_data = {
                        'plan_name': plan_name,
                        'current_period_start': datetime.datetime.fromtimestamp(sub_current_period_start_ts, tz=datetime.timezone.utc).isoformat() if sub_current_period_start_ts else None,
                        'current_period_end': datetime.datetime.fromtimestamp(sub_current_period_end_ts, tz=datetime.timezone.utc).isoformat() if sub_current_period_end_ts else None,
                        'trial_start': datetime.datetime.fromtimestamp(stripe_sub.get('trial_start'), tz=datetime.timezone.utc).isoformat() if stripe_sub.get('trial_start') else None,
                        'trial_end': datetime.datetime.fromtimestamp(stripe_sub.get('trial_end'), tz=datetime.timezone.utc).isoformat() if stripe_sub.get('trial_end') else None,
                        'price_amount': price_amount,
                        'price_currency': price_currency,
                        'price_interval': price_interval,
                        'price_interval_count': price_interval_count,
                    }
                    active_subscriptions_data.append(sub_data)

            except stripe.error.StripeError as e:
                stripe_processing_logger.error(f"User {obj.id}: Stripe error retrieving subscription {local_sub.stripe_subscription_id}: {str(e)}")
            except Exception as e:
                stripe_processing_logger.error(f"User {obj.id}: Unexpected error processing subscription {local_sub.stripe_subscription_id}: {str(e)}", exc_info=True)
        
        return active_subscriptions_data


class VerifyOTPSerializer(serializers.Serializer):
    """Serializer for OTP verification"""
    email = serializers.EmailField(required=True)
    otp = serializers.CharField(required=True, min_length=6, max_length=6)


class GoogleLoginSerializer(serializers.Serializer):
    """Serializer for Google login"""
    id_token = serializers.CharField(required=True)


class ResendOTPSerializer(serializers.Serializer):
    """Serializer for requesting OTP resend"""
    email = serializers.EmailField(required=True)


class WhiteLabelSerializer(serializers.ModelSerializer):
    """Serializer for white label settings"""
    logo = serializers.ImageField(validators=[validate_logo], required=False)

    class Meta:
        model = WhiteLabel
        fields = ('brand_name', 'logo', 'phone_number', 'website')
        extra_kwargs = {
            'brand_name': {'required': True},
            'phone_number': {'required': False},
            'website': {'required': False},
        }

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        request = self.context.get('request')
        if request and instance.logo and hasattr(instance.logo, 'url'):
            representation['logo'] = request.build_absolute_uri(instance.logo.url)
        elif instance.logo:
            representation['logo'] = instance.logo.url
        else:
            representation['logo'] = None
        return representation

    def create(self, validated_data):
        user = self.context['request'].user
        if WhiteLabel.objects.filter(user=user).exists():
            raise serializers.ValidationError(_("White label settings already exist for this user."))
        whitelabel = WhiteLabel.objects.create(user=user, **validated_data)
        return whitelabel

    def update(self, instance, validated_data):
        if instance.user != self.context['request'].user:
            raise serializers.ValidationError(_("You do not have permission to edit these settings."))
        return super().update(instance, validated_data)


class PaymentHistorySerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_gateway_display = serializers.CharField(source='get_payment_gateway_display', read_only=True)

    class Meta:
        model = PaymentHistory
        fields = (
            'id',
            'gateway_transaction_id',
            'amount',
            'currency',
            'status',
            'status_display',
            'payment_gateway',
            'payment_gateway_display',
            'paid_at',
            'created_at',
            'error_message'
        )


class CheckPaymentStatusSerializer(serializers.Serializer):
    transaction_id = serializers.CharField(read_only=True)
    status = serializers.CharField(read_only=True)
    status_display = serializers.CharField(read_only=True)
    task_id = serializers.CharField(read_only=True, required=False, allow_null=True)


class CreatePaymentSerializer(serializers.Serializer):
    plan_id = serializers.CharField(required=True, max_length=255, help_text="Stripe Product ID")
    email = serializers.EmailField(required=False)
    task_id = serializers.CharField(required=False, allow_null=True, max_length=255)
    billing_cycle = serializers.ChoiceField(choices=['monthly', 'yearly'], required=False, help_text="Required if the product has multiple prices (e.g., monthly and yearly).")
    success_url = serializers.URLField(required=False, allow_null=True)
    cancel_url = serializers.URLField(required=False, allow_null=True)
    client_reference_id = serializers.CharField(required=False, allow_null=True, max_length=255)
    existing_customer_id = serializers.CharField(required=False, allow_null=True, max_length=255)
    coupon_id = serializers.CharField(required=False, allow_null=True, max_length=255)
    allow_promotion_codes = serializers.BooleanField(required=False, default=True)


class SubscriptionSerializer(serializers.ModelSerializer):
    """Serializer for user subscriptions."""
    plan_name = serializers.SerializerMethodField()

    class Meta:
        model = Subscription
        fields = (
            'id',
            'stripe_subscription_id',
            'plan_name',
            'status',
        )
        read_only_fields = fields

    def get_plan_name(self, obj):
        if not obj.stripe_subscription_id:
            return "Unknown Plan (No Stripe ID)"
        try:
            stripe_sub = stripe.Subscription.retrieve(
                obj.stripe_subscription_id, 
                expand=['items.data.price.product']
            )
            if stripe_sub.items and stripe_sub.items.data:
                first_item = stripe_sub.items.data[0]
                if first_item.price and first_item.price.product and hasattr(first_item.price.product, 'name'):
                    # Product is an expanded object
                    return first_item.price.product.name
                elif first_item.price and first_item.price.product: 
                    # Product is an ID, retrieve it (less efficient, try to expand above)
                    try:
                        product = stripe.Product.retrieve(first_item.price.product)
                        return product.name
                    except stripe.error.StripeError as e_prod:
                        logger.error(f"SubscriptionSerializer: Stripe error retrieving product {first_item.price.product} for sub {obj.stripe_subscription_id}: {e_prod}")
                        return "Unknown Plan (Product API Error)"
            return "Unknown Plan (No Product Info in Sub Items)"
        except stripe.error.StripeError as e:
            logger.error(f"SubscriptionSerializer: Stripe error retrieving subscription {obj.stripe_subscription_id}: {e}")
            # Fallback to local plan name if Stripe API fails or plan is not linked yet. # This fallback is no longer possible
            # if obj.plan and obj.plan.name:  # obj.plan no longer exists
            #     return f"{obj.plan.name} (Stripe API Error)"
            return "Unknown Plan (Subscription API Error)"
        except Exception as e:
            logger.error(f"SubscriptionSerializer: Unexpected error for sub {obj.stripe_subscription_id}: {e}")
            return "Unknown Plan (Error)"


class UserProfileSettingsSerializer(serializers.ModelSerializer):
    """Serializer for additional user profile settings."""
    # Make choice fields writable by ensuring they are not read_only
    # and by providing the choices directly if not inferred from model
    business_type = serializers.ChoiceField(choices=User.BUSINESS_TYPE_CHOICES, required=False, allow_null=True)
    company_size = serializers.ChoiceField(choices=User.COMPANY_SIZE_CHOICES, required=False, allow_null=True)
    user_role = serializers.ChoiceField(choices=User.ROLE_CHOICES, required=False, allow_null=True) 
    offers_seo_services = serializers.ChoiceField(choices=User.SEO_SERVICES_CHOICES, required=False, allow_null=True)
    
    # For JSONFields storing lists of choices, ensure they can accept lists
    help_areas = serializers.MultipleChoiceField(choices=User.HELP_AREAS_CHOICES, required=False, allow_null=True, allow_empty=True)
    interested_features = serializers.MultipleChoiceField(choices=User.INTERESTED_FEATURES_CHOICES, required=False, allow_null=True, allow_empty=True)

    class Meta:
        model = User
        fields = (
            'business_type',
            'company_size',
            'user_role',
            'offers_seo_services',
            'help_areas',
            'interested_features',
        )

    def validate_help_areas(self, value):
        """Ensure help_areas contains valid choices."""
        if value is None: return None # Allow null
        valid_choices = {choice[0] for choice in User.HELP_AREAS_CHOICES}
        for item in value:
            if item not in valid_choices:
                raise serializers.ValidationError(f"'{item}' is not a valid choice for help areas.")
        return list(value) # Explicitly convert to list

    def validate_interested_features(self, value):
        """Ensure interested_features contains valid choices."""
        if value is None: return None # Allow null
        valid_choices = {choice[0] for choice in User.INTERESTED_FEATURES_CHOICES}
        for item in value:
            if item not in valid_choices:
                raise serializers.ValidationError(f"'{item}' is not a valid choice for interested features.")
        return list(value) # Explicitly convert to list
