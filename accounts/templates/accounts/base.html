<!DOCTYPE html>
<html>
<head>
    <!-- ... other head elements ... -->
    <script src="https://www.google.com/recaptcha/api.js?render={{ recaptcha_site_key }}"></script>
</head>
<body>
    {% block content %}{% endblock %}
    
    <script>
        function executeRecaptcha(action) {
            return new Promise((resolve, reject) => {
                grecaptcha.ready(function() {
                    grecaptcha.execute('{{ recaptcha_site_key }}', {action: action})
                        .then(function(token) {
                            resolve(token);
                        })
                        .catch(function(error) {
                            reject(error);
                        });
                });
            });
        }
        
        // Add this to your form submission handling
        async function handleFormSubmit(event, action) {
            event.preventDefault();
            try {
                const token = await executeRecaptcha(action);
                // Add token to your form data before submission
                const formData = new FormData(event.target);
                formData.append('recaptcha_token', token);
                // Continue with your form submission logic
            } catch (error) {
                console.error('reCAPTC<PERSON> error:', error);
                alert('Error verifying reCAPTCHA. Please try again.');
            }
        }
    </script>
</body>
</html>