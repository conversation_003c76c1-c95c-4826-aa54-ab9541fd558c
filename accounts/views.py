import logging
import stripe # Added stripe
from decimal import Decimal # Added Decimal
import datetime as dt_module # MODIFIED
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils.encoding import force_str
from django.utils.http import urlsafe_base64_decode
from django.utils.translation import gettext_lazy as _
from django.shortcuts import render, redirect, get_object_or_404
from django.template.loader import render_to_string
from django.core.cache import cache
from django.utils import timezone
from django.core.mail import EmailMessage, send_mail # Added send_mail
from django.http import HttpResponse, JsonResponse # Added JsonResponse

from rest_framework import status, generics, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny, IsAdminUser # Added IsAdminUser
from rest_framework_simplejwt.tokens import RefreshToken, TokenError
from rest_framework.throttling import AnonRateThrottle, UserRateThrottle
from rest_framework.parsers import MultiPartParser, FormParser

from .serializers import (
    RegisterSerializer, LoginSerializer, ForgotPasswordSerializer,
    ResetPasswordSerializer, UserSerializer,
    VerifyOTPSerializer, ResendOTPSerializer, WhiteLabelSerializer,
    PaymentHistorySerializer, CheckPaymentStatusSerializer, CreatePaymentSerializer,
    GoogleLoginSerializer, # Added GoogleLoginSerializer
    SubscriptionSerializer, UserProfileSettingsSerializer, # Added UserProfileSettingsSerializer
    AdminUserListSerializer, OutstandingTokenAdminSerializer # Added Admin serializers
)
from .tokens import email_verification_token, password_reset_token
from .utils import (
    send_verification_email, send_password_reset_email, verify_recaptcha, verify_otp,
    generate_otp, store_otp
)
from .exceptions import EmailVerificationError, RecaptchaVerificationError, UserAccountError

# Added for Google OAuth
import requests
from google.oauth2 import id_token as google_id_token
from google.auth.transport import requests as google_auth_requests

from .models import WhiteLabel, CustomUser, PaymentHistory, Subscription
from .gateways.stripe.service import StripeService
from tools.models import AnalysisRecord # Removed Pricing import, kept AnalysisRecord
from django.db import transaction # Added for atomic transactions
from urllib.parse import urlparse, urlunparse, urlencode, parse_qs # Added for URL manipulation
from django.db import models # Added for models
from django.views.generic import TemplateView # Added for serving test HTML page

# --- Admin Views for Token Management ---
from rest_framework_simplejwt.token_blacklist.models import OutstandingToken, BlacklistedToken
from rest_framework_simplejwt.utils import aware_utcnow

User = get_user_model()
logger = logging.getLogger(__name__)

# Initialize Stripe API Key
stripe.api_key = settings.STRIPE_SECRET_KEY

# Define statuses that indicate an active or recently successful subscription
ACTIVE_SUB_STATUSES = {'succeeded', 'pending'} # Include pending to prevent double-clicks

# --- Payment Views ---

def send_payment_success_email(payment_history):
    user = payment_history.user
    if not user or not user.email:
        logger.warning(f"Payment success email not sent: User or user email missing for PaymentHistory ID {payment_history.id}")
        return

    subject = f"Payment Successful: Invoice"
    context = {
        'payment_history': payment_history,
        'user_full_name': user.get_full_name() or user.email,
        'site_name': 'Your Site Name' # Replace with actual site name or load from settings
    }
    # HTML content
    html_message = render_to_string('account/email/payment_invoice.html', context)
    # Plain text content (optional, but recommended)
    # plain_message = render_to_string('account/email/payment_invoice.txt', context)

    try:
        send_mail(
            subject,
            '', # Plain text message (can be empty if HTML is primary)
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
            html_message=html_message,
            fail_silently=False,
        )
        logger.info(f"Payment success email sent to {user.email} for PaymentHistory ID {payment_history.id}")
    except Exception as e:
        logger.error(f"Failed to send payment success email to {user.email}: {str(e)}")


class StripeWebhookView(APIView):
    permission_classes = [AllowAny]
    stripe_service = StripeService()

    def post(self, request, *args, **kwargs):
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    
        try:
            processed_successfully = self.stripe_service.handle_webhook_event(payload, sig_header)
            if processed_successfully:
                return HttpResponse(status=200)
            else:
                return HttpResponse(status=400)
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Stripe Webhook SignatureVerificationError: {str(e)}")
            return HttpResponse(status=400)
        except Exception as e:
            logger.error(f"Stripe Webhook - Unhandled exception in POST: {str(e)}", exc_info=True)
            return HttpResponse(status=500)


class StripePaymentCallbackView(APIView):
    permission_classes = [AllowAny]

    @transaction.atomic
    def get(self, request, *args, **kwargs):
        query_params = request.GET
        payment_uuid = query_params.get('uid')
        # status_from_checkout_url = query_params.get('status') # REMOVED: This parameter is no longer sent

        frontend_base_url = getattr(settings, 'FRONTEND_DOMAIN_BASE', 'https://seoanalyser.com.au').rstrip('/')
        default_redirect_path = '/checkout' 

        if not payment_uuid:
            logger.error(f"StripePaymentCallbackView (GET): Missing uid. Query: {request.GET.urlencode()}")
            error_redirect_url = f"{frontend_base_url}/{default_redirect_path}?error=invalid_callback_params&reason=missing_uid"
            return redirect(error_redirect_url)

        try:
            payment_history = PaymentHistory.objects.select_for_update().get(id=payment_uuid)
            stripe_session_id = payment_history.gateway_transaction_id
            task_id = payment_history.task_id # Retain task_id for redirect logic

            if not stripe_session_id:
                logger.error(f"StripePaymentCallbackView (GET): No gateway_transaction_id in PH {payment_uuid}")
                error_params = urlencode({'error': 'payment_record_incomplete', 'uid': payment_uuid})
                return redirect(f"{frontend_base_url}/{default_redirect_path}?{error_params}")

            try:
                checkout_session = stripe.checkout.Session.retrieve(stripe_session_id)
            except stripe.error.StripeError as e:
                logger.error(f"StripePaymentCallbackView (GET): Stripe API error for session {stripe_session_id}, PH {payment_uuid}: {e}")
                error_params = urlencode({'error': 'stripe_api_error', 'uid': payment_uuid})
                return redirect(f"{frontend_base_url}/{default_redirect_path}?{error_params}")

            initial_ph_status = payment_history.status
            # Determine status based on checkout_session fields
            if checkout_session.payment_status == 'paid':
                payment_history.status = 'succeeded'
                payment_history.paid_at = timezone.now()
                payment_history.error_message = None
            elif checkout_session.status == 'open': # User might have cancelled or abandoned before payment
                payment_history.status = 'cancelled'
                payment_history.error_message = 'User cancelled or abandoned Stripe session (callback).'
            elif checkout_session.status == 'expired':
                payment_history.status = 'failed'
                payment_history.error_message = 'Stripe checkout session expired (callback).'
            # If payment_status is 'unpaid' or 'no_payment_required' and session status is 'complete', 
            # it might be a free trial or similar that succeeded without immediate payment.
            # Webhooks are the ultimate source of truth for such cases.
            # For the immediate callback, we focus on paid/open/expired session states.
            
            payment_history.gateway_specific_details = payment_history.gateway_specific_details or {}
            payment_history.gateway_specific_details.update({
                'callback_payment_status': checkout_session.payment_status,
                'callback_session_status': checkout_session.status,
                'stripe_customer_id': checkout_session.customer,      # Might be None
                'stripe_subscription_id': checkout_session.subscription # Might be None
            })
            if payment_history.status != initial_ph_status:
                 payment_history.save() 
            else:
                 payment_history.save(update_fields=['gateway_specific_details','updated_at'])

            # --- Construct the final redirect URL --- 
            final_redirect_params = {} # Initialize as empty
            final_redirect_target_path = default_redirect_path # Default to 'frontend/checkout'

            if task_id:
                # A task_id is present, attempt to redirect to task-specific URL structure
                # Order: share, uid, status, then other optional params
                final_redirect_params['share'] = task_id
                final_redirect_params['uid'] = str(payment_history.id)
                final_redirect_params['status'] = payment_history.status

                try:
                    analysis_record = AnalysisRecord.objects.select_related('analyzed_url').get(celery_task_id=task_id)
                    analyzed_full_url = analysis_record.analyzed_url.url
                    parsed_url = urlparse(analyzed_full_url)
                    
                    # Construct path like "www.example.com/path/to/page"
                    path_from_analyzed_url = parsed_url.netloc + parsed_url.path.rstrip('/')
                    # Corrected: final_redirect_target_path is just the path component derived from analyzed_url
                    final_redirect_target_path = path_from_analyzed_url
                    
                    if payment_history.status != 'succeeded' and payment_history.error_message:
                        final_redirect_params['error_msg'] = payment_history.error_message[:100]

                except AnalysisRecord.DoesNotExist:
                    logger.warning(f"StripePaymentCallbackView (GET): AnalysisRecord {task_id} not found for PH {payment_history.id}. Falling back to default redirect.")
                    final_redirect_target_path = default_redirect_path # Fallback path
                    # 'share', 'uid', 'status' are already set. Add 'warn'.
                    final_redirect_params['warn'] = 'task_not_found'
                    # Ensure error_msg is in params for this fallback case as well
                    if payment_history.status != 'succeeded' and payment_history.error_message and 'error_msg' not in final_redirect_params:
                        final_redirect_params['error_msg'] = payment_history.error_message[:100]
            else:
                # No task_id, always use the default redirect path
                # Order: uid, status, then other optional params
                final_redirect_params['uid'] = str(payment_history.id)
                # final_redirect_target_path is already default_redirect_path
                if payment_history.status != 'succeeded' and payment_history.error_message:
                     final_redirect_params['error_msg'] = payment_history.error_message[:100]
            
            # frontend_base_url has been rstrip('/'), so final_redirect_target_path should be lstrip('/') for clean join
            full_redirect_url = f"{frontend_base_url}/{final_redirect_target_path.lstrip('/')}?{urlencode(final_redirect_params)}"
            return redirect(full_redirect_url)

        except PaymentHistory.DoesNotExist:
            logger.error(f"StripePaymentCallbackView (GET): PaymentHistory not found for UUID: {payment_uuid}")
            error_params = urlencode({'error': 'payment_not_found', 'uid': payment_uuid})
            return redirect(f"{frontend_base_url}/{default_redirect_path}?{error_params}")
        except Exception as e:
            logger.error(f"StripePaymentCallbackView (GET): Unexpected error for PH {payment_uuid}: {str(e)}", exc_info=True)
            error_params = urlencode({'error': 'unexpected_callback_error', 'uid': payment_uuid})
            return redirect(f"{frontend_base_url}/{default_redirect_path}?{error_params}")


class CheckPaymentStatusView(APIView):
    permission_classes = [IsAuthenticated] # Changed from AllowAny to IsAuthenticated

    def get(self, request, uid, *args, **kwargs):
        try:
            payment_history = PaymentHistory.objects.get(id=uid)

            # Check if the requesting user is the owner of the payment
            if payment_history.user != request.user:
                logger.warning(f"CheckPaymentStatusView: User {request.user.id} attempted to access payment {uid} owned by user {payment_history.user.id if payment_history.user else 'None'}.")
                return Response({"error": "Payment record not found."}, status=status.HTTP_404_NOT_FOUND)

            user = payment_history.user # This is now guaranteed to be the request.user or None if PH.user is None (though IsAuthenticated should prevent anonymous request.user)
            response_data = {}

            plan_name = 'N/A'
            plan_period_display = 'N/A' # This might be hard to derive without Stripe call

            if payment_history.gateway_specific_details:
                # Try to get plan name from gateway_specific_details (Stripe Product name)
                plan_name = payment_history.gateway_specific_details.get('stripe_product_name', 'N/A')
                # plan_id_from_gateway = payment_history.gateway_specific_details.get('stripe_price_id') # This is price_id
                # We don't have a direct local Pricing object to get period display from anymore.
                # If period is needed here, it would require a Stripe API call or storing it in gateway_specific_details.

            formatted_amount = f"{payment_history.amount:.2f}"
            if payment_history.currency and payment_history.currency.lower() == 'usd':
                formatted_amount += "$"
            elif payment_history.currency:
                formatted_amount += payment_history.currency.upper()
            else:
                formatted_amount += " (currency not specified)"

            response_data['payment_status'] = payment_history.status
            response_data['amount'] = formatted_amount
            response_data['plan'] = plan_name

            if user:
                response_data['profile'] = UserSerializer(user).data
                try:
                    whitelabel_setting = WhiteLabel.objects.get(user=user)
                    # Pass context with request to the serializer
                    serializer_context = {'request': request}
                    response_data['whitelabel_setting'] = WhiteLabelSerializer(whitelabel_setting, context=serializer_context).data
                except WhiteLabel.DoesNotExist:
                    response_data['whitelabel_setting'] = None
            else:
                response_data['profile'] = None
                response_data['whitelabel_setting'] = None

            return Response(response_data, status=status.HTTP_200_OK)

        except PaymentHistory.DoesNotExist:
            logger.warning(f"CheckPaymentStatusView: PaymentHistory not found for uid {uid}")
            return Response({"error": "Payment record not found."}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"Error checking payment status for uid {uid}: {str(e)}", exc_info=True)
            return Response({"error": "An unexpected error occurred."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserPaymentHistoryView(generics.ListAPIView):
    serializer_class = PaymentHistorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return PaymentHistory.objects.filter(user=self.request.user).order_by('-created_at')

# --- Existing Views ---

class RegisterView(APIView):
    permission_classes = [AllowAny]
    throttle_classes = [AnonRateThrottle]

    def get_client_ip(self, request):
        cf_connecting_ip = request.headers.get('CF-Connecting-IP')
        if cf_connecting_ip:
            return cf_connecting_ip
        return request.META.get('REMOTE_ADDR')

    def post(self, request):
        try:
            client_ip = self.get_client_ip(request)
            # Use client_ip for rate limiting or logging
            
            email = request.data.get('email')
            if email:
                try:
                    user = User.objects.get(email__iexact=email) # Case-insensitive check
                    if not user.is_verified:
                        # Resend verification email for existing unverified user
                        if not send_verification_email(request, user):
                            raise EmailVerificationError(_("Failed to resend verification email."))
                        return Response(
                            {"message": _("We've resent the verification OTP.")},
                            status=status.HTTP_200_OK
                        )
                    else:
                        # Email exists and is verified
                        return Response(
                            {"email": [_("This email address is already registered and verified.")]},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                except User.DoesNotExist:
                    # Email does not exist, proceed with registration
                    pass

            # Proceed with new user registration
            serializer = RegisterSerializer(data=request.data)
            if not serializer.is_valid():
                raise UserAccountError(detail=serializer.errors)

            # Verify reCAPTCHA (currently commented out)
            # recaptcha_token = serializer.validated_data.pop('recaptcha_token')
            # if not verify_recaptcha(recaptcha_token):
            #     raise RecaptchaVerificationError()
            
            user = serializer.save()
            
            if not send_verification_email(request, user):
                user.delete() 
                raise EmailVerificationError()

            return Response(
                {
                    "message": _("User registered successfully. Please check your email to verify your account."),
                    "user": UserSerializer(user).data
                },
                status=status.HTTP_201_CREATED
            )
            
        except (EmailVerificationError, RecaptchaVerificationError, UserAccountError) as e:
            logger.error(f"Registration error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during registration: {str(e)}", exc_info=True)
            raise UserAccountError(detail=_("An unexpected error occurred. Please try again later."))


class VerifyEmailView(APIView):
    permission_classes = [AllowAny]
    throttle_classes = [AnonRateThrottle] # Added throttle class for consistency

    def get_tokens_for_user(self, user):
        """Generate tokens for user"""
        refresh = RefreshToken.for_user(user)
        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }

    def post(self, request):
        serializer = VerifyOTPSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.validated_data['email']
        otp = serializer.validated_data['otp']

        try:
            user = User.objects.get(email__iexact=email) # Use iexact for case-insensitive lookup

            if user.is_verified:
                return Response(
                    {"message": _("This account is already verified.")},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if verify_otp(user.pk, otp):
                user.is_verified = True
                user.is_active = True # Ensure user is active upon verification
                user.save(update_fields=['is_verified', 'is_active']) # Specify fields to update

                tokens = self.get_tokens_for_user(user)
                user_data = UserSerializer(user).data

                logger.info(f"Email verified successfully for user: {email}")
                return Response(
                    {
                        "message": _("Email verified successfully. You are now logged in."),
                        "user": user_data,
                        "refresh": tokens['refresh'],
                        "access": tokens['access']
                    },
                    status=status.HTTP_200_OK
                )
            else:
                logger.warning(f"Invalid OTP attempt for email: {email}")
                return Response(
                    {"error": _("Invalid or expired OTP.")},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except User.DoesNotExist:
            logger.warning(f"Verification attempt for non-existent email: {email}")
            # Return a generic error to avoid revealing user existence
            return Response(
                {"error": _("Invalid OTP or email address.")},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error during email verification for {email}: {str(e)}", exc_info=True)
            return Response(
                {"error": _("An unexpected error occurred during verification. Please try again later.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
            return Response(
                {"error": _("Invalid or expired OTP.")},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Email verification error: {str(e)}")
            return Response(
                {"error": _("An error occurred during verification.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class LoginView(APIView):
    """API view for user login"""
    permission_classes = [AllowAny]
    throttle_classes = [AnonRateThrottle]

    def get_tokens_for_user(self, user, remember_me=False):
        """Generate tokens for user with custom expiration time"""
        refresh = RefreshToken.for_user(user)
        
        if remember_me:
            refresh.set_exp(lifetime=settings.SIMPLEJWT['REMEMBER_ME_REFRESH_TOKEN_LIFETIME'])
            refresh.access_token.set_exp(
                lifetime=settings.SIMPLEJWT['REMEMBER_ME_ACCESS_TOKEN_LIFETIME']
            )
        
        # Cache the tokens
        cache_key = f"user_tokens:{user.id}"
        cache.set(
            cache_key,
            {
                'access': str(refresh.access_token),
                'refresh': str(refresh),
                'created_at': timezone.now().isoformat()
            },
            timeout=settings.SIMPLEJWT['ACCESS_TOKEN_LIFETIME'].total_seconds()
        )
        
        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }

    def get_client_ip(self, request):
        cf_connecting_ip = request.headers.get('CF-Connecting-IP')
        if cf_connecting_ip:
            return cf_connecting_ip
        return request.META.get('REMOTE_ADDR')

    def post(self, request):
        try:
            client_ip = self.get_client_ip(request)
            # Use client_ip for rate limiting or logging
            # Add origin validation
            origin = request.headers.get('Origin')
            if origin and origin not in settings.CORS_ALLOWED_ORIGINS:
                raise UserAccountError(
                    detail=_("Login not allowed from this domain."),
                    status_code=status.HTTP_403_FORBIDDEN
                )

            # Add user agent validation
            user_agent = request.headers.get('User-Agent', '')
            if not user_agent or len(user_agent) < 10:  # Basic validation
                raise UserAccountError(
                    detail=_("Invalid client."),
                    status_code=status.HTTP_403_FORBIDDEN
                )

            serializer = LoginSerializer(data=request.data)
            if not serializer.is_valid():
                raise UserAccountError(detail=serializer.errors)

            # Add reCAPTCHA verification
            # recaptcha_token = request.data.get('recaptcha_token')
            # if not verify_recaptcha(recaptcha_token):
            #     raise RecaptchaVerificationError()

            email = serializer.validated_data['email']
            password = serializer.validated_data['password']
            remember_me = serializer.validated_data.get('remember_me', False)
            
            try:
                user = User.objects.get(email=email)
            except User.DoesNotExist:
                raise UserAccountError(detail=_("Invalid email or password."))

            if not user.check_password(password):
                raise UserAccountError(detail=_("Invalid email or password."))

            if not user.is_verified:
                raise UserAccountError(detail=_("Please verify your email before logging in."))

            if not user.is_active:
                raise UserAccountError(detail=_("This account is inactive."))

            # Generate tokens
            tokens = self.get_tokens_for_user(user, remember_me)
            
            # Prepare the response
            response = Response({
                'tokens': {
                    'access': tokens['access'],
                    'refresh': tokens['refresh'],
                }
            })

            # Set cookies
            cookie_max_age_access = (
                settings.SIMPLEJWT['REMEMBER_ME_ACCESS_TOKEN_LIFETIME'].total_seconds()
                if remember_me
                else settings.SIMPLEJWT['ACCESS_TOKEN_LIFETIME'].total_seconds()
            )
            cookie_max_age_refresh = (
                settings.SIMPLEJWT['REMEMBER_ME_REFRESH_TOKEN_LIFETIME'].total_seconds()
                if remember_me
                else settings.SIMPLEJWT['REFRESH_TOKEN_LIFETIME'].total_seconds()
            )

            response.set_cookie(
                'access_token',
                tokens['access'],
                max_age=cookie_max_age_access,
                httponly=False, # MODIFIED: Allow JS access to this cookie
                secure=not settings.DEBUG,  # True in production
                samesite='Lax',
                path='/'
            )
            
            response.set_cookie(
                'refresh_token',
                tokens['refresh'],
                max_age=cookie_max_age_refresh,
                httponly=True,
                secure=not settings.DEBUG,  # True in production
                samesite='Lax',
                path='/'
            )

            return response

        except (RecaptchaVerificationError, UserAccountError) as e:
            logger.error(f"Login error: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during login: {str(e)}", exc_info=True)
            raise UserAccountError(detail=_("An unexpected error occurred. Please try again later."))


class ForgotPasswordView(APIView):
    """API view for forgot password request"""
    permission_classes = [AllowAny]
    throttle_classes = [AnonRateThrottle]

    def post(self, request):
        serializer = ForgotPasswordSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']

            try:
                user = User.objects.get(email=email)
            except User.DoesNotExist:
                # Don't reveal that the user doesn't exist
                return Response(
                    {"message": _("If your email is registered, you will receive a password reset link.")},
                    status=status.HTTP_200_OK
                )

            # Send password reset email
            try:
                send_password_reset_email(request, user)
            except Exception as e:
                logger.error(f"Failed to send password reset email: {str(e)}")
                return Response(
                    {"error": _("Failed to send password reset email. Please try again later.")},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            return Response(
                {"message": _("Password reset link has been sent to your email.")},
                status=status.HTTP_200_OK
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResetPasswordView(APIView):
    """API view for password reset"""
    permission_classes = [AllowAny]
    throttle_classes = [AnonRateThrottle]

    def post(self, request, uidb64, token):
        serializer = ResetPasswordSerializer(data=request.data)
        if serializer.is_valid():
            try:
                uid = force_str(urlsafe_base64_decode(uidb64))
                user = User.objects.get(pk=uid)
            except (TypeError, ValueError, OverflowError, User.DoesNotExist):
                user = None

            if user is not None and password_reset_token.check_token(user, token):
                user.set_password(serializer.validated_data['new_password'])
                user.save()

                return Response(
                    {"message": _("Password has been reset successfully.")},
                    status=status.HTTP_200_OK
                )

            return Response(
                {"error": _("Invalid password reset link.")},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(APIView):
    """API view for user profile"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data)

    def patch(self, request):
        serializer = UserSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):
    """
    API view for user logout that blacklists the refresh token
    """
    permission_classes = [IsAuthenticated]
    throttle_classes = [UserRateThrottle]

    def post(self, request):
        try:
            refresh_token = request.data.get('refresh')
            if not refresh_token:
                # raise UserAccountError(
                #     detail=_("Refresh token is required."),
                #     status_code=status.HTTP_400_BAD_REQUEST
                # )
                error = UserAccountError(detail=_("Refresh token is required."))
                error.status_code = status.HTTP_400_BAD_REQUEST
                raise error

            # Blacklist the refresh token
            try:
                token = RefreshToken(refresh_token)
                token.blacklist()
            except TokenError:
                # raise UserAccountError(
                #     detail=_("Invalid or expired refresh token."),
                #     status_code=status.HTTP_400_BAD_REQUEST
                # )
                error = UserAccountError(detail=_("Invalid or expired refresh token."))
                error.status_code = status.HTTP_400_BAD_REQUEST
                raise error
            except Exception as e:
                logger.error(f"Error during token blacklisting: {str(e)}", exc_info=True)
                # raise UserAccountError(
                #     detail=_("An error occurred while logging out."),
                #     status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
                # )
                error = UserAccountError(detail=_("An error occurred while logging out."))
                error.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
                raise error

            logger.info(f"User {request.user.email} logged out successfully")
            return Response(
                {"message": _("Successfully logged out.")},
                status=status.HTTP_200_OK
            )

        except UserAccountError as e:
            logger.warning(f"Logout failed for user {request.user.email}: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error during logout: {str(e)}", exc_info=True)
            raise UserAccountError(
                detail=_("An unexpected error occurred during logout."),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CheckAuthView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data, status=status.HTTP_200_OK)
    


class ResendVerificationOTPView(APIView):
    """API view to resend the verification OTP"""
    permission_classes = [AllowAny]
    throttle_classes = [AnonRateThrottle]

    def post(self, request):
        serializer = ResendOTPSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.validated_data['email']
        generic_success_message = _("If an account with this email exists and requires verification, a new OTP has been sent.")

        try:
            user = User.objects.get(email__iexact=email)

            if user.is_verified:
                return Response(
                    {"message": _("This account is already verified.")},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # User exists and is not verified, proceed to resend OTP
            if send_verification_email(request, user):
                logger.info(f"Resent verification OTP for unverified user: {email}")
                return Response({"message": generic_success_message}, status=status.HTTP_200_OK)
            else:
                # Log email sending failure
                logger.error(f"Failed to resend verification email to {email}")
                raise EmailVerificationError(_("Failed to send verification email. Please try again later."))

        except User.DoesNotExist:
            # Don't reveal that the user doesn't exist
            logger.info(f"OTP resend requested for non-existent email: {email}")
            return Response({"message": generic_success_message}, status=status.HTTP_200_OK)
        except EmailVerificationError as e:
            # Reraise email sending errors to be handled by middleware
            raise e
        except Exception as e:
            logger.error(f"Unexpected error during OTP resend for {email}: {str(e)}", exc_info=True)
            # Generic error for unexpected issues
            return Response(
                {"error": _("An unexpected error occurred. Please try again later.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Helper function to generate tokens (can be moved to utils.py if preferred)
def get_tokens_for_user(user, remember_me=False):
    """Generate tokens for user with custom expiration time"""
    refresh = RefreshToken.for_user(user)
    
    if remember_me: # remember_me might not be directly applicable for OAuth
        # For OAuth, session duration is typically managed by the OAuth provider's token lifespan
        # and how you handle refresh tokens.
        # This part might need adjustment based on desired OAuth session behavior.
        pass # Potentially adjust SIMPLEJWT settings for OAuth initiated sessions if different lifetime is needed

    # Cache the tokens (optional, from existing LoginView)
    cache_key = f"user_tokens:{user.id}"
    cache.set(
        cache_key,
        {
            'access': str(refresh.access_token),
            'refresh': str(refresh),
            'created_at': timezone.now().isoformat()
        },
        timeout=settings.SIMPLEJWT['ACCESS_TOKEN_LIFETIME'].total_seconds()
    )
    
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token),
    }


# --- Google OAuth Views ---

class GoogleIdTokenLoginView(APIView):
    permission_classes = [AllowAny]
    serializer_class = GoogleLoginSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        id_token = serializer.validated_data['id_token']

        try:
            # Verify the ID token
            id_info = google_id_token.verify_oauth2_token(
                id_token, google_auth_requests.Request(), settings.GOOGLE_OAUTH2_CLIENT_ID
            )

            if id_info.get('iss') not in ['accounts.google.com', 'https://accounts.google.com']:
                logger.error(f"Google ID token verification failed: Wrong issuer - {id_info.get('iss')}")
                raise ValueError('Wrong issuer.')

            user_email = id_info.get('email')
            google_user_id = id_info.get('sub') # 'sub' is the standard claim for Google User ID

            if not user_email:
                logger.error("Google OAuth: Email not found in ID token.")
                return Response(
                    {"error": _("Email not found in Google profile.")},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if not google_user_id:
                logger.error("Google OAuth: Google User ID (sub) not found in ID token.")
                return Response(
                    {"error": _("Google User ID not found in Google profile.")},
                    status=status.HTTP_400_BAD_REQUEST
                )

            user_first_name = id_info.get('given_name', '')
            user_last_name = id_info.get('family_name', '')
            
            # User provisioning and login logic
            try:
                user = User.objects.get(email=user_email)
                # User exists with this email. Check if it's already linked to this Google ID or another.
                if user.google_id and user.google_id != google_user_id:
                    # Email is associated with a different Google account
                    logger.error(f"User {user_email} exists but is linked to a different Google ID.")
                    return Response(
                        {"error": _("This email is already associated with a different Google account.")},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                if not user.google_id: # Email exists (e.g. email/password signup), link Google ID
                    user.google_id = google_user_id
                    user.auth_provider = 'google' # Or decide on a strategy for merged accounts, e.g., keep original or update
                    logger.info(f"Linking Google ID {google_user_id} to existing user {user_email}.")

                # Update names if they are empty or potentially different
                if not user.first_name and user_first_name:
                    user.first_name = user_first_name
                if not user.last_name and user_last_name:
                    user.last_name = user_last_name
                
                user.is_verified = True # Google verifies email
                user.is_active = True # Ensure user is active
                user.save()
                logger.info(f"User {user_email} logged in via Google ID token (existing user).")

            except User.DoesNotExist:
                # Create new user
                user = User.objects.create(
                    email=user_email,
                    first_name=user_first_name,
                    last_name=user_last_name,
                    google_id=google_user_id,
                    auth_provider='google',
                    is_verified=True,
                    is_active=True,
                )
                user.set_unusable_password() # No password for Google-only users
                user.save()
                logger.info(f"New user created via Google ID token: {user_email}")

            # Generate your application's tokens
            app_tokens = get_tokens_for_user(user) 

            response_data = {
                "message": _("Successfully authenticated with Google."),
                "user": UserSerializer(user).data,
                "tokens": app_tokens
            }
            return Response(response_data, status=status.HTTP_200_OK)

        except ValueError as e: # Handles verify_oauth2_token errors or our custom ValueErrors
            logger.error(f"Google ID token verification failed: {str(e)}")
            return Response(
                {"error": _("Invalid Google ID token: ") + str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error during Google ID token login for email {user_email if 'user_email' in locals() else 'unknown'}: {str(e)}", exc_info=True)
            return Response(
                {"error": _("An unexpected error occurred during Google authentication.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


# Renaming existing Google OAuth views to avoid conflict and clarify their purpose (Authorization Code Flow)
class GoogleOAuthRedirectView(APIView): # Renamed from GoogleLoginView
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        GOOGLE_OAUTH2_CLIENT_ID = settings.GOOGLE_OAUTH2_CLIENT_ID
        GOOGLE_OAUTH2_REDIRECT_URI = settings.GOOGLE_OAUTH2_REDIRECT_URI
        
        if not GOOGLE_OAUTH2_CLIENT_ID or not GOOGLE_OAUTH2_REDIRECT_URI:
            logger.error("Google OAuth2 client ID or redirect URI not configured.")
            return Response(
                {"error": _("Google OAuth2 is not configured correctly.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # Define scopes: 'openid' is for OpenID Connect, 'email' and 'profile' are common.
        scope = "openid email profile"
        
        # Construct the authorization URL
        auth_url = (
            f"https://accounts.google.com/o/oauth2/v2/auth?"
            f"client_id={GOOGLE_OAUTH2_CLIENT_ID}&"
            f"redirect_uri={GOOGLE_OAUTH2_REDIRECT_URI}&"
            f"response_type=code&"
            f"scope={scope}&"
            f"access_type=offline" # Request refresh token
        )
        return redirect(auth_url)


class GoogleOAuthCallbackView(APIView): # Renamed from GoogleCallbackView
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        code = request.GET.get('code')
        error = request.GET.get('error')

        if error:
            logger.error(f"Google OAuth error: {error}")
            return Response(
                {"error": _("Google authentication failed: ") + error},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not code:
            logger.error("Google OAuth: No code provided in callback.")
            return Response(
                {"error": _("Google authentication failed: No authorization code provided.")},
                status=status.HTTP_400_BAD_REQUEST
            )

        GOOGLE_OAUTH2_CLIENT_ID = settings.GOOGLE_OAUTH2_CLIENT_ID
        GOOGLE_OAUTH2_CLIENT_SECRET = settings.GOOGLE_OAUTH2_CLIENT_SECRET
        GOOGLE_OAUTH2_REDIRECT_URI = settings.GOOGLE_OAUTH2_REDIRECT_URI

        if not all([GOOGLE_OAUTH2_CLIENT_ID, GOOGLE_OAUTH2_CLIENT_SECRET, GOOGLE_OAUTH2_REDIRECT_URI]):
            logger.error("Google OAuth2 settings are incomplete.")
            return Response(
                {"error": _("Google OAuth2 configuration error on server.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        token_url = "https://oauth2.googleapis.com/token"
        payload = {
            'code': code,
            'client_id': GOOGLE_OAUTH2_CLIENT_ID,
            'client_secret': GOOGLE_OAUTH2_CLIENT_SECRET,
            'redirect_uri': GOOGLE_OAUTH2_REDIRECT_URI,
            'grant_type': 'authorization_code',
        }

        try:
            token_response = requests.post(token_url, data=payload)
            token_response.raise_for_status()  # Raise HTTPError for bad responses (4XX or 5XX)
            token_data = token_response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to exchange code for token: {str(e)} - Response: {token_response.text if 'token_response' in locals() else 'N/A'}")
            return Response(
                {"error": _("Failed to obtain tokens from Google.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        id_token_jwt = token_data.get('id_token')
        # access_token_google = token_data.get('access_token') # Can be stored/used if needed for other Google APIs

        if not id_token_jwt:
            logger.error("Google OAuth: No id_token in token response.")
            return Response(
                {"error": _("Failed to get ID token from Google.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        try:
            # Verify the ID token
            id_info = google_id_token.verify_oauth2_token(
                id_token_jwt, google_auth_requests.Request(), GOOGLE_OAUTH2_CLIENT_ID
            )

            if id_info.get('iss') not in ['accounts.google.com', 'https://accounts.google.com']:
                raise ValueError('Wrong issuer.')

            user_email = id_info.get('email')
            if not user_email:
                logger.error("Google OAuth: Email not found in ID token.")
                return Response(
                    {"error": _("Email not found in Google profile.")},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            user_first_name = id_info.get('given_name', '')
            user_last_name = id_info.get('family_name', '')
            # email_verified = id_info.get('email_verified', False) # Google usually ensures this for OAuth flow

        except ValueError as e:
            # Invalid token
            logger.error(f"Google ID token verification failed: {str(e)}")
            return Response(
                {"error": _("Invalid Google ID token.")},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected error during Google ID token verification: {str(e)}")
            return Response(
                {"error": _("An error occurred while verifying Google token.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # User provisioning and login
        try:
            user, created = User.objects.get_or_create(
                email=user_email,
                defaults={
                    'first_name': user_first_name,
                    'last_name': user_last_name,
                    'is_verified': True, # User verified by Google
                    'is_active': True,
                    'auth_provider': 'google', # Set auth_provider for new Google users
                    # 'username': user_email # If your User model uses username and it should be unique
                }
            )

            if not created: # Existing user
                if not user.google_id: # If email existed but not via google, link it.
                    user.google_id = id_info.get('sub')
                    user.auth_provider = 'google' 
                if not user.is_active:
                    user.is_active = True
                if not user.is_verified: # Should be verified by Google, but ensure it
                    user.is_verified = True
                # Optionally update first_name/last_name if they changed in Google profile
                if user.first_name != user_first_name or user.last_name != user_last_name:
                    user.first_name = user_first_name
                    user.last_name = user_last_name
                user.save()
                logger.info(f"User logged in via Google OAuth: {user_email}")
            else: # New user created
                user.google_id = id_info.get('sub') # Ensure google_id is set for new users
                user.set_unusable_password()
                user.save()
                logger.info(f"New user created via Google OAuth: {user_email}")


            # Generate your application's tokens
            app_tokens = get_tokens_for_user(user) 

            # The user is successfully authenticated/created, and tokens are generated.
            # Now, set tokens as cookies and redirect to the frontend dashboard.
            
            frontend_dashboard_url = "https://seoanalyser.com.au/dashboard"
            response = redirect(frontend_dashboard_url)

            # Set cookies for access and refresh tokens
            access_token_lifetime = settings.SIMPLEJWT['ACCESS_TOKEN_LIFETIME']
            refresh_token_lifetime = settings.SIMPLEJWT['REFRESH_TOKEN_LIFETIME']

            response.set_cookie(
                'access_token',
                app_tokens['access'],
                max_age=access_token_lifetime.total_seconds(),
                httponly=False, # MODIFIED: Allow JS access to this cookie
                secure=not settings.DEBUG,  # True in production
                samesite='Lax',
                path='/' 
            )
            response.set_cookie(
                'refresh_token',
                app_tokens['refresh'],
                max_age=refresh_token_lifetime.total_seconds(),
                httponly=True, # Recommended for security
                secure=not settings.DEBUG,  # True in production
                samesite='Lax',
                path='/'
            )
            
            logger.info(f"Redirecting user {user_email} to {frontend_dashboard_url} after Google OAuth login.")
            return response

        except Exception as e:
            logger.error(f"Error during user provisioning or token generation: {str(e)}", exc_info=True)
            return Response(
                {"error": _("An unexpected error occurred after Google authentication.")},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class WhiteLabelSettingsView(generics.GenericAPIView):
    serializer_class = WhiteLabelSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def get_object(self):
        try:
            return WhiteLabel.objects.get(user=self.request.user)
        except WhiteLabel.DoesNotExist:
            return None

    def get_queryset(self):
        return WhiteLabel.objects.filter(user=self.request.user)

    def get(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance is None:
            return Response({"detail": _("White label settings not found.")}, status=status.HTTP_404_NOT_FOUND)
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def post(self, request, *args, **kwargs):
        if WhiteLabel.objects.filter(user=request.user).exists():
            return Response(
                {"detail": _("White label settings already exist. Use PUT or PATCH to update.")},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def put(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance is None:
            return Response(
                {"detail": _("White label settings not found. Use POST to create.")},
                status=status.HTTP_404_NOT_FOUND
            )
        
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def patch(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance is None:
            return Response({"detail": _("White label settings not found.")}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request
        return context


class CreatePaymentSessionView(APIView):
    permission_classes = [AllowAny] # Allow unauthenticated access for now, or IsAuthenticated
    serializer_class = CreatePaymentSerializer
    stripe_service = StripeService() # Use the service for session creation

    def _check_existing_subscription_for_product(self, user, target_product_id):
        """
        Check if user has an existing active subscription for the given product.
        Returns (has_active_sub, subscription_info, action_needed)
        """
        if not user or not user.is_authenticated:
            return False, None, None
            
        try:
            # Get all user's local subscriptions
            user_subscriptions = Subscription.objects.filter(user=user)
            
            for local_sub in user_subscriptions:
                if not local_sub.stripe_subscription_id:
                    continue
                    
                try:
                    # Retrieve subscription from Stripe to get current status and product info
                    stripe_sub = stripe.Subscription.retrieve(
                        local_sub.stripe_subscription_id,
                        expand=['items.data.price.product']
                    )
                    
                    # Check if this subscription is for the same product
                    subscription_product_id = None
                    items_obj = stripe_sub.get('items')
                    if (items_obj and hasattr(items_obj, 'data') and items_obj.data and 
                        len(items_obj.data) > 0 and items_obj.data[0].price and items_obj.data[0].price.product):
                        
                        product_data = items_obj.data[0].price.product
                        if hasattr(product_data, 'id'):
                            subscription_product_id = product_data.id
                        elif isinstance(product_data, str):
                            subscription_product_id = product_data
                    
                    if subscription_product_id == target_product_id:
                        # Same product - check subscription status
                        if stripe_sub.status in ['active', 'trialing']:
                            # Active subscription for same product
                            # Get period end timestamp
                            period_end_timestamp = None
                            if (items_obj and hasattr(items_obj, 'data') and items_obj.data and 
                                len(items_obj.data) > 0):
                                period_end_timestamp = items_obj.data[0].get('current_period_end')
                            if not period_end_timestamp:
                                period_end_timestamp = stripe_sub.get('current_period_end')
                                
                            return True, {
                                'stripe_subscription_id': stripe_sub.id,
                                'status': stripe_sub.status,
                                'current_period_end': period_end_timestamp,
                                'cancel_at_period_end': stripe_sub.get('cancel_at_period_end'),
                                'product_name': product_data.name if hasattr(product_data, 'name') else 'Unknown Product'
                            }, 'active'
                        elif stripe_sub.status in ['past_due', 'unpaid']:
                            # Subscription needs payment
                            # Get period end timestamp
                            period_end_timestamp = None
                            if (items_obj and hasattr(items_obj, 'data') and items_obj.data and 
                                len(items_obj.data) > 0):
                                period_end_timestamp = items_obj.data[0].get('current_period_end')
                            if not period_end_timestamp:
                                period_end_timestamp = stripe_sub.get('current_period_end')
                                
                            return True, {
                                'stripe_subscription_id': stripe_sub.id,
                                'status': stripe_sub.status,
                                'current_period_end': period_end_timestamp,
                                'product_name': product_data.name if hasattr(product_data, 'name') else 'Unknown Product'
                            }, 'needs_payment'
                        elif stripe_sub.status in ['canceled']:
                            # Check if still in grace period - get period end from subscription items
                            period_end_timestamp = None
                            
                            # Try to get period end from subscription items first
                            if (items_obj and hasattr(items_obj, 'data') and items_obj.data and 
                                len(items_obj.data) > 0):
                                period_end_timestamp = items_obj.data[0].get('current_period_end')
                            
                            # Fallback to top-level current_period_end
                            if not period_end_timestamp:
                                period_end_timestamp = stripe_sub.get('current_period_end')
                            
                            if period_end_timestamp:
                                period_end = dt_module.datetime.fromtimestamp(period_end_timestamp, tz=dt_module.timezone.utc)
                                if period_end > dt_module.datetime.now(tz=dt_module.timezone.utc):
                                    return True, {
                                        'stripe_subscription_id': stripe_sub.id,
                                        'status': stripe_sub.status,
                                        'current_period_end': period_end_timestamp,
                                        'product_name': product_data.name if hasattr(product_data, 'name') else 'Unknown Product'
                                    }, 'grace_period'
                                
                except stripe.error.StripeError as e:
                    logger.warning(f"CreatePaymentSessionView: Error checking subscription {local_sub.stripe_subscription_id}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"CreatePaymentSessionView: Unexpected error checking subscription {local_sub.stripe_subscription_id}: {e}")
                    continue
                    
            return False, None, None
            
        except Exception as e:
            logger.error(f"CreatePaymentSessionView: Error checking existing subscriptions for user {user.id}: {e}")
            return False, None, None

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"CreatePaymentSessionView: Invalid data: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        plan_id = validated_data['plan_id']
        billing_cycle = validated_data.get('billing_cycle') # Can be None

        customer_email = None
        user_id = None # Define user_id, though not directly used in the StripeService call signature here
        if request.user.is_authenticated:
            customer_email = request.user.email
            user_id = request.user.id
        elif 'email' in validated_data:
            customer_email = validated_data['email']
        else:
            logger.warning("CreatePaymentSessionView: Email is required for unauthenticated users.")
            return Response(
                {'error': 'Email is required for unauthenticated users'},
                status=status.HTTP_400_BAD_REQUEST
            )

        task_id = validated_data.get('task_id')
        selected_price_id = None
        target_product_id = None

        try:
            if plan_id and plan_id.startswith("price_"):
                # A specific Price ID was provided
                logger.info(f"CreatePaymentSessionView: Received specific Price ID: {plan_id}")
                # We should verify this price ID is active and valid before using it.
                try:
                    price_obj = stripe.Price.retrieve(plan_id, expand=['product'])
                    if not price_obj.active:
                        logger.warning(f"CreatePaymentSessionView: Provided Price ID {plan_id} is not active.")
                        return Response({'error': f"The selected plan (Price ID: {plan_id}) is not active."}, status=status.HTTP_400_BAD_REQUEST)
                    selected_price_id = price_obj.id
                    # Get product ID for duplicate check
                    if price_obj.product:
                        if hasattr(price_obj.product, 'id'):
                            target_product_id = price_obj.product.id
                        elif isinstance(price_obj.product, str):
                            target_product_id = price_obj.product
                except stripe.error.StripeError as e:
                    logger.warning(f"CreatePaymentSessionView: Invalid Price ID {plan_id}. Error: {str(e)}")
                    return Response({'error': f"Invalid plan ID: {plan_id}. Please provide a valid Product ID or Price ID.", "details": str(e)}, status=status.HTTP_400_BAD_REQUEST)
            else:
                # Assume plan_id is a Product ID, proceed with existing logic
                logger.info(f"CreatePaymentSessionView: Received Product ID: {plan_id}. Looking up prices.")
                target_product_id = plan_id  # Store the product ID for duplicate check
                active_prices = stripe.Price.list(product=plan_id, active=True, limit=10) 
            
                if not active_prices.data:
                    logger.warning(f"CreatePaymentSessionView: No active prices found for Stripe Product ID {plan_id}")
                    return Response({'error': f"No active plans found for product {plan_id}."}, status=status.HTTP_400_BAD_REQUEST)

                if len(active_prices.data) == 1:
                    selected_price_id = active_prices.data[0].id
                elif billing_cycle:
                    for price in active_prices.data:
                        if price.recurring and price.recurring.interval == billing_cycle.rstrip('ly'): 
                            selected_price_id = price.id
                            break
                    if not selected_price_id:
                        logger.warning(f"CreatePaymentSessionView: No price found for product {plan_id} with billing cycle '{billing_cycle}'")
                        return Response({'error': f"No '{billing_cycle}' plan found for the selected product. Please choose a cycle or contact support."}, status=status.HTTP_400_BAD_REQUEST)
                else:
                    logger.warning(f"CreatePaymentSessionView: Product {plan_id} has multiple prices, but no billing_cycle was specified.")
                    return Response({'error': f"This product has multiple billing options. Please specify a billing cycle (e.g., monthly, yearly)."}, status=status.HTTP_400_BAD_REQUEST)
            
            if not selected_price_id:
                logger.error(f"CreatePaymentSessionView: Could not determine a Price ID for input '{plan_id}' with cycle '{billing_cycle}'.")
                return Response({'error': "Could not determine a suitable plan. Please contact support."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Check for existing subscriptions for authenticated users (only for subscription products)
            if request.user.is_authenticated and target_product_id:
                # First check if this is a subscription product
                try:
                    price_for_check = stripe.Price.retrieve(selected_price_id)
                    if price_for_check.type == 'recurring':  # Only check for subscription products
                        has_existing, sub_info, action_needed = self._check_existing_subscription_for_product(
                            request.user, target_product_id
                        )
                        
                        if has_existing and action_needed:
                            if action_needed == 'active':
                                return Response({
                                    'error': 'duplicate_subscription',
                                    'message': f"You already have an active subscription for {sub_info['product_name']}. Your subscription is active until {dt_module.datetime.fromtimestamp(sub_info['current_period_end'], tz=dt_module.timezone.utc).strftime('%B %d, %Y')}.",
                                    'subscription_info': sub_info,
                                    'suggested_action': 'manage_existing'
                                }, status=status.HTTP_409_CONFLICT)
                            elif action_needed == 'needs_payment':
                                return Response({
                                    'error': 'subscription_needs_payment',
                                    'message': f"You have an existing subscription for {sub_info['product_name']} that needs payment. Please update your payment method instead of creating a new subscription.",
                                    'subscription_info': sub_info,
                                    'suggested_action': 'update_payment_method'
                                }, status=status.HTTP_409_CONFLICT)
                            elif action_needed == 'grace_period':
                                return Response({
                                    'error': 'subscription_in_grace_period',
                                    'message': f"You have a canceled subscription for {sub_info['product_name']} that's still active until {dt_module.datetime.fromtimestamp(sub_info['current_period_end'], tz=dt_module.timezone.utc).strftime('%B %d, %Y')}. You can reactivate it instead of creating a new subscription.",
                                    'subscription_info': sub_info,
                                    'suggested_action': 'reactivate_existing'
                                }, status=status.HTTP_409_CONFLICT)
                except stripe.error.StripeError as e:
                    logger.warning(f"CreatePaymentSessionView: Error checking price type for {selected_price_id}: {e}")
                    # Continue with checkout creation if we can't determine the price type

            redirect_url = self.stripe_service.create_checkout_session(
                user=request.user if request.user.is_authenticated else None,
                stripe_price_id=selected_price_id, 
                customer_email=customer_email,
                success_url_base=validated_data.get('success_url'),
                cancel_url_base=validated_data.get('cancel_url'),
                client_reference_id=validated_data.get('client_reference_id'),
                task_id=task_id,
                existing_customer_id=validated_data.get('existing_customer_id'),
                coupon_id=validated_data.get('coupon_id'),
                allow_promotion_codes=validated_data.get('allow_promotion_codes')
            )
            return Response({"redirect_url": redirect_url}, status=status.HTTP_200_OK)
        except stripe.error.StripeError as e:
            logger.error(f"CreatePaymentSessionView: Stripe error - {str(e)}", exc_info=True)
            return Response({"error": "Failed to create Stripe checkout session.", "details": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except ValueError as e: # Catch specific ValueErrors from StripeService
            logger.warning(f"CreatePaymentSessionView: Validation error - {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"CreatePaymentSessionView: Unexpected error - {str(e)}", exc_info=True)
            return Response({"error": "An unexpected error occurred."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserProfileSettingsView(generics.RetrieveUpdateAPIView):
    """API view for retrieving and updating user profile settings."""
    serializer_class = UserProfileSettingsSerializer # Corrected: UserProfileSettingsSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        # Returns the request.user instance, as we are updating the user him/herself
        return self.request.user

    def get_queryset(self):
        # Not strictly necessary for RetrieveUpdateAPIView if get_object is overridden,
        # but good practice to define.
        return User.objects.filter(pk=self.request.user.pk)

    # Optional: Add specific logic for update if needed, e.g., after successful update
    # def update(self, request, *args, **kwargs):
    #     response = super().update(request, *args, **kwargs)
    #     if response.status_code == status.HTTP_200_OK:
    #         # Perform any post-update actions here
    #         logger.info(f"User profile settings updated for {request.user.email}")
    #     return response


class StripePricingListView(APIView):
    """
    API view to fetch and list active products and their prices from Stripe.
    Output is a dictionary where keys are product names, and values are lists of price plans.
    """
    permission_classes = [AllowAny] 

    def get(self, request, *args, **kwargs):
        products_map = {} 
        try:
            active_products = stripe.Product.list(active=True, limit=20)

            for product in active_products.data:
                if not product.name:
                    logger.warning(f"StripePricingListView: Skipping product ID {product.id} because it has no name.")
                    continue

                product_key_name = product.name
                product_description = product.description or ""
                parent_product_id = product.id
                parent_product_metadata = product.metadata or {}
                
                if product_key_name not in products_map:
                    products_map[product_key_name] = []
                
                product_prices = stripe.Price.list(product=product.id, active=True, limit=10) 

                for price in product_prices.data:
                    if not price.recurring: 
                        logger.info(f"StripePricingListView: Skipping non-recurring price {price.id} for product {product.id}.")
                        continue

                    # The name for the plan is now the product_key_name (the product's actual name)
                    # No separate 'name' field inside the price plan details itself.
                    plan_details = {
                        "id": price.id, # Stripe Price ID
                        "description": product_description, # Description from parent product
                        "price": Decimal(price.unit_amount) / Decimal(100) if price.unit_amount is not None else None,
                        "currency": price.currency,
                        "interval": price.recurring.interval,
                        "interval_count": price.recurring.interval_count,
                        "product_id": parent_product_id, # ID of the parent product
                        "metadata": parent_product_metadata, # Metadata from parent product
                        "price_metadata": price.metadata or {}, # Price's own metadata
                    }
                    products_map[product_key_name].append(plan_details)
                
                # If a product has no recurring prices, its entry in the map will be an empty list.
                # Optionally, remove products with no prices:
                if not products_map[product_key_name]:
                    logger.info(f"StripePricingListView: Product '{product_key_name}' (ID: {product.id}) has no active recurring prices. Removing from output.")
                    del products_map[product_key_name]
                            
            return Response(products_map, status=status.HTTP_200_OK)

        except stripe.error.StripeError as e:
            logger.error(f"StripePricingListView: Stripe API error - {str(e)}", exc_info=True)
            return Response({"error": "Failed to fetch pricing from Stripe.", "details": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except Exception as e:
            logger.error(f"StripePricingListView: Unexpected error - {str(e)}", exc_info=True)
            return Response({"error": "An unexpected error occurred while fetching pricing."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserSubscriptionsView(generics.ListAPIView):
    """API view to list user's subscriptions with management options."""
    serializer_class = SubscriptionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Subscription.objects.filter(user=self.request.user).order_by('-created_at')

    def get(self, request, *args, **kwargs):
        """Enhanced subscription list with management options."""
        queryset = self.get_queryset()
        subscriptions_data = []
        
        for local_sub in queryset:
            if not local_sub.stripe_subscription_id:
                continue
                
            try:
                # Get detailed subscription info from Stripe
                stripe_sub = stripe.Subscription.retrieve(
                    local_sub.stripe_subscription_id,
                    expand=['items.data.price.product', 'default_payment_method', 'latest_invoice']
                )
                
                # Get product info
                product_name = "Unknown Product"
                product_id = None
                price_amount = None
                price_currency = None
                price_interval = None
                
                if (stripe_sub.items and stripe_sub.items.data and 
                    stripe_sub.items.data[0].price):
                    
                    price_data = stripe_sub.items.data[0].price
                    if price_data.unit_amount:
                        price_amount = Decimal(price_data.unit_amount) / Decimal(100)
                    price_currency = price_data.currency
                    if price_data.recurring:
                        price_interval = price_data.recurring.interval
                    
                    if price_data.product:
                        if hasattr(price_data.product, 'id'):
                            product_id = price_data.product.id
                            if hasattr(price_data.product, 'name'):
                                product_name = price_data.product.name
                        elif isinstance(price_data.product, str):
                            product_id = price_data.product
                            try:
                                product_obj = stripe.Product.retrieve(product_id)
                                product_name = product_obj.name
                            except stripe.error.StripeError:
                                pass
                
                # Determine available actions based on subscription status
                available_actions = []
                if stripe_sub.status == 'active':
                    available_actions.extend(['cancel', 'update_payment_method'])
                    if stripe_sub.cancel_at_period_end:
                        available_actions.append('reactivate')
                elif stripe_sub.status == 'trialing':
                    available_actions.extend(['cancel', 'update_payment_method'])
                elif stripe_sub.status in ['past_due', 'unpaid']:
                    available_actions.extend(['update_payment_method', 'cancel'])
                elif stripe_sub.status == 'canceled':
                    # Check if still in grace period
                    if stripe_sub.current_period_end:
                        period_end = dt_module.datetime.fromtimestamp(stripe_sub.current_period_end, tz=dt_module.timezone.utc)
                        if period_end > dt_module.datetime.now(tz=dt_module.timezone.utc):
                            available_actions.append('reactivate')
                
                subscription_info = {
                    'id': str(local_sub.id),
                    'stripe_subscription_id': stripe_sub.id,
                    'product_name': product_name,
                    'product_id': product_id,
                    'status': stripe_sub.status,
                    'current_period_start': dt_module.datetime.fromtimestamp(stripe_sub.current_period_start, tz=dt_module.timezone.utc).isoformat() if stripe_sub.current_period_start else None,
                    'current_period_end': dt_module.datetime.fromtimestamp(stripe_sub.current_period_end, tz=dt_module.timezone.utc).isoformat() if stripe_sub.current_period_end else None,
                    'cancel_at_period_end': stripe_sub.cancel_at_period_end,
                    'trial_start': dt_module.datetime.fromtimestamp(stripe_sub.trial_start, tz=dt_module.timezone.utc).isoformat() if stripe_sub.trial_start else None,
                    'trial_end': dt_module.datetime.fromtimestamp(stripe_sub.trial_end, tz=dt_module.timezone.utc).isoformat() if stripe_sub.trial_end else None,
                    'price_amount': price_amount,
                    'price_currency': price_currency,
                    'price_interval': price_interval,
                    'available_actions': available_actions,
                    'created_at': local_sub.created_at.isoformat(),
                    'updated_at': local_sub.updated_at.isoformat(),
                }
                
                subscriptions_data.append(subscription_info)
                
            except stripe.error.StripeError as e:
                logger.error(f"UserSubscriptionsView: Stripe error retrieving subscription {local_sub.stripe_subscription_id}: {e}")
                # Include basic info even if Stripe call fails
                subscriptions_data.append({
                    'id': str(local_sub.id),
                    'stripe_subscription_id': local_sub.stripe_subscription_id,
                    'product_name': 'Unknown (Stripe Error)',
                    'status': local_sub.status,
                    'available_actions': [],
                    'error': 'Unable to fetch current subscription details',
                    'created_at': local_sub.created_at.isoformat(),
                    'updated_at': local_sub.updated_at.isoformat(),
                })
            except Exception as e:
                logger.error(f"UserSubscriptionsView: Unexpected error processing subscription {local_sub.stripe_subscription_id}: {e}")
                continue
        
        return Response({
            'subscriptions': subscriptions_data,
            'count': len(subscriptions_data)
        }, status=status.HTTP_200_OK)


class ManageSubscriptionView(APIView):
    """API view to manage individual subscriptions (cancel, reactivate, etc.)."""
    permission_classes = [IsAuthenticated]
    
    def post(self, request, subscription_id, *args, **kwargs):
        """Perform actions on a subscription."""
        action = request.data.get('action')
        
        if not action:
            return Response({'error': 'Action is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Get the local subscription
            local_sub = Subscription.objects.get(id=subscription_id, user=request.user)
            
            if not local_sub.stripe_subscription_id:
                return Response({'error': 'No Stripe subscription ID found'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get current subscription from Stripe
            stripe_sub = stripe.Subscription.retrieve(local_sub.stripe_subscription_id)
            
            if action == 'cancel':
                # Cancel subscription at period end
                updated_sub = stripe.Subscription.modify(
                    local_sub.stripe_subscription_id,
                    cancel_at_period_end=True
                )
                message = f"Subscription will be canceled at the end of the current billing period ({dt_module.datetime.fromtimestamp(updated_sub.current_period_end, tz=dt_module.timezone.utc).strftime('%B %d, %Y')})."
                
            elif action == 'reactivate':
                # Reactivate subscription
                if stripe_sub.status == 'canceled':
                    return Response({'error': 'Cannot reactivate a fully canceled subscription. Please create a new subscription.'}, status=status.HTTP_400_BAD_REQUEST)
                
                updated_sub = stripe.Subscription.modify(
                    local_sub.stripe_subscription_id,
                    cancel_at_period_end=False
                )
                message = "Subscription has been reactivated and will continue at the next billing cycle."
                
            elif action == 'cancel_immediately':
                # Cancel subscription immediately
                updated_sub = stripe.Subscription.cancel(local_sub.stripe_subscription_id)
                message = "Subscription has been canceled immediately."
                
            elif action == 'update_payment_method':
                # Create a setup intent for updating payment method
                setup_intent = stripe.SetupIntent.create(
                    customer=stripe_sub.customer,
                    usage='off_session',
                    metadata={
                        'subscription_id': local_sub.stripe_subscription_id,
                        'user_id': str(request.user.id)
                    }
                )
                return Response({
                    'setup_intent_client_secret': setup_intent.client_secret,
                    'message': 'Use the client secret to update your payment method.'
                }, status=status.HTTP_200_OK)
                
            else:
                return Response({'error': f'Unknown action: {action}'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Update local subscription status
            local_sub.status = updated_sub.status
            local_sub.save(update_fields=['status', 'updated_at'])
            
            return Response({
                'message': message,
                'subscription': {
                    'id': str(local_sub.id),
                    'stripe_subscription_id': updated_sub.id,
                    'status': updated_sub.status,
                    'cancel_at_period_end': updated_sub.cancel_at_period_end,
                    'current_period_end': dt_module.datetime.fromtimestamp(updated_sub.current_period_end, tz=dt_module.timezone.utc).isoformat() if updated_sub.current_period_end else None,
                }
            }, status=status.HTTP_200_OK)
            
        except Subscription.DoesNotExist:
            return Response({'error': 'Subscription not found'}, status=status.HTTP_404_NOT_FOUND)
        except stripe.error.StripeError as e:
            logger.error(f"ManageSubscriptionView: Stripe error for subscription {subscription_id}: {e}")
            return Response({'error': f'Stripe error: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"ManageSubscriptionView: Unexpected error for subscription {subscription_id}: {e}")
            return Response({'error': 'An unexpected error occurred'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# --- Admin Views for Token Management ---

class AdminUserListView(generics.ListAPIView):
    queryset = User.objects.all().order_by('-date_joined')
    serializer_class = AdminUserListSerializer
    permission_classes = [IsAdminUser]

class AdminUserSessionListView(generics.ListAPIView):
    serializer_class = OutstandingTokenAdminSerializer
    permission_classes = [IsAdminUser]

    def get_queryset(self):
        user_id = self.kwargs.get('user_id')
        user = get_object_or_404(User, pk=user_id)
        # Filter out tokens that are already blacklisted or expired
        return OutstandingToken.objects.filter(
            user=user,
            expires_at__gt=aware_utcnow()
        ).select_related('blacklistedtoken').order_by('-created_at')

class AdminRevokeTokenView(APIView):
    permission_classes = [IsAdminUser]

    def post(self, request, token_id, *args, **kwargs):
        try:
            outstanding_token = OutstandingToken.objects.get(id=token_id)
        except OutstandingToken.DoesNotExist:
            return Response({"error": "Token not found."}, status=status.HTTP_404_NOT_FOUND)

        if hasattr(outstanding_token, 'blacklistedtoken'):
             return Response({"message": "Token already revoked."}, status=status.HTTP_400_BAD_REQUEST)

        # Blacklist the token
        try:
            # Create a BlacklistedToken entry
            BlacklistedToken.objects.create(token=outstanding_token)
            
            # Additionally, clear any server-side cache if you were caching tokens by JTI or user ID
            # For example, if you cached by user ID:
            # cache_key = f"user_tokens:{outstanding_token.user.id}"
            # cache.delete(cache_key)
            # Or if you cache by JTI (less common for blacklisting this way):
            # cache.delete(f"token_jti:{outstanding_token.jti}")
            
            logger.info(f"Admin {request.user.email} revoked token ID {token_id} for user {outstanding_token.user.email}")
            return Response({"message": "Token successfully revoked."}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error revoking token ID {token_id}: {str(e)}", exc_info=True)
            return Response({"error": "Failed to revoke token."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# --- End Admin Views ---

