import logging
import time
import threading
from datetime import datetime
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonR<PERSON>ponse
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework.request import Request
import jwt
from django.conf import settings

# Set up authentication middleware logger
auth_middleware_logger = logging.getLogger('auth_middleware')
auth_middleware_logger.setLevel(logging.DEBUG)
auth_middleware_logger.propagate = False

# Create file handler for auth middleware logs
import os
logs_dir = os.path.join(settings.BASE_DIR, 'logs')
os.makedirs(logs_dir, exist_ok=True)

auth_middleware_log_file = os.path.join(logs_dir, 'auth.log')
auth_middleware_file_handler = logging.FileHandler(auth_middleware_log_file)
auth_middleware_file_handler.setLevel(logging.DEBUG)

# Create formatter
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(thread)d] - %(funcName)s:%(lineno)d - %(message)s'
)
auth_middleware_file_handler.setFormatter(formatter)
auth_middleware_logger.addHandler(auth_middleware_file_handler)


class AuthenticationLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log detailed authentication information for debugging
    intermittent authentication issues.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
        auth_middleware_logger.info("AuthenticationLoggingMiddleware initialized")
    
    def process_request(self, request):
        """Process incoming request and log authentication details"""
        
        # Skip non-API requests and static files
        if not request.path.startswith('/api/') or request.path.startswith('/static/'):
            return None
        
        request_id = f"{threading.current_thread().ident}_{int(time.time() * 1000000)}"
        request.auth_request_id = request_id
        
        auth_middleware_logger.debug(f"[{request_id}] Processing request: {request.method} {request.path}")
        
        # Log request headers related to authentication
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header:
            auth_middleware_logger.debug(f"[{request_id}] Authorization header present: {auth_header[:20]}...")
        else:
            auth_middleware_logger.debug(f"[{request_id}] No Authorization header")
        
        # Log cookies
        access_token_cookie = request.COOKIES.get('access_token', '')
        refresh_token_cookie = request.COOKIES.get('refresh_token', '')
        
        if access_token_cookie:
            auth_middleware_logger.debug(f"[{request_id}] Access token cookie present: {access_token_cookie[:20]}...")
        if refresh_token_cookie:
            auth_middleware_logger.debug(f"[{request_id}] Refresh token cookie present: {refresh_token_cookie[:20]}...")
        
        # Log user agent and IP
        user_agent = request.META.get('HTTP_USER_AGENT', '')[:100]
        remote_addr = request.META.get('REMOTE_ADDR', '')
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR', '')
        
        auth_middleware_logger.debug(f"[{request_id}] User-Agent: {user_agent}")
        auth_middleware_logger.debug(f"[{request_id}] Remote-Addr: {remote_addr}")
        if x_forwarded_for:
            auth_middleware_logger.debug(f"[{request_id}] X-Forwarded-For: {x_forwarded_for}")
        
        # Attempt JWT authentication if token is present
        if auth_header.startswith('Bearer '):
            self._test_jwt_authentication(request, request_id)
        
        return None
    
    def _test_jwt_authentication(self, request, request_id):
        """Test JWT authentication and log detailed results"""
        try:
            auth_middleware_logger.debug(f"[{request_id}] Testing JWT authentication")
            
            # Extract token
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if not auth_header.startswith('Bearer '):
                auth_middleware_logger.warning(f"[{request_id}] Invalid auth header format")
                return
            
            token = auth_header.split(' ')[1]
            auth_middleware_logger.debug(f"[{request_id}] Extracted token: {token[:20]}...{token[-10:]}")
            
            # Decode token without verification to check structure
            try:
                unverified_payload = jwt.decode(token, options={"verify_signature": False})
                auth_middleware_logger.debug(f"[{request_id}] Token payload (unverified): {unverified_payload}")
                
                # Check expiration
                exp = unverified_payload.get('exp')
                if exp:
                    exp_time = datetime.fromtimestamp(exp)
                    now = datetime.now()
                    time_until_exp = exp_time - now
                    auth_middleware_logger.debug(f"[{request_id}] Token expires at: {exp_time}")
                    auth_middleware_logger.debug(f"[{request_id}] Time until expiration: {time_until_exp}")
                    
                    if exp_time <= now:
                        auth_middleware_logger.warning(f"[{request_id}] Token is expired!")
                
            except jwt.DecodeError as e:
                auth_middleware_logger.error(f"[{request_id}] Token decode error: {e}")
                return
            
            # Test with JWTAuthentication class
            jwt_authenticator = JWTAuthentication()
            
            # Create a DRF Request object
            drf_request = Request(request)
            
            start_time = time.time()
            result = jwt_authenticator.authenticate(drf_request)
            end_time = time.time()
            
            auth_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            if result:
                user, validated_token = result
                auth_middleware_logger.info(f"[{request_id}] JWT authentication SUCCESS in {auth_time:.2f}ms")
                auth_middleware_logger.debug(f"[{request_id}] Authenticated user: {user.email} (ID: {user.id})")
                auth_middleware_logger.debug(f"[{request_id}] Token type: {type(validated_token)}")
                
                # Store authentication info for later use
                request.auth_middleware_user = user
                request.auth_middleware_token = validated_token
                request.auth_middleware_time = auth_time
                
            else:
                auth_middleware_logger.warning(f"[{request_id}] JWT authentication returned None in {auth_time:.2f}ms")
                
        except InvalidToken as e:
            auth_middleware_logger.error(f"[{request_id}] JWT InvalidToken error: {e}")
        except TokenError as e:
            auth_middleware_logger.error(f"[{request_id}] JWT TokenError: {e}")
        except Exception as e:
            auth_middleware_logger.error(f"[{request_id}] Unexpected JWT authentication error: {e}", exc_info=True)
    
    def process_response(self, request, response):
        """Process response and log authentication results"""
        
        # Skip non-API requests
        if not hasattr(request, 'auth_request_id'):
            return response
        
        request_id = request.auth_request_id
        
        # Log response status
        auth_middleware_logger.debug(f"[{request_id}] Response status: {response.status_code}")
        
        # Check if authentication was successful vs response status
        if hasattr(request, 'auth_middleware_user'):
            if response.status_code == 401:
                auth_middleware_logger.error(
                    f"[{request_id}] AUTHENTICATION MISMATCH: JWT auth succeeded but got 401 response!"
                )
            else:
                auth_time = getattr(request, 'auth_middleware_time', 0)
                auth_middleware_logger.info(
                    f"[{request_id}] Authentication consistent: JWT success, response {response.status_code} (auth time: {auth_time:.2f}ms)"
                )
        else:
            if response.status_code != 401 and request.path.startswith('/api/accounts/'):
                # Check if this is a protected endpoint that should require auth
                protected_endpoints = ['/api/accounts/profile/', '/api/accounts/check-auth/']
                if any(request.path.startswith(endpoint) for endpoint in protected_endpoints):
                    auth_middleware_logger.warning(
                        f"[{request_id}] Possible auth bypass: No JWT auth but got {response.status_code} on protected endpoint"
                    )
        
        # Log response headers that might affect authentication
        if hasattr(response, 'cookies'):
            for cookie_name in ['access_token', 'refresh_token']:
                if cookie_name in response.cookies:
                    cookie_value = response.cookies[cookie_name].value
                    auth_middleware_logger.debug(f"[{request_id}] Response set cookie {cookie_name}: {cookie_value[:20]}...")
        
        return response
    
    def process_exception(self, request, exception):
        """Process exceptions and log authentication-related errors"""
        
        if hasattr(request, 'auth_request_id'):
            request_id = request.auth_request_id
            auth_middleware_logger.error(f"[{request_id}] Exception during request processing: {exception}", exc_info=True)
        
        return None


class AuthenticationDebugMiddleware(MiddlewareMixin):
    """
    Additional middleware for debugging authentication issues in specific scenarios
    """
    
    def process_view(self, request, view_func, view_args, view_kwargs):
        """Process view and log authentication state"""
        
        # Only log for API views
        if not request.path.startswith('/api/'):
            return None
        
        request_id = getattr(request, 'auth_request_id', 'unknown')
        
        # Log Django's authentication state
        auth_middleware_logger.debug(f"[{request_id}] Django user.is_authenticated: {request.user.is_authenticated}")
        if request.user.is_authenticated:
            auth_middleware_logger.debug(f"[{request_id}] Django user: {request.user.email} (ID: {request.user.id})")
        
        # Log view information
        view_name = getattr(view_func, '__name__', 'unknown')
        view_class = getattr(view_func, 'view_class', None)
        if view_class:
            view_name = f"{view_class.__name__}.{view_name}"
        
        auth_middleware_logger.debug(f"[{request_id}] View: {view_name}")
        
        # Check for permission classes
        if hasattr(view_func, 'view_class'):
            permission_classes = getattr(view_func.view_class, 'permission_classes', [])
            auth_middleware_logger.debug(f"[{request_id}] Permission classes: {[p.__name__ for p in permission_classes]}")
        
        return None


# Utility function to enable/disable debug logging
def set_auth_debug_level(level='DEBUG'):
    """Set authentication logging level"""
    if level.upper() == 'DEBUG':
        auth_middleware_logger.setLevel(logging.DEBUG)
    elif level.upper() == 'INFO':
        auth_middleware_logger.setLevel(logging.INFO)
    elif level.upper() == 'WARNING':
        auth_middleware_logger.setLevel(logging.WARNING)
    elif level.upper() == 'ERROR':
        auth_middleware_logger.setLevel(logging.ERROR)
    
    auth_middleware_logger.info(f"Authentication logging level set to: {level.upper()}")


# Function to analyze auth logs
def analyze_auth_logs(log_file_path=None):
    """Analyze authentication logs for patterns"""
    if not log_file_path:
        log_file_path = auth_middleware_log_file
    
    try:
        with open(log_file_path, 'r') as f:
            lines = f.readlines()
        
        # Count different types of events
        auth_successes = 0
        auth_failures = 0
        auth_mismatches = 0
        token_expired = 0
        
        for line in lines:
            if 'JWT authentication SUCCESS' in line:
                auth_successes += 1
            elif 'JWT authentication returned None' in line:
                auth_failures += 1
            elif 'AUTHENTICATION MISMATCH' in line:
                auth_mismatches += 1
            elif 'Token is expired' in line:
                token_expired += 1
        
        print(f"Authentication Log Analysis:")
        print(f"  Total lines: {len(lines)}")
        print(f"  Auth successes: {auth_successes}")
        print(f"  Auth failures: {auth_failures}")
        print(f"  Auth mismatches: {auth_mismatches}")
        print(f"  Expired tokens: {token_expired}")
        
        if auth_mismatches > 0:
            print(f"\n⚠️  Found {auth_mismatches} authentication mismatches - this indicates the bug!")
        
        return {
            'total_lines': len(lines),
            'auth_successes': auth_successes,
            'auth_failures': auth_failures,
            'auth_mismatches': auth_mismatches,
            'token_expired': token_expired
        }
        
    except FileNotFoundError:
        print(f"Log file not found: {log_file_path}")
        return None
    except Exception as e:
        print(f"Error analyzing logs: {e}")
        return None 