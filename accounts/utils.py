import requests
import random
import string
import redis
import logging
from django.conf import settings
from datetime import timed<PERSON>ta
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.contrib.sites.shortcuts import get_current_site
from django.urls import reverse
from django.core.cache import cache

logger = logging.getLogger(__name__)

# Connect to Redis
redis_client = redis.Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    db=settings.REDIS_DB,
    socket_timeout=settings.REDIS_SOCKET_TIMEOUT
)

from .tokens import email_verification_token, password_reset_token


def verify_recaptcha(token):
    """Verify reCAPTCHA token with Google's API"""
    try:
        response = requests.post('https://www.google.com/recaptcha/api/siteverify', {
            'secret': settings.RECAPTCHA_SECRET_KEY,
            'response': token
        })
        result = response.json()
        
        if result['success']:
            return result['score'] >= settings.RECAPTCHA_REQUIRED_SCORE
        return False
    except Exception as e:
        logger.error(f"reCAPTCHA verification failed: {str(e)}")
        return False


def generate_otp(length=6):
    """Generate a numeric OTP of specified length"""
    return ''.join(random.choices(string.digits, k=length))

def store_otp(user_id, otp, expiry_minutes=2):
    """Store OTP in Redis with expiration"""
    key = f"login_otp:{user_id}"
    redis_client.set(key, otp)
    redis_client.expire(key, expiry_minutes * 60)  # Convert minutes to seconds

def verify_otp(user_id, otp):
    """Verify if OTP is valid for the user"""
    key = f"login_otp:{user_id}"
    stored_otp = redis_client.get(key)
    
    if not stored_otp:
        return False
    
    # Convert bytes to string for comparison
    stored_otp = stored_otp.decode('utf-8')
    
    # One-time use: delete after verification attempt
    redis_client.delete(key)
    
    return stored_otp == otp


def send_verification_email(request, user):
    """Send email with OTP verification code to the user"""
    try:
        otp = generate_otp()
        
        store_otp(user.pk, otp)
        
        mail_subject = 'Verify your SEO Analyser account'
        
        message = render_to_string('accounts/email_verification.html', {
            'user': user,
            'otp': otp,
        })
        
        # Send email
        email = EmailMessage(
            mail_subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [user.email],
        )
        email.content_subtype = "html"
        return email.send()
    except Exception as e:
        logger.error(f"Email sending error: {str(e)}")
        return False


def send_password_reset_email(request, user):
    """Send password reset link to the user"""
    current_site = get_current_site(request)
    mail_subject = 'Reset your SEO Analyser password'
    
    # Generate password reset token
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = password_reset_token.make_token(user)
    
    # Create password reset link
    reset_link = f"{request.scheme}://{current_site.domain}{reverse('accounts:reset-password', kwargs={'uidb64': uid, 'token': token})}"
    
    # Render email template
    message = render_to_string('accounts/password_reset_email.html', {
        'user': user,
        'reset_link': reset_link,
    })
    
    # Send email
    email = EmailMessage(
        mail_subject,
        message,
        settings.DEFAULT_FROM_EMAIL,
        [user.email],
    )
    email.content_subtype = "html"
    return email.send()
