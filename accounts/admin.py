from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from django.db.models import ProtectedError
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils.html import format_html
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

# Import Pricing from tools.models for use with SubscriptionAdmin if needed for plan linking logic
# from tools.models import Pricing as ToolsPricing # This line was already commented but there might be another one
from .models import CustomUser, WhiteLabel, PaymentHistory, Subscription # Removed SubscriptionPlan
from .forms import CustomUserAdminForm, WhiteLabelAdminForm


class CustomUserAdmin(BaseUserAdmin):
    """Admin configuration for the CustomUser model"""
    model = CustomUser
    list_display = (
        'email', 'first_name', 'last_name', 'is_staff', 'is_active', 'is_verified',
        'auth_provider', 'google_id', 'stripe_customer_id', 'business_type', 'company_size', 'user_role'
    )
    list_filter = (
        'is_staff', 'is_active', 'is_verified', 'auth_provider', 'date_joined', 'created_at',
        'business_type', 'company_size', 'user_role', 'offers_seo_services'
    )
    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name')}),
        (_('Profile Settings'), {
            'fields': (
                'business_type', 'company_size', 'user_role',
                'offers_seo_services', 'help_areas', 'interested_features'
            )
        }),
        (_('Permissions'), {
            'fields': ('is_active', 'is_verified', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        (_('Authentication Info'), {'fields': ('auth_provider', 'google_id', 'stripe_customer_id')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
                'email', 'password', 'password2', 'first_name', 'last_name',
                'is_staff', 'is_active', 'is_verified', 'auth_provider',
            ),
        }),
    )
    search_fields = ('email', 'first_name', 'last_name', 'google_id', 'stripe_customer_id')
    ordering = ('email',)
    readonly_fields = ('last_login', 'date_joined', 'google_id', 'stripe_customer_id')

    def delete_model(self, request, obj):
        """Handle deletion of a single user"""
        try:
            logger.info(f"Attempting to delete user: {obj.email}")
            super().delete_model(request, obj)
            logger.info(f"Successfully deleted user: {obj.email}")
            messages.success(request, _('User was successfully deleted.'))
        except ProtectedError as e:
            logger.error(f"Protected error when deleting user {obj.email}: {str(e)}")
            messages.error(request, _('Cannot delete user because they have related items that are protected.'))
        except Exception as e:
            logger.error(f"Unexpected error when deleting user {obj.email}: {str(e)}", exc_info=True)
            messages.error(request, _('Failed to delete user. Please try again.'))

    def delete_queryset(self, request, queryset):
        """Handle bulk deletion of users"""
        try:
            emails = list(queryset.values_list('email', flat=True))
            logger.info(f"Attempting to delete users: {emails}")
            super().delete_queryset(request, queryset)
            logger.info(f"Successfully deleted users: {emails}")
            messages.success(request, _('Selected users were successfully deleted.'))
        except ProtectedError as e:
            logger.error(f"Protected error when bulk deleting users: {str(e)}")
            messages.error(request, _('Cannot delete some users because they have related items that are protected.'))
        except Exception as e:
            logger.error(f"Unexpected error when bulk deleting users: {str(e)}", exc_info=True)
            messages.error(request, _('Failed to delete users. Please try again.'))

    def response_delete(self, request, obj_display, obj_id):
        """Override response after deletion"""
        try:
            return super().response_delete(request, obj_display, obj_id)
        except Exception as e:
            logger.error(f"Error in response_delete: {str(e)}", exc_info=True)
            messages.error(request, _('An error occurred while processing the deletion.'))
            return HttpResponseRedirect(reverse('admin:accounts_customuser_changelist'))

    def has_delete_permission(self, request, obj=None):
        """Check if user has delete permission"""
        try:
            has_perm = super().has_delete_permission(request, obj)
            logger.info(f"Delete permission check for user {request.user}: {has_perm}")
            return has_perm
        except Exception as e:
            logger.error(f"Error checking delete permission: {str(e)}", exc_info=True)
            return False


admin.site.register(CustomUser, CustomUserAdmin)

@admin.register(WhiteLabel)
class WhiteLabelAdmin(admin.ModelAdmin):
    list_display = ('user', 'brand_name', 'display_logo', 'phone_number', 'website', 'created_at')
    search_fields = ('user__email', 'brand_name', 'phone_number', 'website')
    readonly_fields = ('created_at', 'updated_at', 'display_logo')
    list_filter = ('created_at', 'updated_at')

    def display_logo(self, obj):
        if obj.logo and hasattr(obj.logo, 'url'):
            logo_url = obj.logo.url
            return format_html('<img src="{}" style="max-width:100px; max-height:100px;" /><br/><a href="{}" target="_blank">{}</a>',
                             logo_url, logo_url, obj.logo.name)
        return "-"
    display_logo.short_description = _('Logo Preview')

@admin.register(PaymentHistory)
class PaymentHistoryAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'user_email',
        'gateway_transaction_id', 
        'gateway_charge_id',
        'gateway_subscription_id',
        'amount', 
        'currency', 
        'status', 
        'payment_gateway', 
        'paid_at', 
        'created_at',
        'task_id',
        'error_message_summary',
    )
    list_filter = ('status', 'payment_gateway', 'created_at', 'paid_at', 'user__email')
    search_fields = (
        'id',
        'user__email', 
        'gateway_transaction_id', 
        'gateway_charge_id',
        'gateway_subscription_id', 
        'task_id',
        'error_message'
    )
    readonly_fields = ('id', 'created_at', 'updated_at', 'paid_at', 'gateway_specific_details_pretty')
    fieldsets = (
        ('Payment Information', {
            'fields': ('user', 'gateway_transaction_id', 'gateway_charge_id', 'gateway_subscription_id', 'task_id')
        }),
        ('Amount & Status', {
            'fields': ('amount', 'currency', 'status', 'payment_gateway')
        }),
        ('Details & Timestamps', {
            'fields': ('gateway_specific_details_pretty', 'error_message', 'paid_at', 'created_at', 'updated_at')
        }),
    )
    ordering = ('-created_at',)
    raw_id_fields = ('user',)

    def user_email(self, obj):
        return obj.user.email if obj.user else None
    user_email.short_description = 'User Email'
    user_email.admin_order_field = 'user__email'

    def error_message_summary(self, obj):
        if obj.error_message:
            return (obj.error_message[:75] + '...') if len(obj.error_message) > 75 else obj.error_message
        return None
    error_message_summary.short_description = 'Error Message'

    def gateway_specific_details_pretty(self, obj):
        import json
        if obj.gateway_specific_details:
            return format_html("<pre>{}</pre>", json.dumps(obj.gateway_specific_details, indent=2))
        return None
    gateway_specific_details_pretty.short_description = 'Gateway Details (Formatted)'

@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'user_email',
        'stripe_subscription_id',
        'status',
        'created_at',
        'updated_at'
    )
    list_filter = ('status', 'created_at')
    search_fields = (
        'user__email',
        'stripe_subscription_id',
    )
    raw_id_fields = ('user',)
    readonly_fields = (
        'id',
        'created_at',
        'updated_at',
    )
    fieldsets = (
        (None, {
            'fields': ('id', 'user', 'stripe_subscription_id')
        }),
        (_('Status & Period'), {
            'fields': (
                'status', 
            )
        }),
        (_('Timestamps & Metadata'), {
            'fields': ('metadata', 'created_at', 'updated_at')
        }),
    )

    def user_email(self, obj):
        return obj.user.email
    user_email.short_description = _('User Email')
