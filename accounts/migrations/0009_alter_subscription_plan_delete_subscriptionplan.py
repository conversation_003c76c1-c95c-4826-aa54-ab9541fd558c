# Generated by Django 5.2 on 2025-05-13 15:32

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0008_subscription_alter_paymenthistory_options_and_more'),
        ('tools', '0008_update_discount_field'),
    ]

    operations = [
        migrations.AlterField(
            model_name='subscription',
            name='plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subscriptions', to='tools.pricing'),
        ),
        migrations.DeleteModel(
            name='SubscriptionPlan',
        ),
    ]
