# Generated by Django 5.2 on 2025-05-23 23:20

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0016_remove_paymenthistory_subscription_and_more'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='subscription',
            name='accounts_su_current_b666c1_idx',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='cancel_at_period_end',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='canceled_at',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='current_period_end',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='current_period_start',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='ended_at',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='trial_end',
        ),
        migrations.RemoveField(
            model_name='subscription',
            name='trial_start',
        ),
    ]
