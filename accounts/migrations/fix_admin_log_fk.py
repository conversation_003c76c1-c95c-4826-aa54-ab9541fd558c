from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('accounts', '0001_initial'),  # Replace with your last migration
        ('admin', '0003_logentry_add_action_flag_choices'),  # This is Django's admin migration
    ]

    operations = [
        migrations.RunSQL(
            # Forward SQL - Update the foreign key to point to our custom user table
            sql="""
            ALTER TABLE django_admin_log
            DROP CONSTRAINT IF EXISTS django_admin_log_user_id_c564eba6_fk_auth_user_id;
            
            ALTER TABLE django_admin_log
            ADD CONSTRAINT django_admin_log_user_id_fk
            FOREIGN KEY (user_id) REFERENCES accounts_customuser(id)
            ON DELETE CASCADE;
            """,
            # Reverse SQL - Restore the original foreign key if needed
            reverse_sql="""
            ALTER TABLE django_admin_log
            DROP CONSTRAINT IF EXISTS django_admin_log_user_id_fk;
            
            ALTER TABLE django_admin_log
            ADD CONSTRAINT django_admin_log_user_id_c564eba6_fk_auth_user_id
            FOREIGN KEY (user_id) REFERENCES auth_user(id)
            ON DELETE CASCADE;
            """
        ),
    ]