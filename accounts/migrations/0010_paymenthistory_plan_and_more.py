# Generated by Django 5.2 on 2025-05-14 06:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0009_alter_subscription_plan_delete_subscriptionplan'),
        ('tools', '0008_update_discount_field'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymenthistory',
            name='plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_histories_for_plan', to='tools.pricing'),
        ),
        migrations.AddField(
            model_name='paymenthistory',
            name='subscription_record',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_histories', to='accounts.subscription'),
        ),
    ]
