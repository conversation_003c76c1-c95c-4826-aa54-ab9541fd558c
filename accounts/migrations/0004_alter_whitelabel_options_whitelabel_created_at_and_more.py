# Generated by Django 5.2 on 2025-05-13 07:11

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0003_paymenthistory_task_id_alter_paymenthistory_currency_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='whitelabel',
            options={'verbose_name': 'White Label Setting', 'verbose_name_plural': 'White Label Settings'},
        ),
        migrations.AddField(
            model_name='whitelabel',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='whitelabel',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='whitelabel',
            name='logo',
            field=models.TextField(help_text='Base64 encoded logo data'),
        ),
    ]
