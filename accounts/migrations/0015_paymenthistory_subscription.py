# Generated by Django 5.2 on 2025-05-23 22:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0014_customuser_business_type_customuser_company_size_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymenthistory',
            name='subscription',
            field=models.ForeignKey(blank=True, help_text='The local subscription this payment is associated with, if any.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_histories', to='accounts.subscription'),
        ),
    ]
