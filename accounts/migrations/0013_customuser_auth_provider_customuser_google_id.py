# Generated by Django 5.2 on 2025-05-17 07:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0012_customuser_stripe_customer_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='auth_provider',
            field=models.CharField(choices=[('email', 'Email'), ('google', 'Google')], default='email', help_text='The authentication provider used to register the account.', max_length=50),
        ),
        migrations.AddField(
            model_name='customuser',
            name='google_id',
            field=models.CharField(blank=True, db_index=True, help_text='Google User ID', max_length=255, null=True, unique=True),
        ),
    ]
