# Generated by Django 5.2 on 2025-05-19 11:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0013_customuser_auth_provider_customuser_google_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='business_type',
            field=models.CharField(blank=True, choices=[('digital_agency', 'Digital Agency'), ('saas', 'SaaS'), ('freelance', 'Freelance'), ('it_services', 'IT Services'), ('franchise', 'Franchise'), ('e_commerce', 'E-Commerce'), ('multi_location', 'Multi-location business'), ('other', 'Other')], help_text='Which of these describes your business best?', max_length=50, null=True, verbose_name='business type'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='company_size',
            field=models.CharField(blank=True, choices=[('just_me', 'Just me'), ('2_10', '2–10'), ('11_50', '11–50'), ('51_200', '51–200'), ('201_500', '201–500'), ('500_plus', '500+')], help_text="What's the size of your company or team?", max_length=50, null=True, verbose_name='company size'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='help_areas',
            field=models.JSONField(blank=True, default=list, help_text='What areas do you want help with? (Multi-select)', null=True, verbose_name='help areas'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='interested_features',
            field=models.JSONField(blank=True, default=list, help_text='Which features are you most interested in using? (multi-select)', null=True, verbose_name='interested features'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='offers_seo_services',
            field=models.CharField(blank=True, choices=[('agency', 'Yes, I run an SEO agency'), ('part_of_service', "Yes, but it's part of a larger service offering"), ('own_websites_only', "No, I'm working on my own websites only")], help_text='Do you offer SEO services to clients?', max_length=50, null=True, verbose_name='offers seo services'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='user_role',
            field=models.CharField(blank=True, choices=[('head_of_seo', 'Head of SEO'), ('marketing_manager', 'Marketing Manager'), ('owner_vp', 'Owner/VP'), ('developer', 'Developer'), ('seo_specialist', 'SEO Specialist'), ('content_manager', 'Content Manager'), ('analyst', 'Analyst'), ('other', 'Other')], help_text="What's your role?", max_length=50, null=True, verbose_name='user role'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='auth_provider',
            field=models.CharField(choices=[('email', 'Email'), ('google', 'Google')], default='email', help_text='The authentication provider used to register the account.', max_length=50, verbose_name='authentication provider'),
        ),
    ]
