from django.db import migrations, models
import uuid

def gen_uuid(apps, schema_editor):
    PaymentHistory = apps.get_model('accounts', 'PaymentHistory')
    for row in PaymentHistory.objects.all():
        row.new_id = uuid.uuid4()
        row.save(update_fields=['new_id'])

class Migration(migrations.Migration):
    dependencies = [
        ('accounts', '0005_alter_whitelabel_logo'),
    ]

    operations = [
        # 1. Add a new UUID field that allows NULL
        migrations.AddField(
            model_name='paymenthistory',
            name='new_id',
            field=models.UUIDField(null=True, blank=True),
        ),

        # 2. Generate UUIDs for existing rows
        migrations.RunPython(gen_uuid),

        # 3. Make the new UUID field NOT NULL
        migrations.AlterField(
            model_name='paymenthistory',
            name='new_id',
            field=models.UUIDField(default=uuid.uuid4, unique=True, null=False),
        ),

        # 4. Remove the old AutoField primary key
        migrations.RemoveField(
            model_name='paymenthistory',
            name='id',
        ),

        # 5. Rename the new_id field to id and make it the primary key
        migrations.RenameField(
            model_name='paymenthistory',
            old_name='new_id',
            new_name='id',
        ),
        migrations.AlterField(
            model_name='paymenthistory',
            name='id',
            field=models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False),
        ),
    ] 