# Generated by Django 5.2 on 2025-05-12 08:35

import accounts.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', 'fix_admin_log_fk'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gateway_transaction_id', models.CharField(help_text='Transaction ID from the payment gateway', max_length=255, unique=True)),
                ('gateway_charge_id', models.CharField(blank=True, help_text='Charge ID from the payment gateway, if applicable', max_length=255, null=True)),
                ('gateway_subscription_id', models.CharField(blank=True, help_text='Subscription ID from the payment gateway, if applicable', max_length=255, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='aud', max_length=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('requires_action', 'Requires Action')], default='pending', max_length=20)),
                ('payment_gateway', models.CharField(choices=[('stripe', 'Stripe'), ('paypal', 'PayPal')], default='stripe', max_length=50)),
                ('gateway_specific_details', models.JSONField(blank=True, default=dict, help_text='Raw response or specific details from the gateway')),
                ('error_message', models.TextField(blank=True, null=True)),
                ('paid_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_histories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Payment Histories',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WhiteLabel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('brand_name', models.CharField(max_length=255)),
                ('logo', models.ImageField(upload_to='whitelabel_logos/', validators=[accounts.validators.validate_logo])),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='whitelabel_settings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
