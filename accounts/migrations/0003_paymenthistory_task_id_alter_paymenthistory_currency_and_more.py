# Generated by Django 5.2 on 2025-05-12 13:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_subscriptionplan'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymenthistory',
            name='task_id',
            field=models.CharField(blank=True, db_index=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='paymenthistory',
            name='currency',
            field=models.CharField(default='usd', max_length=10),
        ),
        migrations.AlterField(
            model_name='paymenthistory',
            name='gateway_subscription_id',
            field=models.CharField(blank=True, db_index=True, help_text='Subscription ID from the payment gateway, if applicable', max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='paymenthistory',
            name='gateway_transaction_id',
            field=models.CharField(blank=True, help_text='Transaction/Session ID from the payment gateway', max_length=255, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='paymenthistory',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('succeeded', 'Succeeded'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded'), ('requires_action', 'Requires Action')], db_index=True, default='pending', max_length=20),
        ),
    ]
