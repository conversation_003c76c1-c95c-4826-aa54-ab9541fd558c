# Generated by Django 5.2 on 2025-05-12 11:05

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_paymenthistory_whitelabel'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('stripe_price_id', models.CharField(max_length=100, unique=True)),
                ('stripe_product_id', models.CharField(max_length=100, unique=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(default='USD', max_length=3)),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('interval', models.CharField(choices=[('month', 'Monthly'), ('year', 'Yearly')], default='month', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('features', models.JSONField(default=dict)),
                ('is_active', models.BooleanField(default=True)),
                ('trial_days', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['price'],
                'indexes': [models.Index(fields=['is_active'], name='accounts_su_is_acti_c44b39_idx'), models.Index(fields=['stripe_price_id'], name='accounts_su_stripe__c19545_idx'), models.Index(fields=['stripe_product_id'], name='accounts_su_stripe__cf7323_idx')],
            },
        ),
    ]
