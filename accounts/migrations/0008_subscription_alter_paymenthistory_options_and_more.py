# Generated by Django 5.2 on 2025-05-13 15:12

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0007_payment_history_uuid_migration'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('stripe_subscription_id', models.CharField(db_index=True, help_text='Stripe Subscription ID', max_length=255, unique=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('trialing', 'Trialing'), ('past_due', 'Past Due'), ('canceled', 'Canceled'), ('unpaid', 'Unpaid'), ('incomplete', 'Incomplete'), ('incomplete_expired', 'Incomplete Expired'), ('paused', 'Paused')], db_index=True, max_length=30)),
                ('current_period_start', models.DateTimeField(blank=True, help_text='Start of the current billing period', null=True)),
                ('current_period_end', models.DateTimeField(blank=True, help_text='End of the current billing period (expiration date)', null=True)),
                ('cancel_at_period_end', models.BooleanField(default=False, help_text='True if the subscription is set to cancel at the end of the current period')),
                ('trial_start', models.DateTimeField(blank=True, null=True)),
                ('trial_end', models.DateTimeField(blank=True, null=True)),
                ('ended_at', models.DateTimeField(blank=True, help_text='Timestamp when the subscription truly ended (e.g., after cancellation or non-payment)', null=True)),
                ('canceled_at', models.DateTimeField(blank=True, help_text='Timestamp when a cancellation was requested', null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Additional metadata from Stripe or internal notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Subscription',
                'verbose_name_plural': 'Subscriptions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AlterModelOptions(
            name='paymenthistory',
            options={'ordering': ['-created_at'], 'verbose_name': 'Payment History', 'verbose_name_plural': 'Payment Histories'},
        ),
        migrations.AlterField(
            model_name='paymenthistory',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False),
        ),
        migrations.AddIndex(
            model_name='paymenthistory',
            index=models.Index(fields=['user', 'status'], name='accounts_pa_user_id_ea1b56_idx'),
        ),
        migrations.AddIndex(
            model_name='paymenthistory',
            index=models.Index(fields=['created_at'], name='accounts_pa_created_1db8d2_idx'),
        ),
        migrations.AddField(
            model_name='subscription',
            name='plan',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subscriptions', to='accounts.subscriptionplan'),
        ),
        migrations.AddField(
            model_name='subscription',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['user', 'status'], name='accounts_su_user_id_17dcf3_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['current_period_end'], name='accounts_su_current_b666c1_idx'),
        ),
    ]
