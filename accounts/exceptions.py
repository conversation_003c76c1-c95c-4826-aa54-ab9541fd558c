from rest_framework.exceptions import APIException
from rest_framework import status
from django.utils.translation import gettext_lazy as _

class EmailVerificationError(APIException):
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = _('Failed to send verification email.')
    default_code = 'email_verification_error'

class RecaptchaVerificationError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = _('Failed to verify reCAPTCHA.')
    default_code = 'recaptcha_verification_error'

class OAuth2Error(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = _('OAuth2 authentication failed.')
    default_code = 'oauth2_error'

class UserAccountError(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = _('User account operation failed.')
    default_code = 'user_account_error'