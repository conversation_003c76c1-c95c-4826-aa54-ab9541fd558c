import json
import logging
from django.http import JsonResponse, HttpResponse
from rest_framework import status
from django.urls import resolve
from .exceptions import EmailVerificationError, RecaptchaVerificationError, OAuth2Error, UserAccountError

logger = logging.getLogger(__name__)

class AccountErrorHandlerMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        return self.get_response(request)

    def process_exception(self, request, exception):
        # Check if this is an admin request
        if request.path.startswith('/admin/'):
            logger.error(f"Admin error processing request {request.path}: {str(exception)}", exc_info=True)
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': str(exception),
                    'detail': 'Admin operation failed'
                }, status=500)
            return None  # Let Django's admin handle the error

        if isinstance(exception, (EmailVerificationError, RecaptchaVerificationError, OAuth2Error, UserAccountError)):
            return JsonResponse(
                {'error': str(exception)},
                status=exception.status_code
            )
        
        # Log unexpected errors
        logger.error(f"Unexpected error processing request {request.path}: {str(exception)}", exc_info=True)
        return JsonResponse(
            {'error': 'An unexpected error occurred. Please try again later.'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
