import requests
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

GOOGLE_VERIFY_URL = 'https://www.google.com/recaptcha/api/siteverify'

def verify_recaptcha(token):
    if not token:
        logger.warning("reCAPTCHA token missing.")
        return False

    secret_key = settings.RECAPTCHA_SECRET_KEY
    required_score = settings.RECAPTCHA_REQUIRED_SCORE

    try:
        response = requests.post(GOOGLE_VERIFY_URL, data={
            'secret': secret_key,
            'response': token
        })
        response.raise_for_status()
        result = response.json()

        logger.debug(f"reCAPTCHA verification result: {result}")

        if result.get('success') and result.get('score', 0) >= required_score:
            return True
        else:
            logger.warning(f"reCAPTCHA verification failed or score too low. Result: {result}")
            return False

    except requests.exceptions.RequestException as e:
        logger.error(f"Error verifying reCAPTCHA token: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during reCAPTCHA verification: {e}")
        return False 