from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.core.mail import send_mail
from django.conf import settings
import bleach
import logging

from .serializers import ContactFormSerializer
from .utils import verify_recaptcha

logger = logging.getLogger(__name__)

# Create your views here.

class ContactFormAPIView(APIView):
    permission_classes = []
    authentication_classes = []

    def post(self, request, *args, **kwargs):
        serializer = ContactFormSerializer(data=request.data)

        if serializer.is_valid():
            recaptcha_token = serializer.validated_data.get('recaptcha_token')

            if not verify_recaptcha(recaptcha_token):
                logger.warning(f"reCAPTCHA verification failed for request from {request.META.get('REMOTE_ADDR')}")
                return Response({"error": "reCAPTCHA verification failed."}, status=status.HTTP_400_BAD_REQUEST)

            subject = bleach.clean(serializer.validated_data.get('subject'), strip=True)
            user_email = bleach.clean(serializer.validated_data.get('email'), strip=True)
            allowed_tags = ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li']
            message_text = bleach.clean(serializer.validated_data.get('text'), tags=allowed_tags, strip=True)

            email_subject = f"Contact Form Submission: {subject}"
            email_body = f"""
            You received a new message from the contact form:

            From: {user_email}
            Subject: {subject}

            Message:
            {message_text}
            """
            sender_email = settings.DEFAULT_FROM_EMAIL
            recipient_list = [settings.CONTACT_EMAIL]

            try:
                send_mail(email_subject, email_body, sender_email, recipient_list, fail_silently=False)
                logger.info(f"Contact form submitted successfully by {user_email}")
                return Response({"message": "Your message has been sent successfully!"}, status=status.HTTP_200_OK)
            except Exception as e:
                logger.error(f"Failed to send contact form email: {e}")
                return Response({"error": "There was an error sending your message. Please try again later."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            logger.warning(f"Contact form validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
