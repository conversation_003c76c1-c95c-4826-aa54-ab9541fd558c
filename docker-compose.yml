services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.15.1
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.http.ssl.enabled=false
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - 9200:9200
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - elastic-net
    restart: always

volumes:
  elasticsearch-data:

networks:
  elastic-net:
    driver: bridge